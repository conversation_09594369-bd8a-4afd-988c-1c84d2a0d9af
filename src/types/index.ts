// Types pour l'application Halal Invest

export interface UserProfile {
  id: string
  email: string
  fullName?: string
  riskTolerance: 'conservative' | 'moderate' | 'aggressive'
  investmentHorizon: 'short' | 'medium' | 'long'
  monthlyBudget: number
  shariaPurityLevel: number // 95-100
  boycottPreferences: string[]

  // Nouvelles données d'onboarding
  hasInvestedBefore: boolean
  isCurrentlyInvesting: boolean
  currentInvestmentAmount?: number
  monthlyIncome?: number
  monthlySavings?: number
  investmentGoals: string[]
  age?: number
  profession?: string
  onboardingCompleted: boolean

  createdAt: string
  updatedAt: string
}

export interface Stock {
  id: string
  symbol: string
  name: string
  sector: string
  isShariCompliant: boolean
  shariaKingSince?: string // Date depuis laquelle c'est un Sharia King
  debtRatio: number
  nonHalalRevenueRatio: number
  liquidityRatio: number
  currentPrice?: number
  lastChecked: string
  createdAt: string
  updatedAt: string
}

export interface PortfolioAllocation {
  symbol: string
  name: string
  percentage: number
  amount: number
  isShariKing?: boolean
}

export interface Portfolio {
  id: string
  userId: string
  name: string
  totalAmount: number
  allocations: PortfolioAllocation[]
  createdAt: string
  updatedAt: string
}

export interface Notification {
  id: string
  userId: string
  type: 'sharia_compliance_change' | 'portfolio_update' | 'new_recommendation'
  title: string
  message: string
  isRead: boolean
  createdAt: string
}

export interface OnboardingQuestion {
  id: string
  type: 'single-choice' | 'multiple-choice' | 'slider' | 'input' | 'currency'
  title: string
  subtitle?: string
  question: string
  options?: {
    value: string | number
    label: string
    description?: string
    icon?: string
  }[]
  min?: number
  max?: number
  step?: number
  required?: boolean
  validation?: (value: any) => boolean | string
}

export interface OnboardingStep {
  id: string
  title: string
  description: string
  icon: string
  questions: OnboardingQuestion[]
  progress: number
}

export interface OnboardingData {
  // Profil personnel
  age?: number
  profession?: string
  monthlyIncome?: number
  monthlySavings?: number

  // Expérience d'investissement
  hasInvestedBefore: boolean
  isCurrentlyInvesting: boolean
  currentInvestmentAmount?: number
  investmentExperience: 'beginner' | 'intermediate' | 'advanced'

  // Objectifs et préférences
  investmentGoals: string[]
  riskTolerance: 'conservative' | 'moderate' | 'aggressive'
  investmentHorizon: 'short' | 'medium' | 'long'
  monthlyBudget: number

  // Préférences Sharia
  shariaPurityLevel: number
  boycottPreferences: string[]
  shariaKnowledge: 'beginner' | 'intermediate' | 'advanced'
}

export interface ShariaScreeningCriteria {
  maxDebtRatio: number // 33%
  maxNonHalalRevenue: number // 5%
  maxLiquidityRatio: number // 33%
  excludedSectors: string[]
}

export interface FinancialData {
  symbol: string
  price: number
  marketCap: number
  totalDebt: number
  totalRevenue: number
  nonHalalRevenue: number
  cashAndEquivalents: number
  lastUpdated: string
}

// Types pour Yahoo Finance API
export interface YahooFinanceQuote {
  symbol: string
  regularMarketPrice: number
  regularMarketChange: number
  regularMarketChangePercent: number
  regularMarketVolume: number
  marketCap: number
  trailingPE?: number
  forwardPE?: number
  dividendYield?: number
  fiftyTwoWeekLow: number
  fiftyTwoWeekHigh: number
  currency: string
  exchangeName: string
  longName: string
  shortName: string
  sector?: string
  industry?: string
}

export interface YahooFinanceHistoricalData {
  date: Date
  open: number
  high: number
  low: number
  close: number
  adjClose: number
  volume: number
}

export interface YahooFinancialStats {
  symbol: string
  totalDebt?: number
  totalCash?: number
  totalRevenue?: number
  grossProfits?: number
  operatingCashflow?: number
  freeCashflow?: number
  returnOnEquity?: number
  returnOnAssets?: number
  debtToEquity?: number
  currentRatio?: number
  quickRatio?: number
  lastUpdated: string
}

export interface CachedFinancialData {
  data: YahooFinanceQuote | YahooFinancialStats | YahooFinanceHistoricalData[]
  timestamp: number
  ttl: number
}

// Types pour la gestion des portefeuilles utilisateur
export interface UserWatchlist {
  id: string
  userId: string
  symbol: string
  addedAt: string
  notes?: string
  targetPrice?: number
  alertEnabled: boolean
}

export interface UserPortfolio {
  id: string
  userId: string
  name: string
  description?: string
  totalInvested: number
  targetAmount?: number
  riskTolerance: 'conservative' | 'moderate' | 'aggressive'
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface UserPortfolioPosition {
  id: string
  portfolioId: string
  symbol: string
  shares: number
  averagePrice: number
  totalInvested: number
  targetAllocationPercent?: number
  addedAt: string
  lastUpdated: string
}

export interface UserRecommendation {
  id: string
  userId: string
  symbol: string
  recommendationType: 'buy' | 'sell' | 'hold' | 'watch'
  reason: string
  confidenceScore: number
  targetPrice?: number
  targetAllocationPercent?: number
  isShariaCompliant: boolean
  expiresAt?: string
  isRead: boolean
  createdAt: string
}

export interface UserTransaction {
  id: string
  userId: string
  portfolioId?: string
  symbol: string
  transactionType: 'buy' | 'sell' | 'dividend'
  shares: number
  pricePerShare: number
  totalAmount: number
  fees: number
  notes?: string
  transactionDate: string
  createdAt: string
}

export interface UserAlert {
  id: string
  userId: string
  symbol: string
  alertType: 'price_above' | 'price_below' | 'sharia_change' | 'recommendation'
  targetValue?: number
  isActive: boolean
  isTriggered: boolean
  triggeredAt?: string
  message?: string
  createdAt: string
}

export interface PortfolioSummary {
  portfolioId: string
  userId: string
  portfolioName: string
  investedAmount: number
  positionsCount: number
  currentValue: number
  totalCost: number
  unrealizedGainLoss: number
  gainLossPercent: number
  shariaCompliantCount: number
  shariaKingsCount: number
  createdAt: string
  updatedAt: string
}

export interface PortfolioValue {
  totalValue: number
  totalInvested: number
  totalGainLoss: number
  gainLossPercent: number
}

export interface EnrichedPortfolioPosition extends UserPortfolioPosition {
  stockInfo?: Stock
  currentPrice?: number
  currentValue?: number
  gainLoss?: number
  gainLossPercent?: number
  allocationPercent?: number
}

export interface PortfolioRecommendation {
  allocations: PortfolioAllocation[]
  totalAmount: number
  riskScore: number
  shariaCompliance: number
  expectedReturn: number
  reasoning: string
}

// Constantes pour les secteurs exclus (Sharia)
export const EXCLUDED_SECTORS = [
  'Alcohol',
  'Tobacco',
  'Gambling',
  'Conventional Banking',
  'Conventional Insurance',
  'Weapons & Defense',
  'Adult Entertainment',
  'Pork Products'
] as const

// Entreprises spécifiquement exclues pour contenu illicite ou modèle d'affaires non-halal
export const EXCLUDED_COMPANIES = [
  'GOOGL', // Alphabet - Revenus publicitaires incluant contenu illicite
  'GOOG',  // Alphabet Class A
  'META',  // Meta - Revenus publicitaires incluant contenu illicite
  'FB',    // Meta (ancien symbole)
  'SNAP',  // Snapchat - Contenu inapproprié
  'TWTR',  // Twitter (ancien) - Contenu inapproprié
  'X',     // Twitter/X - Contenu inapproprié
  'PINS',  // Pinterest - Contenu potentiellement inapproprié
  'ROKU',  // Roku - Streaming avec contenu illicite
  'NFLX',  // Netflix - Contenu illicite (films/séries)
  'DIS',   // Disney - Contenu potentiellement inapproprié
  'SPOT',  // Spotify - Musique avec contenu illicite
] as const

// Constantes pour les entreprises couramment boycottées
export const COMMON_BOYCOTT_COMPANIES = [
  'Coca-Cola',
  'PepsiCo',
  'Nestlé',
  'McDonald\'s',
  'Starbucks',
  'KFC',
  'Burger King',
  'Pizza Hut'
] as const

export type ExcludedSector = typeof EXCLUDED_SECTORS[number]
export type ExcludedCompany = typeof EXCLUDED_COMPANIES[number]
export type BoycottCompany = typeof COMMON_BOYCOTT_COMPANIES[number]
