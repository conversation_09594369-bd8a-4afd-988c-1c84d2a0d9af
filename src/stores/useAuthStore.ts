import { create } from 'zustand'
import { User } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'
import { UserProfile } from '@/types'

interface AuthState {
  user: User | null
  profile: UserProfile | null
  loading: boolean
  hasRLSError: boolean
  signIn: (email: string, password: string) => Promise<void>
  signUp: (email: string, password: string, fullName?: string) => Promise<void>
  signInWithGoogle: () => Promise<void>
  signOut: () => Promise<void>
  updateProfile: (profile: Partial<UserProfile>) => Promise<void>
  fetchProfile: () => Promise<void>
  setUser: (user: User | null) => void
  createProfile: (user: User, fullName?: string) => Promise<void>
  setRLSError: (hasError: boolean) => void
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  profile: null,
  loading: true,
  hasRLSError: false,

  signIn: async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (error) throw error

    if (data.user) {
      set({ user: data.user })
      await get().fetchProfile()
    }
  },

  signUp: async (email: string, password: string, fullName?: string) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName || null,
        }
      }
    })

    if (error) throw error

    if (data.user) {
      set({ user: data.user })
      // Le profil sera créé automatiquement par le trigger
      // Mais on peut aussi le créer manuellement si le trigger ne fonctionne pas
      await get().createProfile(data.user, fullName)
      await get().fetchProfile()
      // Note: La redirection vers /onboarding sera gérée dans la page register
    }
  },

  signInWithGoogle: async () => {
    const { error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/auth/callback`
      }
    })

    if (error) throw error
  },

  createProfile: async (user: User, fullName?: string) => {
    try {
      // Vérifier si le profil existe déjà
      const { data: existingProfile, error: selectError } = await supabase
        .from('profiles')
        .select('id')
        .eq('id', user.id)
        .single()

      // Si la table n'existe pas, afficher un message d'erreur
      if (selectError && selectError.message.includes('does not exist')) {
        console.error('❌ Tables non créées. Veuillez exécuter le script SQL dans Supabase Dashboard.')
        return
      }

      if (!existingProfile && !selectError) {
        // Créer le profil s'il n'existe pas
        const { error: profileError } = await supabase
          .from('profiles')
          .insert({
            id: user.id,
            email: user.email!,
            full_name: fullName || user.user_metadata?.full_name || null,
            risk_tolerance: 'moderate',
            investment_horizon: 'medium',
            monthly_budget: 1000,
            sharia_purity_level: 98,
            boycott_preferences: [],
            has_invested_before: false,
            is_currently_investing: false,
            investment_goals: [],
            onboarding_completed: false,
          })

        if (profileError) {
          console.error('❌ Erreur création profil:', profileError.message)
          // Ne pas throw l'erreur pour éviter de bloquer l'authentification
        } else {
          console.log('✅ Profil créé avec succès')
        }
      }
    } catch (error) {
      console.error('❌ Erreur dans createProfile:', error)
    }
  },

  signOut: async () => {
    const { error } = await supabase.auth.signOut()
    if (error) throw error
    
    set({ user: null, profile: null })
  },

  updateProfile: async (profileUpdate: Partial<UserProfile>) => {
    const { user } = get()
    if (!user) throw new Error('No user logged in')

    // Préparer les données pour la base de données
    const updateData: any = {
      updated_at: new Date().toISOString(),
    }

    // Ajouter seulement les champs qui sont définis
    if (profileUpdate.fullName !== undefined) updateData.full_name = profileUpdate.fullName
    if (profileUpdate.riskTolerance !== undefined) updateData.risk_tolerance = profileUpdate.riskTolerance
    if (profileUpdate.investmentHorizon !== undefined) updateData.investment_horizon = profileUpdate.investmentHorizon
    if (profileUpdate.monthlyBudget !== undefined) updateData.monthly_budget = profileUpdate.monthlyBudget
    if (profileUpdate.shariaPurityLevel !== undefined) updateData.sharia_purity_level = profileUpdate.shariaPurityLevel
    if (profileUpdate.boycottPreferences !== undefined) updateData.boycott_preferences = profileUpdate.boycottPreferences

    // Nouvelles colonnes d'onboarding
    if (profileUpdate.age !== undefined) updateData.age = profileUpdate.age
    if (profileUpdate.profession !== undefined) updateData.profession = profileUpdate.profession
    if (profileUpdate.monthlyIncome !== undefined) updateData.monthly_income = profileUpdate.monthlyIncome
    if (profileUpdate.monthlySavings !== undefined) updateData.monthly_savings = profileUpdate.monthlySavings
    if (profileUpdate.hasInvestedBefore !== undefined) updateData.has_invested_before = profileUpdate.hasInvestedBefore
    if (profileUpdate.isCurrentlyInvesting !== undefined) updateData.is_currently_investing = profileUpdate.isCurrentlyInvesting
    if (profileUpdate.currentInvestmentAmount !== undefined) updateData.current_investment_amount = profileUpdate.currentInvestmentAmount
    if (profileUpdate.investmentGoals !== undefined) updateData.investment_goals = profileUpdate.investmentGoals
    if (profileUpdate.onboardingCompleted !== undefined) updateData.onboarding_completed = profileUpdate.onboardingCompleted

    const { data, error } = await supabase
      .from('profiles')
      .update(updateData)
      .eq('id', user.id)
      .select()
      .single()

    if (error) throw error

    // Convertir les données de la DB vers le format de l'app
    const updatedProfile: UserProfile = {
      id: data.id,
      email: data.email,
      fullName: data.full_name || undefined,
      riskTolerance: data.risk_tolerance,
      investmentHorizon: data.investment_horizon,
      monthlyBudget: data.monthly_budget,
      shariaPurityLevel: data.sharia_purity_level,
      boycottPreferences: data.boycott_preferences || [],
      hasInvestedBefore: data.has_invested_before || false,
      isCurrentlyInvesting: data.is_currently_investing || false,
      currentInvestmentAmount: data.current_investment_amount,
      monthlyIncome: data.monthly_income,
      monthlySavings: data.monthly_savings,
      investmentGoals: data.investment_goals || [],
      age: data.age,
      profession: data.profession,
      onboardingCompleted: data.onboarding_completed || false,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
    }

    set({ profile: updatedProfile })
  },

  fetchProfile: async () => {
    const { user } = get()
    if (!user) return

    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    if (error) {
      // Détecter les erreurs 406 (RLS)
      if (error.message.includes('406') || error.message.includes('Not Acceptable')) {
        console.error('🚨 Erreur 406: Problème avec les politiques RLS. Exécuter fix-rls-policies.sql')
        set({ hasRLSError: true })
        return
      }

      // Si la table n'existe pas, afficher un message d'erreur
      if (error.message.includes('does not exist')) {
        console.error('❌ Tables non créées. Veuillez exécuter le script SQL dans Supabase Dashboard.')
        return
      }

      // Si le profil n'existe pas, le créer
      if (error.code === 'PGRST116') {
        console.log('🔄 Profil non trouvé, création...')
        await get().createProfile(user)
        return
      }

      console.error('❌ Erreur récupération profil:', error.message, error)
      return
    }

    // Convertir les données de la DB vers le format de l'app
    const profile: UserProfile = {
      id: data.id,
      email: data.email,
      fullName: data.full_name || undefined,
      riskTolerance: data.risk_tolerance,
      investmentHorizon: data.investment_horizon,
      monthlyBudget: data.monthly_budget,
      shariaPurityLevel: data.sharia_purity_level,
      boycottPreferences: data.boycott_preferences || [],
      hasInvestedBefore: data.has_invested_before || false,
      isCurrentlyInvesting: data.is_currently_investing || false,
      currentInvestmentAmount: data.current_investment_amount,
      monthlyIncome: data.monthly_income,
      monthlySavings: data.monthly_savings,
      investmentGoals: data.investment_goals || [],
      age: data.age,
      profession: data.profession,
      onboardingCompleted: data.onboarding_completed || false,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
    }

    set({ profile })
    console.log('✅ Profil récupéré avec succès')
  },

  setUser: (user: User | null) => {
    set({ user, loading: false })
    if (user) {
      // Créer le profil si nécessaire (pour les connexions Google)
      get().createProfile(user)
      get().fetchProfile()
    } else {
      set({ profile: null })
    }
  },

  setRLSError: (hasError: boolean) => {
    set({ hasRLSError: hasError })
  },
}))

// Initialiser l'écoute des changements d'auth
supabase.auth.onAuthStateChange((_event, session) => {
  useAuthStore.getState().setUser(session?.user ?? null)
})
