import { create } from 'zustand'
import { Portfolio, PortfolioRecommendation, Stock, EXCLUDED_COMPANIES } from '@/types'
import { supabase } from '@/lib/supabase'

interface PortfolioState {
  portfolios: Portfolio[]
  currentRecommendation: PortfolioRecommendation | null
  availableStocks: Stock[]
  loading: boolean
  realTimePrices: Record<string, number>
  lastPriceUpdate: Date | null
  generateRecommendation: (
    amount: number,
    riskTolerance: string,
    purityLevel: number,
    boycottList: string[]
  ) => Promise<PortfolioRecommendation>
  savePortfolio: (portfolio: Omit<Portfolio, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>
  fetchPortfolios: (userId: string) => Promise<void>
  fetchAvailableStocks: () => Promise<void>
  updateStockCompliance: () => Promise<void>
  updateRealTimePrices: (symbols: string[]) => Promise<void>
  syncStockData: (symbols: string[]) => Promise<void>
  preloadPopularStocks: () => Promise<void>
}

export const usePortfolioStore = create<PortfolioState>((set, get) => ({
  portfolios: [],
  currentRecommendation: null,
  availableStocks: [],
  loading: false,
  realTimePrices: {},
  lastPriceUpdate: null,

  generateRecommendation: async (
    amount: number,
    riskTolerance: string,
    purityLevel: number,
    boycottList: string[]
  ) => {
    set({ loading: true })
    
    try {
      // Récupérer les actions halal disponibles
      const { data: stocks, error } = await supabase
        .from('stocks')
        .select('*')
        .eq('is_sharia_compliant', true)
        .not('symbol', 'in', `(${boycottList.map(s => `"${s}"`).join(',')})`)

      if (error) throw error

      // Algorithme simple de répartition basé sur le profil de risque
      const filteredStocks = stocks.filter(stock => {
        // Exclure les entreprises spécifiquement interdites
        if (EXCLUDED_COMPANIES.includes(stock.symbol as any)) {
          return false
        }

        // Filtrer selon le niveau de pureté
        if (purityLevel >= 99) {
          // Très strict : seulement les Sharia Kings
          return stock.sharia_king_since !== null
        } else if (purityLevel >= 97) {
          // Strict : ratios très conservateurs
          return stock.debt_ratio <= 25 && stock.non_halal_revenue_ratio <= 3
        } else {
          // Standard : critères AAOIFI normaux
          return stock.debt_ratio <= 33 && stock.non_halal_revenue_ratio <= 5
        }
      })

      // Répartition selon le profil de risque
      let allocations = []
      
      if (riskTolerance === 'conservative') {
        // Privilégier les Sharia Kings et les grandes caps
        const shariaKings = filteredStocks.filter(s => s.sharia_king_since !== null)
        allocations = distributeConservative(shariaKings.slice(0, 8), amount)
      } else if (riskTolerance === 'moderate') {
        // Mix équilibré
        allocations = distributeModerate(filteredStocks.slice(0, 12), amount)
      } else {
        // Plus de diversification, inclure des small caps
        allocations = distributeAggressive(filteredStocks.slice(0, 15), amount)
      }

      const recommendation: PortfolioRecommendation = {
        allocations,
        totalAmount: amount,
        riskScore: getRiskScore(riskTolerance),
        shariaCompliance: purityLevel,
        expectedReturn: getExpectedReturn(riskTolerance),
        reasoning: generateReasoning(riskTolerance, purityLevel, allocations.length)
      }

      set({ currentRecommendation: recommendation, loading: false })
      return recommendation

    } catch (error) {
      console.error('Error generating recommendation:', error)
      set({ loading: false })
      throw error
    }
  },

  savePortfolio: async (portfolio) => {
    const { data, error } = await supabase
      .from('portfolios')
      .insert({
        user_id: portfolio.userId,
        name: portfolio.name,
        total_amount: portfolio.totalAmount,
        allocations: portfolio.allocations,
      })
      .select()
      .single()

    if (error) throw error

    // Convertir et ajouter à la liste
    const newPortfolio: Portfolio = {
      id: data.id,
      userId: data.user_id,
      name: data.name,
      totalAmount: data.total_amount,
      allocations: data.allocations,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
    }

    set(state => ({
      portfolios: [...state.portfolios, newPortfolio]
    }))
  },

  fetchPortfolios: async (userId: string) => {
    const { data, error } = await supabase
      .from('portfolios')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) throw error

    const portfolios: Portfolio[] = data.map(p => ({
      id: p.id,
      userId: p.user_id,
      name: p.name,
      totalAmount: p.total_amount,
      allocations: p.allocations,
      createdAt: p.created_at,
      updatedAt: p.updated_at,
    }))

    set({ portfolios })
  },

  fetchAvailableStocks: async () => {
    const { data, error } = await supabase
      .from('stocks_with_realtime_data')
      .select('*')
      .eq('is_sharia_compliant', true)
      .order('symbol')

    if (error) throw error

    const stocks: Stock[] = data.map(s => ({
      id: s.id,
      symbol: s.symbol,
      name: s.name,
      sector: s.sector,
      isShariCompliant: s.is_sharia_compliant,
      shariaKingSince: s.sharia_king_since || undefined,
      debtRatio: s.debt_ratio,
      nonHalalRevenueRatio: s.non_halal_revenue_ratio,
      liquidityRatio: s.liquidity_ratio,
      currentPrice: s.current_price,
      lastChecked: s.last_checked,
      createdAt: s.created_at,
      updatedAt: s.updated_at,
    }))

    set({ availableStocks: stocks })
  },

  updateStockCompliance: async () => {
    try {
      const response = await fetch('/api/finance/sync', {
        method: 'PUT'
      })

      if (!response.ok) {
        throw new Error('Failed to update Sharia compliance')
      }

      const result = await response.json()
      console.log('Sharia compliance updated:', result.data.updatedCount, 'stocks')

      // Rafraîchir les actions disponibles
      await get().fetchAvailableStocks()
    } catch (error) {
      console.error('Error updating Sharia compliance:', error)
      throw error
    }
  },

  updateRealTimePrices: async (symbols: string[]) => {
    try {
      if (symbols.length === 0) return

      const symbolsParam = symbols.join(',')
      const response = await fetch(`/api/finance/quote?symbols=${symbolsParam}`)

      if (!response.ok) {
        throw new Error('Failed to fetch real-time prices')
      }

      const result = await response.json()
      const prices: Record<string, number> = {}

      result.data.forEach((quote: any) => {
        prices[quote.symbol] = quote.regularMarketPrice
      })

      set({
        realTimePrices: { ...get().realTimePrices, ...prices },
        lastPriceUpdate: new Date()
      })

    } catch (error) {
      console.error('Error updating real-time prices:', error)
      throw error
    }
  },

  syncStockData: async (symbols: string[]) => {
    try {
      set({ loading: true })

      const response = await fetch('/api/finance/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ symbols })
      })

      if (!response.ok) {
        throw new Error('Failed to sync stock data')
      }

      const result = await response.json()
      console.log('Stock data synced:', result.data)

      // Rafraîchir les données après la synchronisation
      await Promise.all([
        get().fetchAvailableStocks(),
        get().updateRealTimePrices(symbols)
      ])

    } catch (error) {
      console.error('Error syncing stock data:', error)
      throw error
    } finally {
      set({ loading: false })
    }
  },

  preloadPopularStocks: async () => {
    try {
      // Récupérer la liste des symboles populaires
      const popularResponse = await fetch('/api/finance/preload')
      if (!popularResponse.ok) {
        throw new Error('Failed to get popular symbols')
      }

      const popularResult = await popularResponse.json()
      const symbols = popularResult.data.popularSymbols

      // Précharger les données
      const preloadResponse = await fetch('/api/finance/preload', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ symbols })
      })

      if (!preloadResponse.ok) {
        throw new Error('Failed to preload data')
      }

      const preloadResult = await preloadResponse.json()
      console.log('Popular stocks preloaded:', preloadResult.data)

    } catch (error) {
      console.error('Error preloading popular stocks:', error)
      throw error
    }
  }
}))

// Fonctions utilitaires pour la répartition
function distributeConservative(stocks: any[], amount: number) {
  // Répartition conservatrice : 60% sur les 3 premiers, 40% sur les autres
  const allocations = []
  const topStocks = stocks.slice(0, 3)
  const otherStocks = stocks.slice(3, 8)
  
  topStocks.forEach((stock, index) => {
    const percentage = index === 0 ? 25 : 17.5 // 25%, 17.5%, 17.5%
    allocations.push({
      symbol: stock.symbol,
      name: stock.name,
      percentage,
      amount: Math.round((amount * percentage) / 100),
      isShariKing: stock.sharia_king_since !== null
    })
  })
  
  otherStocks.forEach(stock => {
    const percentage = 8 // 8% chacun
    allocations.push({
      symbol: stock.symbol,
      name: stock.name,
      percentage,
      amount: Math.round((amount * percentage) / 100),
      isShariKing: stock.sharia_king_since !== null
    })
  })
  
  return allocations
}

function distributeModerate(stocks: any[], amount: number) {
  // Répartition équilibrée
  const basePercentage = 100 / stocks.length
  return stocks.map(stock => ({
    symbol: stock.symbol,
    name: stock.name,
    percentage: Math.round(basePercentage * 10) / 10,
    amount: Math.round((amount * basePercentage) / 100),
    isShariKing: stock.sharia_king_since !== null
  }))
}

function distributeAggressive(stocks: any[], amount: number) {
  // Répartition plus diversifiée avec des poids variables
  const weights = stocks.map((_, index) => Math.max(1, 15 - index))
  const totalWeight = weights.reduce((sum, w) => sum + w, 0)
  
  return stocks.map((stock, index) => {
    const percentage = Math.round((weights[index] / totalWeight) * 100 * 10) / 10
    return {
      symbol: stock.symbol,
      name: stock.name,
      percentage,
      amount: Math.round((amount * percentage) / 100),
      isShariKing: stock.sharia_king_since !== null
    }
  })
}

function getRiskScore(riskTolerance: string): number {
  switch (riskTolerance) {
    case 'conservative': return 3
    case 'moderate': return 5
    case 'aggressive': return 8
    default: return 5
  }
}

function getExpectedReturn(riskTolerance: string): number {
  switch (riskTolerance) {
    case 'conservative': return 6.5
    case 'moderate': return 8.2
    case 'aggressive': return 10.8
    default: return 8.2
  }
}

function generateReasoning(riskTolerance: string, purityLevel: number, stockCount: number): string {
  let reasoning = `Portefeuille ${riskTolerance === 'conservative' ? 'conservateur' : riskTolerance === 'moderate' ? 'équilibré' : 'dynamique'} `
  reasoning += `avec ${stockCount} actions sélectionnées selon vos critères de pureté (${purityLevel}%). `
  
  if (purityLevel >= 99) {
    reasoning += 'Priorité donnée aux "Sharia Kings" pour une stabilité maximale.'
  } else if (purityLevel >= 97) {
    reasoning += 'Critères stricts appliqués pour une conformité renforcée.'
  } else {
    reasoning += 'Critères AAOIFI standards respectés.'
  }
  
  return reasoning
}
