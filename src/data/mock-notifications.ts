import { Notification } from '@/components/notifications/NotificationBanner'

/**
 * Données de test pour les notifications
 */

export const mockNotifications: Notification[] = [
  {
    id: 'notif-1',
    type: 'sharia-alert',
    title: 'Changement de statut Sharia',
    message: 'L\'action XYZ Corp a été reclassée comme non-halal suite à une augmentation de ses revenus d\'intérêts.',
    actionLabel: 'Voir les détails',
    actionUrl: '/portfolio',
    isRead: false,
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // Il y a 2 heures
    priority: 'high',
    category: 'sharia'
  },
  {
    id: 'notif-2',
    type: 'success',
    title: 'Portefeuille mis à jour',
    message: 'Votre portefeuille a été automatiquement rééquilibré selon vos préférences.',
    actionLabel: 'Voir le portefeuille',
    actionUrl: '/portfolio',
    isRead: false,
    createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // Il y a 4 heures
    priority: 'medium',
    category: 'portfolio'
  },
  {
    id: 'notif-3',
    type: 'info',
    title: 'Nouveau Sharia King',
    message: 'Apple Inc. (AAPL) est maintenant certifié "Sharia King" - halal depuis plus de 10 ans !',
    actionLabel: 'En savoir plus',
    isRead: false,
    createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // Il y a 6 heures
    priority: 'low',
    category: 'sharia'
  },
  {
    id: 'notif-4',
    type: 'warning',
    title: 'Ratio de dette élevé',
    message: 'Microsoft Corp présente un ratio de dette de 31%, proche de la limite Sharia de 33%.',
    actionLabel: 'Surveiller',
    actionUrl: '/portfolio',
    isRead: false,
    createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(), // Il y a 8 heures
    priority: 'medium',
    category: 'sharia'
  },
  {
    id: 'notif-5',
    type: 'info',
    title: 'Rapport mensuel disponible',
    message: 'Votre rapport de performance mensuel est prêt à être consulté.',
    actionLabel: 'Télécharger',
    isRead: true, // Déjà lu
    createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // Il y a 1 jour
    priority: 'low',
    category: 'portfolio'
  },
  {
    id: 'notif-6',
    type: 'error',
    title: 'Erreur de synchronisation',
    message: 'Impossible de mettre à jour les prix en temps réel. Dernière mise à jour il y a 2 heures.',
    actionLabel: 'Réessayer',
    isRead: false,
    createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // Il y a 30 minutes
    priority: 'high',
    category: 'system'
  },
  {
    id: 'notif-7',
    type: 'success',
    title: 'Investissement automatique effectué',
    message: 'Votre investissement mensuel de 1000€ a été réparti selon votre stratégie.',
    actionLabel: 'Voir les détails',
    actionUrl: '/portfolio',
    isRead: false,
    createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(), // Il y a 12 heures
    priority: 'medium',
    category: 'portfolio'
  }
]

/**
 * Simule la récupération des notifications depuis une API
 */
export async function fetchMockNotifications(): Promise<Notification[]> {
  // Simuler un délai d'API
  await new Promise(resolve => setTimeout(resolve, 300))
  
  return mockNotifications
}

/**
 * Simule l'ajout d'une nouvelle notification
 */
export function createMockNotification(type: 'market-update' | 'sharia-change' | 'portfolio-rebalance'): Notification {
  const templates = {
    'market-update': {
      type: 'info' as const,
      title: 'Mise à jour du marché',
      message: 'Les marchés ont clôturé en hausse de 2.3% aujourd\'hui.',
      category: 'market' as const,
      priority: 'low' as const
    },
    'sharia-change': {
      type: 'sharia-alert' as const,
      title: 'Alerte conformité Sharia',
      message: 'Une entreprise de votre portefeuille nécessite une vérification.',
      category: 'sharia' as const,
      priority: 'high' as const,
      actionLabel: 'Vérifier maintenant'
    },
    'portfolio-rebalance': {
      type: 'success' as const,
      title: 'Rééquilibrage terminé',
      message: 'Votre portefeuille a été rééquilibré avec succès.',
      category: 'portfolio' as const,
      priority: 'medium' as const,
      actionLabel: 'Voir les changements'
    }
  }

  const template = templates[type]
  
  return {
    id: Math.random().toString(36).substr(2, 9),
    ...template,
    isRead: false,
    createdAt: new Date().toISOString()
  }
}

/**
 * Génère des notifications de test pour différents scénarios
 */
export function generateTestNotifications(): Notification[] {
  const now = new Date()
  
  return [
    // Notification urgente récente
    {
      id: 'test-urgent',
      type: 'sharia-alert',
      title: 'Action non-halal détectée',
      message: 'Tesla Inc. a été automatiquement retirée de votre portefeuille suite à un changement de classification Sharia.',
      actionLabel: 'Voir les alternatives',
      actionUrl: '/portfolio-generator',
      isRead: false,
      createdAt: new Date(now.getTime() - 5 * 60 * 1000).toISOString(), // Il y a 5 minutes
      priority: 'high',
      category: 'sharia'
    },
    
    // Notification de succès
    {
      id: 'test-success',
      type: 'success',
      title: 'Objectif atteint !',
      message: 'Félicitations ! Votre portefeuille a atteint +15% de performance cette année.',
      actionLabel: 'Voir les détails',
      actionUrl: '/portfolio',
      isRead: false,
      createdAt: new Date(now.getTime() - 60 * 60 * 1000).toISOString(), // Il y a 1 heure
      priority: 'medium',
      category: 'portfolio'
    },
    
    // Notification d'information
    {
      id: 'test-info',
      type: 'info',
      title: 'Nouvelle fonctionnalité',
      message: 'Découvrez notre nouveau système d\'export PDF pour vos rapports de portefeuille.',
      actionLabel: 'Essayer maintenant',
      actionUrl: '/portfolio',
      isRead: false,
      createdAt: new Date(now.getTime() - 3 * 60 * 60 * 1000).toISOString(), // Il y a 3 heures
      priority: 'low',
      category: 'system'
    }
  ]
}
