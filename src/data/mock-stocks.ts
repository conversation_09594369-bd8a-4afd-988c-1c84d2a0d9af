import { Stock } from '@/types'

/**
 * Données de test pour les actions halal
 * Ces données seraient normalement récupérées via une API financière
 */

export const mockShariaCompliantStocks: Stock[] = [
  // Sharia Kings - Entreprises halal depuis 10+ ans
  {
    id: 'aapl',
    symbol: 'AAPL',
    name: 'Apple Inc.',
    sector: 'Technology',
    isShariCompliant: true,
    shariaKingSince: '2010-01-01',
    debtRatio: 0.25, // 25% - Bon
    nonHalalRevenueRatio: 0.02, // 2% - Excellent
    liquidityRatio: 0.15, // 15% - Bon
    currentPrice: 175.50,
    lastChecked: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'msft',
    symbol: 'MSFT',
    name: 'Microsoft Corporation',
    sector: 'Technology',
    isShariCompliant: true,
    shariaKingSince: '2012-01-01',
    debtRatio: 0.18,
    nonHalalRevenueRatio: 0.01,
    liquidityRatio: 0.22,
    currentPrice: 420.30,
    lastChecked: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'googl',
    symbol: 'GOOGL',
    name: 'Alphabet Inc.',
    sector: 'Technology',
    isShariCompliant: true,
    shariaKingSince: '2013-01-01',
    debtRatio: 0.08,
    nonHalalRevenueRatio: 0.03,
    liquidityRatio: 0.28,
    currentPrice: 165.80,
    lastChecked: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'nvda',
    symbol: 'NVDA',
    name: 'NVIDIA Corporation',
    sector: 'Technology',
    isShariCompliant: true,
    shariaKingSince: '2014-01-01',
    debtRatio: 0.12,
    nonHalalRevenueRatio: 0.00,
    liquidityRatio: 0.18,
    currentPrice: 875.20,
    lastChecked: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },

  // Actions halal récentes (non Sharia Kings)
  {
    id: 'tsla',
    symbol: 'TSLA',
    name: 'Tesla Inc.',
    sector: 'Automotive',
    isShariCompliant: true,
    debtRatio: 0.22,
    nonHalalRevenueRatio: 0.01,
    liquidityRatio: 0.16,
    currentPrice: 248.50,
    lastChecked: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'amzn',
    symbol: 'AMZN',
    name: 'Amazon.com Inc.',
    sector: 'E-commerce',
    isShariCompliant: true,
    debtRatio: 0.28,
    nonHalalRevenueRatio: 0.04,
    liquidityRatio: 0.25,
    currentPrice: 185.90,
    lastChecked: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'meta',
    symbol: 'META',
    name: 'Meta Platforms Inc.',
    sector: 'Technology',
    isShariCompliant: true,
    debtRatio: 0.05,
    nonHalalRevenueRatio: 0.02,
    liquidityRatio: 0.32,
    currentPrice: 485.75,
    lastChecked: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },

  // Secteur Santé
  {
    id: 'jnj',
    symbol: 'JNJ',
    name: 'Johnson & Johnson',
    sector: 'Healthcare',
    isShariCompliant: true,
    shariaKingSince: '2011-01-01',
    debtRatio: 0.19,
    nonHalalRevenueRatio: 0.00,
    liquidityRatio: 0.14,
    currentPrice: 155.20,
    lastChecked: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'pfizer',
    symbol: 'PFE',
    name: 'Pfizer Inc.',
    sector: 'Healthcare',
    isShariCompliant: true,
    debtRatio: 0.31,
    nonHalalRevenueRatio: 0.00,
    liquidityRatio: 0.21,
    currentPrice: 28.45,
    lastChecked: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },

  // Secteur Industriel
  {
    id: 'cat',
    symbol: 'CAT',
    name: 'Caterpillar Inc.',
    sector: 'Industrial',
    isShariCompliant: true,
    debtRatio: 0.29,
    nonHalalRevenueRatio: 0.00,
    liquidityRatio: 0.12,
    currentPrice: 385.60,
    lastChecked: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'hon',
    symbol: 'HON',
    name: 'Honeywell International Inc.',
    sector: 'Industrial',
    isShariCompliant: true,
    debtRatio: 0.26,
    nonHalalRevenueRatio: 0.03,
    liquidityRatio: 0.08,
    currentPrice: 215.30,
    lastChecked: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },

  // Secteur Biens de consommation
  {
    id: 'pg',
    symbol: 'PG',
    name: 'Procter & Gamble Co.',
    sector: 'Consumer Goods',
    isShariCompliant: true,
    shariaKingSince: '2010-01-01',
    debtRatio: 0.24,
    nonHalalRevenueRatio: 0.00,
    liquidityRatio: 0.11,
    currentPrice: 165.40,
    lastChecked: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'ko',
    symbol: 'KO',
    name: 'The Coca-Cola Company',
    sector: 'Beverages',
    isShariCompliant: true,
    debtRatio: 0.32,
    nonHalalRevenueRatio: 0.00,
    liquidityRatio: 0.09,
    currentPrice: 62.85,
    lastChecked: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },

  // Secteur Énergie renouvelable
  {
    id: 'enph',
    symbol: 'ENPH',
    name: 'Enphase Energy Inc.',
    sector: 'Renewable Energy',
    isShariCompliant: true,
    debtRatio: 0.15,
    nonHalalRevenueRatio: 0.00,
    liquidityRatio: 0.25,
    currentPrice: 125.70,
    lastChecked: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },

  // Actions avec ratios limites (pour tester les filtres)
  {
    id: 'limit1',
    symbol: 'LIMIT1',
    name: 'Borderline Company 1',
    sector: 'Technology',
    isShariCompliant: true,
    debtRatio: 0.32, // Proche de la limite 33%
    nonHalalRevenueRatio: 0.04, // Proche de la limite 5%
    liquidityRatio: 0.31, // Proche de la limite 33%
    currentPrice: 100.00,
    lastChecked: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
]

// Actions non-halal pour les tests (ne devraient pas apparaître dans les portefeuilles)
export const mockNonShariaStocks: Stock[] = [
  {
    id: 'jpm',
    symbol: 'JPM',
    name: 'JPMorgan Chase & Co.',
    sector: 'Conventional Banking',
    isShariCompliant: false,
    debtRatio: 0.15,
    nonHalalRevenueRatio: 0.95, // Banque conventionnelle
    liquidityRatio: 0.20,
    currentPrice: 185.50,
    lastChecked: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'bac',
    symbol: 'BAC',
    name: 'Bank of America Corp.',
    sector: 'Conventional Banking',
    isShariCompliant: false,
    debtRatio: 0.12,
    nonHalalRevenueRatio: 0.90,
    liquidityRatio: 0.18,
    currentPrice: 42.30,
    lastChecked: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
]

// Toutes les actions pour les tests
export const allMockStocks: Stock[] = [
  ...mockShariaCompliantStocks,
  ...mockNonShariaStocks
]

/**
 * Simule la récupération d'actions depuis une API
 */
export async function fetchMockStocks(): Promise<Stock[]> {
  // Simuler un délai d'API
  await new Promise(resolve => setTimeout(resolve, 500))
  
  return mockShariaCompliantStocks
}

/**
 * Simule la récupération d'une action spécifique
 */
export async function fetchMockStock(symbol: string): Promise<Stock | null> {
  await new Promise(resolve => setTimeout(resolve, 200))
  
  return allMockStocks.find(stock => stock.symbol === symbol) || null
}
