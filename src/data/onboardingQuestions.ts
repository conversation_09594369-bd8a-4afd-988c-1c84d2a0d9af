import { OnboardingStep } from '@/types'

export const onboardingSteps: OnboardingStep[] = [
  {
    id: 'welcome',
    title: 'Bienvenue !',
    description: 'Commençons par apprendre à vous connaître',
    icon: '👋',
    progress: 10,
    questions: [
      {
        id: 'age',
        type: 'slider',
        title: 'Parlons de vous',
        subtitle: 'Ces informations nous aident à personnaliser vos recommandations',
        question: 'Quel âge avez-vous ?',
        min: 18,
        max: 80,
        step: 1,
        required: true
      },
      {
        id: 'profession',
        type: 'single-choice',
        title: 'Votre profession',
        question: 'Dans quel domaine travaillez-vous ?',
        options: [
          {
            value: 'informatique',
            label: 'Informatique / Tech',
            description: 'Développeur, ingénieur, IT, data...',
            icon: '💻'
          },
          {
            value: 'sante',
            label: 'Santé',
            description: 'M<PERSON><PERSON><PERSON>, infirmier, pharmacien...',
            icon: '🏥'
          },
          {
            value: 'education',
            label: 'Éducation',
            description: 'Enseignant, formateur, chercheur...',
            icon: '🎓'
          },
          {
            value: 'finance',
            label: 'Finance / Banque',
            description: 'Ban<PERSON>er, comptable, analyste...',
            icon: '🏦'
          },
          {
            value: 'commerce',
            label: 'Commerce / Vente',
            description: 'Commercial, marketing, retail...',
            icon: '🛍️'
          },
          {
            value: 'ingenierie',
            label: 'Ingénierie',
            description: 'Ingénieur civil, mécanique, électrique...',
            icon: '⚙️'
          },
          {
            value: 'juridique',
            label: 'Juridique',
            description: 'Avocat, juriste, notaire...',
            icon: '⚖️'
          },
          {
            value: 'communication',
            label: 'Communication / Média',
            description: 'Journaliste, graphiste, community manager...',
            icon: '📱'
          },
          {
            value: 'transport',
            label: 'Transport / Logistique',
            description: 'Chauffeur, logisticien, pilote...',
            icon: '🚛'
          },
          {
            value: 'restauration',
            label: 'Restauration / Hôtellerie',
            description: 'Chef, serveur, hôtelier...',
            icon: '🍽️'
          },
          {
            value: 'artisanat',
            label: 'Artisanat / Métiers manuels',
            description: 'Électricien, plombier, menuisier...',
            icon: '🔨'
          },
          {
            value: 'fonction_publique',
            label: 'Fonction publique',
            description: 'Administration, police, pompier...',
            icon: '🏛️'
          },
          {
            value: 'etudiant',
            label: 'Étudiant',
            description: 'En cours d\'études',
            icon: '📚'
          },
          {
            value: 'retraite',
            label: 'Retraité',
            description: 'À la retraite',
            icon: '🌴'
          },
          {
            value: 'sans_emploi',
            label: 'Sans emploi',
            description: 'En recherche d\'emploi',
            icon: '🔍'
          },
          {
            value: 'autre',
            label: 'Autre profession',
            description: 'Je vais préciser dans le champ suivant',
            icon: '✏️'
          }
        ],
        required: true
      },
      {
        id: 'profession_custom',
        type: 'input',
        title: 'Précisez votre profession',
        question: 'Pouvez-vous nous dire plus précisément quel est votre métier ?',
        required: false
      }
    ]
  },
  {
    id: 'financial-situation',
    title: 'Situation financière',
    description: 'Comprenons votre situation actuelle',
    icon: '💰',
    progress: 25,
    questions: [
      {
        id: 'monthlyIncome',
        type: 'currency',
        title: 'Vos revenus',
        subtitle: 'Information confidentielle et sécurisée',
        question: 'Quel est votre revenu mensuel net ?',
        min: 0,
        max: 50000,
        required: true
      },
      {
        id: 'monthlySavings',
        type: 'currency',
        title: 'Votre épargne',
        question: 'Combien pouvez-vous épargner par mois ?',
        min: 0,
        max: 10000,
        required: true
      }
    ]
  },
  {
    id: 'investment-experience',
    title: 'Expérience d\'investissement',
    description: 'Évaluons votre expérience',
    icon: '📈',
    progress: 40,
    questions: [
      {
        id: 'hasInvestedBefore',
        type: 'single-choice',
        title: 'Votre expérience',
        question: 'Avez-vous déjà investi en bourse ?',
        options: [
          {
            value: 'true',
            label: 'Oui, j\'ai déjà investi',
            description: 'J\'ai de l\'expérience avec les investissements',
            icon: '✅'
          },
          {
            value: 'false',
            label: 'Non, je débute',
            description: 'C\'est ma première fois',
            icon: '🌱'
          }
        ],
        required: true
      },
      {
        id: 'investmentExperience',
        type: 'single-choice',
        title: 'Niveau d\'expérience',
        question: 'Comment évaluez-vous votre niveau ?',
        options: [
          {
            value: 'beginner',
            label: 'Débutant',
            description: 'Je découvre le monde de l\'investissement',
            icon: '🌱'
          },
          {
            value: 'intermediate',
            label: 'Intermédiaire',
            description: 'J\'ai quelques connaissances de base',
            icon: '📚'
          },
          {
            value: 'advanced',
            label: 'Avancé',
            description: 'Je maîtrise les concepts d\'investissement',
            icon: '🎓'
          }
        ],
        required: true
      }
    ]
  },
  {
    id: 'current-investments',
    title: 'Investissements actuels',
    description: 'Vos investissements en cours',
    icon: '💼',
    progress: 55,
    questions: [
      {
        id: 'isCurrentlyInvesting',
        type: 'single-choice',
        title: 'Investissements actuels',
        question: 'Investissez-vous actuellement ?',
        options: [
          {
            value: 'true',
            label: 'Oui, j\'investis régulièrement',
            description: 'J\'ai des investissements en cours',
            icon: '📊'
          },
          {
            value: 'false',
            label: 'Non, pas pour le moment',
            description: 'Je n\'investis pas actuellement',
            icon: '💤'
          }
        ],
        required: true
      },
      {
        id: 'currentInvestmentAmount',
        type: 'currency',
        title: 'Montant investi',
        question: 'Quel est le montant total de vos investissements actuels ?',
        min: 0,
        max: 1000000,
        required: false
      }
    ]
  },
  {
    id: 'investment-goals',
    title: 'Objectifs d\'investissement',
    description: 'Définissons vos objectifs',
    icon: '🎯',
    progress: 70,
    questions: [
      {
        id: 'investmentGoals',
        type: 'multiple-choice',
        title: 'Vos objectifs',
        question: 'Quels sont vos objectifs d\'investissement ?',
        options: [
          {
            value: 'retirement',
            label: 'Préparer ma retraite',
            description: 'Constituer un capital pour plus tard',
            icon: '🏖️'
          },
          {
            value: 'house',
            label: 'Acheter un bien immobilier',
            description: 'Épargner pour un achat immobilier',
            icon: '🏠'
          },
          {
            value: 'education',
            label: 'Financer des études',
            description: 'Pour moi ou mes enfants',
            icon: '🎓'
          },
          {
            value: 'travel',
            label: 'Voyager',
            description: 'Financer mes projets de voyage',
            icon: '✈️'
          },
          {
            value: 'emergency',
            label: 'Fonds d\'urgence',
            description: 'Constituer une réserve de sécurité',
            icon: '🛡️'
          },
          {
            value: 'wealth',
            label: 'Faire fructifier mon argent',
            description: 'Augmenter mon patrimoine',
            icon: '💎'
          }
        ],
        required: true
      },
      {
        id: 'investmentHorizon',
        type: 'single-choice',
        title: 'Horizon d\'investissement',
        question: 'Sur quelle durée souhaitez-vous investir ?',
        options: [
          {
            value: 'short',
            label: 'Court terme (1-3 ans)',
            description: 'J\'aurai besoin de cet argent bientôt',
            icon: '⚡'
          },
          {
            value: 'medium',
            label: 'Moyen terme (3-10 ans)',
            description: 'Je peux attendre quelques années',
            icon: '🌳'
          },
          {
            value: 'long',
            label: 'Long terme (10+ ans)',
            description: 'Je pense à très long terme',
            icon: '🌲'
          }
        ],
        required: true
      }
    ]
  },
  {
    id: 'risk-tolerance',
    title: 'Tolérance au risque',
    description: 'Évaluons votre profil de risque',
    icon: '⚖️',
    progress: 85,
    questions: [
      {
        id: 'riskTolerance',
        type: 'single-choice',
        title: 'Votre profil de risque',
        question: 'Comment réagiriez-vous si vos investissements perdaient 20% en un mois ?',
        options: [
          {
            value: 'conservative',
            label: 'Je serais très inquiet',
            description: 'Je préfère la sécurité aux gains élevés',
            icon: '🛡️'
          },
          {
            value: 'moderate',
            label: 'Je serais préoccupé mais patient',
            description: 'J\'accepte un risque modéré pour de meilleurs rendements',
            icon: '⚖️'
          },
          {
            value: 'aggressive',
            label: 'Je resterais confiant',
            description: 'J\'accepte les fluctuations pour maximiser les gains',
            icon: '🚀'
          }
        ],
        required: true
      },
      {
        id: 'monthlyBudget',
        type: 'currency',
        title: 'Budget mensuel',
        question: 'Combien souhaitez-vous investir par mois ?',
        min: 50,
        max: 5000,
        required: true
      }
    ]
  },
  {
    id: 'sharia-preferences',
    title: 'Préférences Sharia',
    description: 'Configurons vos critères halal',
    icon: '🕌',
    progress: 100,
    questions: [
      {
        id: 'shariaKnowledge',
        type: 'single-choice',
        title: 'Connaissance de la finance islamique',
        question: 'Quel est votre niveau de connaissance de la finance islamique ?',
        options: [
          {
            value: 'beginner',
            label: 'Débutant',
            description: 'Je découvre les principes de la finance islamique',
            icon: '🌱'
          },
          {
            value: 'intermediate',
            label: 'Intermédiaire',
            description: 'Je connais les bases',
            icon: '📚'
          },
          {
            value: 'advanced',
            label: 'Avancé',
            description: 'Je maîtrise les concepts Sharia',
            icon: '🎓'
          }
        ],
        required: true
      },
      {
        id: 'shariaPurityLevel',
        type: 'slider',
        title: 'Niveau de pureté',
        subtitle: '95% = Plus d\'opportunités, 100% = Conformité maximale',
        question: 'Quel niveau de pureté Sharia souhaitez-vous ?',
        min: 95,
        max: 100,
        step: 1,
        required: true
      },
      {
        id: 'boycottChoice',
        type: 'single-choice',
        title: 'Préférences de boycott',
        question: 'Souhaitez-vous exclure certaines entreprises de vos investissements ?',
        options: [
          {
            value: 'no_boycott',
            label: 'Aucune exclusion',
            description: 'Je n\'ai pas de préférence particulière',
            icon: '✅'
          },
          {
            value: 'boycott_companies',
            label: 'Exclure certaines entreprises',
            description: 'Je souhaite boycotter des entreprises spécifiques',
            icon: '🚫'
          }
        ],
        required: true
      },
      {
        id: 'boycottPreferences',
        type: 'multiple-choice',
        title: 'Entreprises à exclure',
        question: 'Quelles entreprises souhaitez-vous exclure ?',
        options: [
          {
            value: 'coca-cola',
            label: 'Coca-Cola',
            description: 'Boissons gazeuses',
            icon: '🥤'
          },
          {
            value: 'mcdonalds',
            label: 'McDonald\'s',
            description: 'Restauration rapide',
            icon: '🍔'
          },
          {
            value: 'starbucks',
            label: 'Starbucks',
            description: 'Café et boissons',
            icon: '☕'
          },
          {
            value: 'nestle',
            label: 'Nestlé',
            description: 'Produits alimentaires',
            icon: '🍫'
          },
          {
            value: 'amazon',
            label: 'Amazon',
            description: 'E-commerce et cloud',
            icon: '📦'
          },
          {
            value: 'meta',
            label: 'Meta (Facebook)',
            description: 'Réseaux sociaux',
            icon: '📱'
          }
        ],
        required: true
      }
    ]
  }
]
