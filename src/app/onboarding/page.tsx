'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/stores/useAuthStore'
import { OnboardingData } from '@/types'
import OnboardingFlow from '@/components/onboarding/OnboardingFlow'
import { AnimatedBackground } from '@/components/animated-background'
import { ShaderBackground } from '@/components/shader-background'
import { EnhancedWaves } from '@/components/enhanced-waves'
import { motion } from 'framer-motion'

export default function OnboardingPage() {
  const { user, profile, updateProfile, fetchProfile, loading } = useAuthStore()
  const router = useRouter()
  const [isCompleting, setIsCompleting] = useState(false)

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login')
    }
    
    // Si l'utilisateur a déjà complété l'onboarding, rediriger
    if (profile?.onboardingCompleted) {
      router.push('/dashboard')
    }
  }, [user, profile, loading, router])

  const handleOnboardingComplete = async (data: OnboardingData) => {
    if (!user) return

    setIsCompleting(true)

    try {
      console.log('🚀 Début de la sauvegarde de l\'onboarding...', data)

      // Mettre à jour le profil avec les données d'onboarding
      await updateProfile({
        // Données personnelles
        age: data.age,
        profession: data.profession,
        monthlyIncome: data.monthlyIncome,
        monthlySavings: data.monthlySavings,

        // Expérience d'investissement
        hasInvestedBefore: data.hasInvestedBefore,
        isCurrentlyInvesting: data.isCurrentlyInvesting,
        currentInvestmentAmount: data.currentInvestmentAmount,

        // Objectifs et préférences
        investmentGoals: data.investmentGoals,
        riskTolerance: data.riskTolerance,
        investmentHorizon: data.investmentHorizon,
        monthlyBudget: data.monthlyBudget,

        // Préférences Sharia
        shariaPurityLevel: data.shariaPurityLevel,
        boycottPreferences: data.boycottPreferences,

        // Marquer l'onboarding comme terminé
        onboardingCompleted: true
      })

      console.log('✅ Profil mis à jour avec succès')

      // Recharger le profil pour s'assurer que les données sont à jour
      await fetchProfile()

      console.log('🔄 Profil rechargé, onboardingCompleted:', profile?.onboardingCompleted)

      // Rediriger vers le dashboard avec un message de succès
      console.log('🏠 Redirection vers le dashboard...')
      router.push('/dashboard?onboarding=completed')
    } catch (error) {
      console.error('Erreur lors de la sauvegarde du profil:', error)
      // Afficher une erreur mais permettre quand même de continuer
      router.push('/dashboard?onboarding=error')
    } finally {
      setIsCompleting(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background relative overflow-hidden">
        <EnhancedWaves variant="aurora" intensity="low" />
        <AnimatedBackground variant="particles" />
        <ShaderBackground variant="aurora" intensity="low" />

        <div className="flex items-center justify-center min-h-screen relative z-10">
          <motion.div
            className="flex flex-col items-center space-y-4"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full"
            />
            <p className="text-muted-foreground">Chargement de votre profil...</p>
          </motion.div>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  if (isCompleting) {
    return (
      <div className="min-h-screen bg-background relative overflow-hidden">
        <AnimatedBackground variant="gradient" />
        <ShaderBackground variant="liquid" intensity="medium" />

        <div className="flex items-center justify-center min-h-screen relative z-10">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
            className="text-center bg-card/80 backdrop-blur-sm p-8 rounded-xl border border-border max-w-md"
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full mx-auto mb-6 glow-primary"
            />
            <h2 className="text-2xl font-bold text-foreground mb-3">
              Finalisation de votre profil...
            </h2>
            <p className="text-muted-foreground">
              Nous préparons votre expérience personnalisée selon les principes islamiques
            </p>
          </motion.div>
        </div>
      </div>
    )
  }

  return <OnboardingFlow onComplete={handleOnboardingComplete} />
}
