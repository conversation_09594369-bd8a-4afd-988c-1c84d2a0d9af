"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Slider } from '@/components/ui/slider'
import { Label } from '@/components/ui/label'
import { generatePersonalizedPortfolio, filterShariaCompliantStocks } from '@/lib/portfolio-generator'
import { fetchMockStocks } from '@/data/mock-stocks'
import { UserProfile, Stock, PortfolioAllocation } from '@/types'
import { Crown, TrendingUp, Shield, DollarSign } from 'lucide-react'

export default function PortfolioGeneratorPage() {
  const [stocks, setStocks] = useState<Stock[]>([])
  const [portfolio, setPortfolio] = useState<PortfolioAllocation[]>([])
  const [loading, setLoading] = useState(false)
  const [purityLevel, setPurityLevel] = useState([100])
  const [monthlyBudget, setMonthlyBudget] = useState([1000])
  const [riskTolerance, setRiskTolerance] = useState<'conservative' | 'moderate' | 'aggressive'>('moderate')

  // Profil utilisateur de test
  const testProfile: UserProfile = {
    id: 'test-user',
    riskTolerance,
    investmentHorizon: 'long-term',
    monthlyBudget: monthlyBudget[0],
    shariaPurityLevel: purityLevel[0],
    boycottPreferences: [],
    ethicalPreferences: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }

  useEffect(() => {
    loadStocks()
  }, [])

  const loadStocks = async () => {
    try {
      const fetchedStocks = await fetchMockStocks()
      setStocks(fetchedStocks)
    } catch (error) {
      console.error('Erreur lors du chargement des actions:', error)
    }
  }

  const generatePortfolio = async () => {
    setLoading(true)
    try {
      // Générer le portefeuille avec le profil actuel
      const generatedPortfolio = generatePersonalizedPortfolio(testProfile, stocks)
      setPortfolio(generatedPortfolio)
    } catch (error) {
      console.error('Erreur lors de la génération du portefeuille:', error)
    } finally {
      setLoading(false)
    }
  }

  const totalAmount = portfolio.reduce((sum, allocation) => sum + allocation.amount, 0)
  const shariaKingsCount = portfolio.filter(allocation => allocation.isShariKing).length

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Générateur de Portefeuille Halal</h1>
        <p className="text-muted-foreground">
          Créez votre portefeuille ETF maison personnalisé selon vos critères Sharia
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Configuration */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Configuration</CardTitle>
              <CardDescription>
                Ajustez vos paramètres d'investissement
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Budget mensuel */}
              <div className="space-y-2">
                <Label>Budget mensuel: {monthlyBudget[0]}€</Label>
                <Slider
                  value={monthlyBudget}
                  onValueChange={setMonthlyBudget}
                  max={5000}
                  min={100}
                  step={50}
                  className="w-full"
                />
              </div>

              {/* Niveau de pureté */}
              <div className="space-y-2">
                <Label>Niveau de pureté Sharia: {purityLevel[0]}%</Label>
                <Slider
                  value={purityLevel}
                  onValueChange={setPurityLevel}
                  max={100}
                  min={95}
                  step={1}
                  className="w-full"
                />
                <p className="text-xs text-muted-foreground">
                  95% = critères assouplis, 100% = critères stricts AAOIFI
                </p>
              </div>

              {/* Profil de risque */}
              <div className="space-y-2">
                <Label>Profil de risque</Label>
                <div className="grid grid-cols-1 gap-2">
                  {[
                    { value: 'conservative', label: 'Conservateur', desc: '5-8 actions stables' },
                    { value: 'moderate', label: 'Équilibré', desc: '8-12 actions mixtes' },
                    { value: 'aggressive', label: 'Agressif', desc: '12-20 actions diversifiées' }
                  ].map((option) => (
                    <Button
                      key={option.value}
                      variant={riskTolerance === option.value ? 'default' : 'outline'}
                      onClick={() => setRiskTolerance(option.value as any)}
                      className="justify-start h-auto p-3"
                    >
                      <div className="text-left">
                        <div className="font-medium">{option.label}</div>
                        <div className="text-xs text-muted-foreground">{option.desc}</div>
                      </div>
                    </Button>
                  ))}
                </div>
              </div>

              <Button 
                onClick={generatePortfolio} 
                disabled={loading || stocks.length === 0}
                className="w-full"
              >
                {loading ? 'Génération...' : 'Générer le Portefeuille'}
              </Button>
            </CardContent>
          </Card>

          {/* Statistiques */}
          {portfolio.length > 0 && (
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Statistiques Sharia
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm">Actions sélectionnées</span>
                    <Badge variant="secondary">{portfolio.length}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm flex items-center gap-1">
                      <Crown className="h-3 w-3 text-yellow-500" />
                      Sharia Kings
                    </span>
                    <Badge variant="secondary">{shariaKingsCount}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Niveau de pureté</span>
                    <Badge variant="secondary">{purityLevel[0]}%</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Total alloué</span>
                    <Badge variant="secondary">{totalAmount}€</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Résultats */}
        <div className="lg:col-span-2">
          {portfolio.length === 0 ? (
            <Card>
              <CardContent className="flex items-center justify-center h-64">
                <div className="text-center">
                  <TrendingUp className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">
                    Configurez vos paramètres et générez votre portefeuille halal personnalisé
                  </p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5" />
                    Votre Portefeuille Halal Personnalisé
                  </CardTitle>
                  <CardDescription>
                    Répartition optimisée selon votre profil {riskTolerance === 'conservative' ? 'conservateur' : riskTolerance === 'moderate' ? 'équilibré' : 'agressif'}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {portfolio.map((allocation, index) => (
                      <div
                        key={allocation.symbol}
                        className="flex items-center justify-between p-3 border rounded-lg"
                      >
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center text-sm font-medium">
                            {index + 1}
                          </div>
                          <div>
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{allocation.symbol}</span>
                              {allocation.isShariKing && (
                                <Crown className="h-4 w-4 text-yellow-500" title="Sharia King - Halal depuis 10+ ans" />
                              )}
                            </div>
                            <p className="text-sm text-muted-foreground">{allocation.name}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-medium">{allocation.amount}€</div>
                          <div className="text-sm text-muted-foreground">
                            {allocation.percentage.toFixed(1)}%
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Actions d'export */}
              <Card>
                <CardContent className="pt-6">
                  <div className="flex gap-3">
                    <Button variant="outline" className="flex-1">
                      Exporter en PDF
                    </Button>
                    <Button variant="outline" className="flex-1">
                      Exporter en Excel
                    </Button>
                    <Button className="flex-1">
                      Sauvegarder le Portefeuille
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
