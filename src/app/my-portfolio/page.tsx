'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useUser } from '@supabase/auth-helpers-react'
import UserDashboard from '@/components/UserDashboard'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Loader2, LogIn } from 'lucide-react'

export default function MyPortfolioPage() {
  const user = useUser()
  const router = useRouter()

  // Rediriger vers la connexion si non authentifié
  useEffect(() => {
    if (user === null) {
      router.push('/auth/login')
    }
  }, [user, router])

  // Chargement
  if (user === undefined) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card>
          <CardContent className="p-8 text-center">
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">Chargement...</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Non authentifié
  if (user === null) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card>
          <CardContent className="p-8 text-center">
            <LogIn className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
            <h2 className="text-xl font-semibold mb-2">Connexion requise</h2>
            <p className="text-muted-foreground mb-4">
              Vous devez être connecté pour accéder à votre portefeuille
            </p>
            <Button onClick={() => router.push('/auth/login')}>
              Se connecter
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Utilisateur authentifié
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <UserDashboard />
      </div>
    </div>
  )
}
