'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/stores/useAuthStore'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ThemeToggle } from '@/components/theme-toggle'
import { AnimatedBackground } from '@/components/animated-background'
import { EnhancedWaves } from '@/components/enhanced-waves'
import { checkConfiguration, testGoogleOAuth, showConfigurationGuide } from '@/lib/checkConfig'
import RLSErrorAlert from '@/components/RLSErrorAlert'
import { motion } from 'framer-motion'
import {
  TrendingUp,
  Settings,
  PieChart,
  BarChart3,
  DollarSign,
  Shield,
  Zap,
  Users
} from 'lucide-react'

export default function Dashboard() {
  const { user, profile, loading, signOut, hasRLSError } = useAuthStore()
  const router = useRouter()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login')
    }

    // Rediriger vers l'onboarding si pas encore complété
    if (user && profile && !profile.onboardingCompleted) {
      console.log('❌ Onboarding non complété, redirection...', {
        user: !!user,
        profile: !!profile,
        onboardingCompleted: profile.onboardingCompleted
      })
      router.push('/onboarding')
    } else if (user && profile && profile.onboardingCompleted) {
      console.log('✅ Onboarding complété, affichage du dashboard', {
        user: !!user,
        profile: !!profile,
        onboardingCompleted: profile.onboardingCompleted
      })
    }
  }, [user, profile, loading, router])

  const handleSignOut = async () => {
    try {
      await signOut()
      router.push('/')
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  const handleCheckConfig = async () => {
    await checkConfiguration()
    showConfigurationGuide()
  }

  const handleTestGoogle = async () => {
    await testGoogleOAuth()
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <motion.div
          className="flex flex-col items-center space-y-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary"></div>
          <p className="text-muted-foreground">Chargement de votre dashboard...</p>
        </motion.div>
      </div>
    )
  }

  if (!user) {
    return null // Redirection en cours
  }

  return (
    <div className="min-h-screen bg-background relative overflow-hidden">
      {/* Animations de fond subtiles */}
      <EnhancedWaves variant="flowing" intensity="low" />
      <AnimatedBackground variant="particles" />

      {/* Header */}
      <header className="bg-card/80 backdrop-blur-sm border-b border-border sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <motion.div
              className="flex items-center space-x-2"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
            >
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center glow-primary">
                <span className="text-primary-foreground font-bold text-sm">🕌</span>
              </div>
              <span className="text-xl font-bold text-foreground">Halal Invest</span>
            </motion.div>

            <motion.div
              className="flex items-center space-x-4"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <span className="text-muted-foreground hidden md:block">
                Bienvenue, {profile?.fullName || user.email?.split('@')[0]}
              </span>
              <ThemeToggle />
              <Button variant="outline" onClick={handleSignOut} className="border-glow">
                Déconnexion
              </Button>
            </motion.div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8 relative z-10">
        {/* Alerte RLS si erreur 406 */}
        {hasRLSError && <RLSErrorAlert />}

        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl font-bold text-foreground mb-2">
            Tableau de bord
          </h1>
          <p className="text-xl text-muted-foreground">
            Gérez vos investissements halal et suivez vos portefeuilles en temps réel
          </p>
        </motion.div>

        {/* Stats Cards */}
        <div className="grid md:grid-cols-4 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <Card className="border-border hover:shadow-lg transition-all duration-300 group">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium text-muted-foreground">
                    Profil de risque
                  </CardTitle>
                  <BarChart3 className="w-4 h-4 text-primary group-hover:scale-110 transition-transform" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-foreground">
                  {profile?.riskTolerance === 'conservative' ? 'Conservateur' :
                   profile?.riskTolerance === 'moderate' ? 'Équilibré' :
                   profile?.riskTolerance === 'aggressive' ? 'Dynamique' : 'Non défini'}
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  Horizon: {profile?.investmentHorizon === 'short' ? 'Court terme' :
                           profile?.investmentHorizon === 'medium' ? 'Moyen terme' :
                           profile?.investmentHorizon === 'long' ? 'Long terme' : 'Non défini'}
                </p>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="border-border hover:shadow-lg transition-all duration-300 group">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium text-muted-foreground">
                    Budget mensuel
                  </CardTitle>
                  <DollarSign className="w-4 h-4 text-primary group-hover:scale-110 transition-transform" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-foreground">
                  {profile?.monthlyBudget ? `${profile.monthlyBudget}€` : 'Non défini'}
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  Disponible pour investissement
                </p>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <Card className="border-border hover:shadow-lg transition-all duration-300 group">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium text-muted-foreground">
                    Niveau de pureté
                  </CardTitle>
                  <Shield className="w-4 h-4 text-primary group-hover:scale-110 transition-transform" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-primary">
                  {profile?.shariaPurityLevel || 98}%
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  Conformité Sharia
                </p>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <Card className="border-border hover:shadow-lg transition-all duration-300 group">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium text-muted-foreground">
                    Portefeuilles actifs
                  </CardTitle>
                  <PieChart className="w-4 h-4 text-primary group-hover:scale-110 transition-transform" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-foreground">
                  3
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  En surveillance
                </p>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Quick Actions */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            <Card className="border-border hover:shadow-lg transition-all duration-300 group">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                    <Zap className="w-5 h-5 text-primary" />
                  </div>
                  <div>
                    <CardTitle className="text-foreground">Générer un portefeuille</CardTitle>
                    <CardDescription>
                      Créez un portefeuille personnalisé selon vos critères halal
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Button className="w-full glow-primary group">
                  Commencer la génération
                  <TrendingUp className="ml-2 w-4 h-4 group-hover:scale-110 transition-transform" />
                </Button>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <Card className="border-border hover:shadow-lg transition-all duration-300 group">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                    <Settings className="w-5 h-5 text-primary" />
                  </div>
                  <div>
                    <CardTitle className="text-foreground">Configurer le profil</CardTitle>
                    <CardDescription>
                      Ajustez vos préférences de risque et critères éthiques
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Button variant="outline" className="w-full border-glow">
                  Modifier le profil
                </Button>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.7 }}
          >
            <Card className="border-border hover:shadow-lg transition-all duration-300 group">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                    <Users className="w-5 h-5 text-primary" />
                  </div>
                  <div>
                    <CardTitle className="text-foreground">Communauté</CardTitle>
                    <CardDescription>
                      Rejoignez la communauté des investisseurs musulmans
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Button variant="outline" className="w-full border-glow">
                  Découvrir
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Debug Info (à supprimer en production) */}
        {process.env.NODE_ENV === 'development' && (
          <motion.div
            className="space-y-6 mt-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
          >
            <Card className="border-border bg-card/50">
              <CardHeader>
                <CardTitle className="text-foreground flex items-center">
                  🔧 Outils de développement
                </CardTitle>
                <CardDescription>
                  Outils pour tester et déboguer l'application
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-4">
                  <Button onClick={handleCheckConfig} variant="outline" className="border-glow">
                    Vérifier la configuration
                  </Button>
                  <Button onClick={handleTestGoogle} variant="outline" className="border-glow">
                    Tester Google OAuth
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground">
                  Ouvrir la console du navigateur (F12) pour voir les résultats
                </p>
              </CardContent>
            </Card>

            <Card className="border-border bg-card/50">
              <CardHeader>
                <CardTitle className="text-foreground">Debug Info</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="text-xs bg-muted p-4 rounded overflow-auto text-muted-foreground">
                  <strong>User:</strong> {JSON.stringify(user, null, 2)}
                  <br /><br />
                  <strong>Profile:</strong> {JSON.stringify(profile, null, 2)}
                </pre>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </main>
    </div>
  )
}
