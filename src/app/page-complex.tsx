'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ThemeToggle } from "@/components/theme-toggle"
import { ParticleBackground } from "@/components/particle-background"
import Link from "next/link"
import {
  TrendingUp,
  Shield,
  Users,
  BarChart3,
  CheckCircle,
  Star,
  ArrowRight,
  MSquare as Mosque,
  DollarSign,
  PieChart,
} from "lucide-react"

export default function HalalInvestLanding() {
  return (
    <div className="min-h-screen bg-background relative">
      <ParticleBackground />

      {/* Header */}
      <header className="border-b border-border bg-card/50 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
              <Mosque className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold text-gray-900 dark:text-white">Halal Invest</span>
          </div>
          <nav className="hidden md:flex items-center space-x-6">
            <a href="#features" className="text-muted-foreground hover:text-foreground transition-colors">
              Features
            </a>
            <a href="#how-it-works" className="text-muted-foreground hover:text-foreground transition-colors">
              How It Works
            </a>
            <a href="#sharia-compliance" className="text-muted-foreground hover:text-foreground transition-colors">
              Sharia Compliance
            </a>
            <a href="#pricing" className="text-muted-foreground hover:text-foreground transition-colors">
              Pricing
            </a>
          </nav>
          <div className="flex items-center space-x-4">
            <Link href="/auth/login" className="text-muted-foreground hover:text-foreground transition-colors">
              Se connecter
            </Link>
            <Link href="/auth/register">
              <Button size="sm" className="bg-primary hover:bg-primary/90">
                S'inscrire
              </Button>
            </Link>
            {/* Theme Toggle Button */}
            <ThemeToggle />
            <Button className="bg-primary hover:bg-primary/90 text-primary-foreground glow-green">
              Get Started
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4 bg-gradient-to-br from-muted/30 to-background relative">
        <div className="container mx-auto text-center max-w-4xl">
          <Badge className="mb-6 bg-green-100 text-green-800 border-green-200 dark:bg-green-900 dark:text-green-200 dark:border-green-700">
            🕌 Sharia-Compliant Investment Platform
          </Badge>
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6 text-balance">
            Investissez selon les <span className="text-green-600 dark:text-green-400">Principes Islamiques</span>
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 text-pretty max-w-2xl mx-auto">
            Découvrez des portefeuilles d'investissement halal personnalisés qui respectent les valeurs islamiques.
            Obtenez des conseils conformes à la Sharia avec des filtres éthiques automatisés et un suivi de conformité en temps réel.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/auth/register">
              <Button size="lg" className="bg-green-600 hover:bg-green-700 text-white">
                Commencer maintenant
                <TrendingUp className="w-5 h-5 ml-2" />
              </Button>
            </Link>
            <Link href="/auth/login">
              <Button
                size="lg"
                variant="outline"
                className="border-green-600 text-green-600 hover:bg-green-50 dark:hover:bg-green-900 bg-transparent"
              >
                Se connecter
              </Button>
            </Link>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16">
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-2 text-glow-green">10,000+</div>
              <div className="text-muted-foreground">Muslim Investors</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-2 text-glow-green">500+</div>
              <div className="text-muted-foreground">Halal Stocks Monitored</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-2 text-glow-green">99.8%</div>
              <div className="text-muted-foreground">Sharia Compliance Rate</div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 px-4 relative">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">Why Choose Halal Invest Advisor?</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Advanced technology meets Islamic finance principles to deliver personalized, compliant investment
              solutions.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="border-border hover:shadow-lg transition-shadow border-glow-green">
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 glow-green">
                  <Shield className="w-6 h-6 text-primary" />
                </div>
                <CardTitle>100% Sharia Compliant</CardTitle>
                <CardDescription>
                  All investments are screened using AAOIFI standards with automated compliance monitoring.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-border hover:shadow-lg transition-shadow border-glow-green">
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 glow-green">
                  <PieChart className="w-6 h-6 text-primary" />
                </div>
                <CardTitle>Personalized Portfolios</CardTitle>
                <CardDescription>
                  AI-powered portfolio generation based on your risk profile, investment horizon, and ethical
                  preferences.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-border hover:shadow-lg transition-shadow border-glow-green">
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 glow-green">
                  <Star className="w-6 h-6 text-primary" />
                </div>
                <CardTitle>Sharia King Stocks</CardTitle>
                <CardDescription>
                  Exclusive access to companies that have maintained halal compliance for 10+ consecutive years.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-border hover:shadow-lg transition-shadow border-glow-green">
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 glow-green">
                  <BarChart3 className="w-6 h-6 text-primary" />
                </div>
                <CardTitle>Real-time Monitoring</CardTitle>
                <CardDescription>
                  Continuous compliance tracking with instant notifications when stocks change status.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-border hover:shadow-lg transition-shadow border-glow-green">
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 glow-green">
                  <DollarSign className="w-6 h-6 text-primary" />
                </div>
                <CardTitle>Transparent Pricing</CardTitle>
                <CardDescription>
                  No hidden fees. Clear, Islamic finance-compliant pricing structure with no interest-based charges.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-border hover:shadow-lg transition-shadow border-glow-green">
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 glow-green">
                  <Users className="w-6 h-6 text-primary" />
                </div>
                <CardTitle>Expert Guidance</CardTitle>
                <CardDescription>
                  Access to certified Islamic finance advisors and comprehensive educational resources.
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* Sharia Compliance Section */}
      <section id="sharia-compliance" className="py-20 px-4 bg-muted/30 relative">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <Badge className="mb-4 bg-primary/10 text-primary border-primary/20 border-glow-green">
                AAOIFI Certified Standards
              </Badge>
              <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
                Rigorous Sharia Compliance Screening
              </h2>
              <p className="text-lg text-muted-foreground mb-8">
                Our advanced algorithms continuously monitor investments against strict Islamic finance criteria,
                ensuring your portfolio remains 100% halal at all times.
              </p>

              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-primary mt-1 flex-shrink-0" />
                  <div>
                    <div className="font-semibold text-foreground">Debt Ratio Screening</div>
                    <div className="text-muted-foreground">
                      Total debt must be less than 33% of market capitalization
                    </div>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-primary mt-1 flex-shrink-0" />
                  <div>
                    <div className="font-semibold text-foreground">Revenue Purity</div>
                    <div className="text-muted-foreground">Non-halal revenue must be less than 5% of total income</div>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-primary mt-1 flex-shrink-0" />
                  <div>
                    <div className="font-semibold text-foreground">Liquidity Requirements</div>
                    <div className="text-muted-foreground">Cash and deposits under 33% of market cap</div>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-primary mt-1 flex-shrink-0" />
                  <div>
                    <div className="font-semibold text-foreground">Sector Exclusions</div>
                    <div className="text-muted-foreground">
                      Automatic filtering of prohibited industries (alcohol, gambling, conventional banking)
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-card rounded-xl p-8 border border-border border-glow-green">
              <h3 className="text-xl font-bold text-foreground mb-6">Portfolio Compliance Dashboard</h3>

              {/* Mock Chart */}
              <div className="space-y-6">
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm text-muted-foreground">Sharia Compliance Score</span>
                    <span className="text-sm font-semibold text-primary text-glow-green">99.8%</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div className="bg-primary h-2 rounded-full" style={{ width: "99.8%" }}></div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-muted/50 rounded-lg">
                    <div className="text-2xl font-bold text-primary text-glow-green">47</div>
                    <div className="text-sm text-muted-foreground">Halal Stocks</div>
                  </div>
                  <div className="text-center p-4 bg-muted/50 rounded-lg">
                    <div className="text-2xl font-bold text-primary text-glow-green">12</div>
                    <div className="text-sm text-muted-foreground">Sharia Kings</div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Technology</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-16 bg-muted rounded-full h-2">
                        <div className="bg-chart-1 h-2 rounded-full" style={{ width: "35%" }}></div>
                      </div>
                      <span className="text-sm font-medium">35%</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Healthcare</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-16 bg-muted rounded-full h-2">
                        <div className="bg-chart-2 h-2 rounded-full" style={{ width: "25%" }}></div>
                      </div>
                      <span className="text-sm font-medium">25%</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Consumer Goods</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-16 bg-muted rounded-full h-2">
                        <div className="bg-chart-3 h-2 rounded-full" style={{ width: "20%" }}></div>
                      </div>
                      <span className="text-sm font-medium">20%</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Energy</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-16 bg-muted rounded-full h-2">
                        <div className="bg-chart-4 h-2 rounded-full" style={{ width: "20%" }}></div>
                      </div>
                      <span className="text-sm font-medium">20%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section id="how-it-works" className="py-20 px-4 relative">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">How Halal Invest Works</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Get started with halal investing in three simple steps
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6 glow-green">
                <span className="text-2xl font-bold text-primary-foreground">1</span>
              </div>
              <h3 className="text-xl font-bold text-foreground mb-4">Complete Your Profile</h3>
              <p className="text-muted-foreground">
                Answer questions about your risk tolerance, investment goals, and ethical preferences to create your
                personalized profile.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6 glow-green">
                <span className="text-2xl font-bold text-primary-foreground">2</span>
              </div>
              <h3 className="text-xl font-bold text-foreground mb-4">Get Your Portfolio</h3>
              <p className="text-muted-foreground">
                Our AI generates a customized halal portfolio with precise euro allocations for each recommended stock.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6 glow-green">
                <span className="text-2xl font-bold text-primary-foreground">3</span>
              </div>
              <h3 className="text-xl font-bold text-foreground mb-4">Monitor & Invest</h3>
              <p className="text-muted-foreground">
                Track your investments with real-time compliance monitoring and receive notifications about any changes.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-primary text-primary-foreground relative">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Start Your Halal Investment Journey Today</h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Join thousands of Muslim investors who trust Halal Invest Advisor for their Sharia-compliant investment
            needs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              variant="secondary"
              className="bg-primary-foreground text-primary hover:bg-primary-foreground/90"
            >
              Get Started Free
              <ArrowRight className="w-5 h-5 ml-2" />
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground/10 bg-transparent"
            >
              Schedule Consultation
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-4 bg-card border-t border-border relative">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center glow-green">
                  <Mosque className="w-5 h-5 text-primary-foreground" />
                </div>
                <span className="text-xl font-bold text-foreground">Halal Invest</span>
              </div>
              <p className="text-muted-foreground">
                Empowering Muslim investors with Sharia-compliant investment solutions.
              </p>
            </div>

            <div>
              <h4 className="font-semibold text-foreground mb-4">Product</h4>
              <ul className="space-y-2 text-muted-foreground">
                <li>
                  <a href="#features" className="hover:text-foreground transition-colors">
                    Features
                  </a>
                </li>
                <li>
                  <a href="#how-it-works" className="hover:text-foreground transition-colors">
                    How It Works
                  </a>
                </li>
                <li>
                  <a href="#sharia-compliance" className="hover:text-foreground transition-colors">
                    Sharia Compliance
                  </a>
                </li>
                <li>
                  <a href="#pricing" className="hover:text-foreground transition-colors">
                    Pricing
                  </a>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold text-foreground mb-4">Company</h4>
              <ul className="space-y-2 text-muted-foreground">
                <li>
                  <a href="#" className="hover:text-foreground transition-colors">
                    About
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-foreground transition-colors">
                    Blog
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-foreground transition-colors">
                    Careers
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-foreground transition-colors">
                    Contact
                  </a>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold text-foreground mb-4">Legal</h4>
              <ul className="space-y-2 text-muted-foreground">
                <li>
                  <a href="#" className="hover:text-foreground transition-colors">
                    Privacy Policy
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-foreground transition-colors">
                    Terms of Service
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-foreground transition-colors">
                    Sharia Compliance
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-foreground transition-colors">
                    Disclaimer
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <div className="border-t border-border mt-8 pt-8 text-center text-muted-foreground">
            <p>
              &copy; 2024 Halal Invest Advisor. All rights reserved. | Certified Sharia-Compliant Investment Platform
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
