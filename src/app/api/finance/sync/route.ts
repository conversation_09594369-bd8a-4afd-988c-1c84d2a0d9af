import { NextRequest, NextResponse } from 'next/server'
import { 
  syncStockData, 
  syncMultipleStocks, 
  updateShariaCompliance, 
  cleanupOldData 
} from '@/lib/database-sync'

/**
 * API Route pour synchroniser les données financières avec la base de données
 * POST /api/finance/sync - Synchroniser des actions spécifiques
 * PUT /api/finance/sync - Mettre à jour la conformité Sharia
 * DELETE /api/finance/sync - Nettoyer les anciennes données
 */

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { symbols, options } = body

    // Validation des paramètres
    if (!symbols || !Array.isArray(symbols)) {
      return NextResponse.json(
        { error: 'Missing or invalid symbols array' },
        { status: 400 }
      )
    }

    if (symbols.length === 0) {
      return NextResponse.json(
        { error: 'Symbols array cannot be empty' },
        { status: 400 }
      )
    }

    if (symbols.length > 50) {
      return NextResponse.json(
        { error: 'Too many symbols. Maximum 50 symbols allowed for sync.' },
        { status: 400 }
      )
    }

    // Validation et nettoyage des symboles
    const validSymbols = symbols
      .map((symbol: any) => {
        if (typeof symbol !== 'string') {
          return null
        }
        return symbol.trim().toUpperCase()
      })
      .filter((symbol: string | null): symbol is string => {
        return symbol !== null && isValidSymbol(symbol)
      })

    if (validSymbols.length === 0) {
      return NextResponse.json(
        { error: 'No valid symbols provided' },
        { status: 400 }
      )
    }

    // Synchroniser les données
    const startTime = Date.now()
    const results = await syncMultipleStocks(validSymbols, options)
    const endTime = Date.now()

    // Calculer les statistiques
    const successCount = Object.values(results).filter(r => r.success).length
    const failureCount = validSymbols.length - successCount

    return NextResponse.json({
      success: true,
      message: 'Synchronization completed',
      data: {
        symbolsRequested: symbols.length,
        symbolsProcessed: validSymbols.length,
        successCount,
        failureCount,
        processingTimeMs: endTime - startTime,
        results
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error during sync:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to synchronize data',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * PUT - Mettre à jour la conformité Sharia pour toutes les actions
 */
export async function PUT(request: NextRequest) {
  try {
    const startTime = Date.now()
    const updatedCount = await updateShariaCompliance()
    const endTime = Date.now()

    return NextResponse.json({
      success: true,
      message: 'Sharia compliance updated',
      data: {
        updatedCount,
        processingTimeMs: endTime - startTime
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error updating Sharia compliance:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to update Sharia compliance',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * DELETE - Nettoyer les anciennes données
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const daysToKeepParam = searchParams.get('daysToKeep')
    const daysToKeep = daysToKeepParam ? parseInt(daysToKeepParam, 10) : 365

    // Validation
    if (isNaN(daysToKeep) || daysToKeep < 1 || daysToKeep > 3650) {
      return NextResponse.json(
        { error: 'Invalid daysToKeep. Must be between 1 and 3650.' },
        { status: 400 }
      )
    }

    const startTime = Date.now()
    const cleanupResults = await cleanupOldData(daysToKeep)
    const endTime = Date.now()

    return NextResponse.json({
      success: true,
      message: 'Data cleanup completed',
      data: {
        daysToKeep,
        historicalRecordsDeleted: cleanupResults.historicalDeleted,
        cacheEntriesDeleted: cleanupResults.cacheDeleted,
        processingTimeMs: endTime - startTime
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error during cleanup:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to cleanup data',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * GET - Obtenir le statut de synchronisation
 */
export async function GET(request: NextRequest) {
  try {
    // Cette route pourrait retourner des informations sur le dernier sync
    // Pour l'instant, on retourne juste un statut basique
    
    return NextResponse.json({
      success: true,
      message: 'Sync API is operational',
      endpoints: {
        'POST /api/finance/sync': 'Synchronize specific stocks',
        'PUT /api/finance/sync': 'Update Sharia compliance for all stocks',
        'DELETE /api/finance/sync?daysToKeep=365': 'Cleanup old data'
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error getting sync status:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to get sync status',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * Validation des symboles
 */
function isValidSymbol(symbol: string): boolean {
  return /^[A-Z0-9]{1,10}$/.test(symbol)
}
