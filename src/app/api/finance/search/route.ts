import { NextRequest, NextResponse } from 'next/server'
import { searchStocks } from '@/lib/yahoo-finance'

/**
 * API Route pour rechercher des actions
 * GET /api/finance/search?q=apple
 * GET /api/finance/search?q=AAPL&limit=10
 */

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')
    const limitParam = searchParams.get('limit')
    const limit = limitParam ? parseInt(limitParam, 10) : 20

    // Validation des paramètres
    if (!query) {
      return NextResponse.json(
        { error: 'Missing required parameter: q (query)' },
        { status: 400 }
      )
    }

    // Validation de la requête
    if (query.length < 2) {
      return NextResponse.json(
        { error: 'Query must be at least 2 characters long' },
        { status: 400 }
      )
    }

    if (query.length > 50) {
      return NextResponse.json(
        { error: 'Query too long. Maximum 50 characters allowed.' },
        { status: 400 }
      )
    }

    // Validation de la limite
    if (isNaN(limit) || limit < 1 || limit > 100) {
      return NextResponse.json(
        { error: 'Invalid limit. Must be between 1 and 100.' },
        { status: 400 }
      )
    }

    // Recherche des actions
    const results = await searchStocks(query)
    
    // Filtrer et limiter les résultats
    const filteredResults = results
      .filter(result => result.typeDisp === 'Equity') // Seulement les actions
      .slice(0, limit)
      .map(result => ({
        symbol: result.symbol,
        name: result.longname || result.shortname,
        exchange: result.exchange,
        sector: result.sector,
        industry: result.industry,
        marketCap: result.marketCap,
        currency: result.currency
      }))

    return NextResponse.json({
      success: true,
      data: filteredResults,
      count: filteredResults.length,
      query,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error searching stocks:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to search stocks',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
