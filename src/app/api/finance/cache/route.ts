import { NextRequest, NextResponse } from 'next/server'
import { getCacheStats, clearCache } from '@/lib/yahoo-finance'

/**
 * API Route pour gérer le cache des données financières
 * GET /api/finance/cache - Obtenir les statistiques du cache
 * DELETE /api/finance/cache - Vider tout le cache
 * DELETE /api/finance/cache?symbol=AAPL - Vider le cache pour un symbole
 */

export async function GET(request: NextRequest) {
  try {
    const stats = getCacheStats()
    
    return NextResponse.json({
      success: true,
      data: stats,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error getting cache stats:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to get cache statistics',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const symbol = searchParams.get('symbol')

    if (symbol) {
      // Vider le cache pour un symbole spécifique
      clearCache(symbol.toUpperCase())
      
      return NextResponse.json({
        success: true,
        message: `Cache cleared for symbol: ${symbol.toUpperCase()}`,
        timestamp: new Date().toISOString()
      })
    } else {
      // Vider tout le cache
      clearCache()
      
      return NextResponse.json({
        success: true,
        message: 'All cache cleared',
        timestamp: new Date().toISOString()
      })
    }

  } catch (error) {
    console.error('Error clearing cache:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to clear cache',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
