import { NextRequest, NextResponse } from 'next/server'
import { getQuote, getMultipleQuotes } from '@/lib/yahoo-finance'

/**
 * API Route pour récupérer les cotations en temps réel
 * GET /api/finance/quote?symbol=AAPL
 * GET /api/finance/quote?symbols=AAPL,MSFT,GOOGL
 */

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const symbol = searchParams.get('symbol')
    const symbols = searchParams.get('symbols')

    // Validation des paramètres
    if (!symbol && !symbols) {
      return NextResponse.json(
        { error: 'Missing required parameter: symbol or symbols' },
        { status: 400 }
      )
    }

    // Récupération de plusieurs symboles
    if (symbols) {
      const symbolList = symbols.split(',').map(s => s.trim().toUpperCase())
      
      if (symbolList.length > 50) {
        return NextResponse.json(
          { error: 'Too many symbols. Maximum 50 symbols allowed.' },
          { status: 400 }
        )
      }

      const quotes = await getMultipleQuotes(symbolList)
      
      return NextResponse.json({
        success: true,
        data: quotes,
        count: quotes.length,
        timestamp: new Date().toISOString()
      })
    }

    // Récupération d'un seul symbole
    if (symbol) {
      const quote = await getQuote(symbol.toUpperCase())
      
      return NextResponse.json({
        success: true,
        data: quote,
        timestamp: new Date().toISOString()
      })
    }

  } catch (error) {
    console.error('Error fetching quote:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch quote data',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * Validation des symboles
 */
function isValidSymbol(symbol: string): boolean {
  // Symbole doit être entre 1 et 10 caractères, lettres et chiffres uniquement
  return /^[A-Z0-9]{1,10}$/.test(symbol)
}

/**
 * Middleware pour valider les symboles
 */
function validateSymbols(symbols: string[]): string[] {
  return symbols.filter(symbol => {
    const isValid = isValidSymbol(symbol)
    if (!isValid) {
      console.warn(`Invalid symbol format: ${symbol}`)
    }
    return isValid
  })
}
