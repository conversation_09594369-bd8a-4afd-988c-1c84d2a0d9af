import { NextRequest, NextResponse } from 'next/server'
import { getHistoricalData } from '@/lib/yahoo-finance'

/**
 * API Route pour récupérer l'historique des prix
 * GET /api/finance/historical?symbol=AAPL&period=1y
 * GET /api/finance/historical?symbol=AAPL&from=2024-01-01&to=2024-12-31&interval=1d
 */

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const symbol = searchParams.get('symbol')
    const period = searchParams.get('period')
    const from = searchParams.get('from')
    const to = searchParams.get('to')
    const interval = searchParams.get('interval') as '1d' | '1wk' | '1mo' || '1d'

    // Validation des paramètres
    if (!symbol) {
      return NextResponse.json(
        { error: 'Missing required parameter: symbol' },
        { status: 400 }
      )
    }

    // Validation du format du symbole
    if (!isValidSymbol(symbol.toUpperCase())) {
      return NextResponse.json(
        { error: 'Invalid symbol format' },
        { status: 400 }
      )
    }

    // Validation de l'intervalle
    if (!['1d', '1wk', '1mo'].includes(interval)) {
      return NextResponse.json(
        { error: 'Invalid interval. Must be 1d, 1wk, or 1mo' },
        { status: 400 }
      )
    }

    let period1: Date
    let period2: Date = new Date()

    // Déterminer les dates selon les paramètres
    if (period) {
      period1 = getPeriodStartDate(period)
    } else if (from && to) {
      period1 = new Date(from)
      period2 = new Date(to)
      
      // Validation des dates
      if (isNaN(period1.getTime()) || isNaN(period2.getTime())) {
        return NextResponse.json(
          { error: 'Invalid date format. Use YYYY-MM-DD' },
          { status: 400 }
        )
      }
      
      if (period1 >= period2) {
        return NextResponse.json(
          { error: 'Start date must be before end date' },
          { status: 400 }
        )
      }
    } else {
      // Par défaut: 1 an
      period1 = getPeriodStartDate('1y')
    }

    // Récupération des données historiques
    const historicalData = await getHistoricalData(
      symbol.toUpperCase(),
      period1,
      period2,
      interval
    )

    return NextResponse.json({
      success: true,
      data: historicalData,
      count: historicalData.length,
      period: {
        from: period1.toISOString().split('T')[0],
        to: period2.toISOString().split('T')[0],
        interval
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error fetching historical data:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch historical data',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * Convertit une période en date de début
 */
function getPeriodStartDate(period: string): Date {
  const now = new Date()
  const date = new Date(now)

  switch (period) {
    case '1d':
      date.setDate(date.getDate() - 1)
      break
    case '5d':
      date.setDate(date.getDate() - 5)
      break
    case '1m':
      date.setMonth(date.getMonth() - 1)
      break
    case '3m':
      date.setMonth(date.getMonth() - 3)
      break
    case '6m':
      date.setMonth(date.getMonth() - 6)
      break
    case '1y':
      date.setFullYear(date.getFullYear() - 1)
      break
    case '2y':
      date.setFullYear(date.getFullYear() - 2)
      break
    case '5y':
      date.setFullYear(date.getFullYear() - 5)
      break
    case '10y':
      date.setFullYear(date.getFullYear() - 10)
      break
    default:
      // Par défaut: 1 an
      date.setFullYear(date.getFullYear() - 1)
  }

  return date
}

/**
 * Validation des symboles
 */
function isValidSymbol(symbol: string): boolean {
  return /^[A-Z0-9]{1,10}$/.test(symbol)
}
