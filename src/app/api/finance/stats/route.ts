import { NextRequest, NextResponse } from 'next/server'
import { getFinancialStats, calculateShariaRatios } from '@/lib/yahoo-finance'

/**
 * API Route pour récupérer les statistiques financières détaillées
 * GET /api/finance/stats?symbol=AAPL
 * GET /api/finance/stats?symbol=AAPL&sharia=true
 */

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const symbol = searchParams.get('symbol')
    const includeSharia = searchParams.get('sharia') === 'true'

    // Validation des paramètres
    if (!symbol) {
      return NextResponse.json(
        { error: 'Missing required parameter: symbol' },
        { status: 400 }
      )
    }

    // Validation du format du symbole
    if (!isValidSymbol(symbol.toUpperCase())) {
      return NextResponse.json(
        { error: 'Invalid symbol format' },
        { status: 400 }
      )
    }

    // Récupération des statistiques financières
    const stats = await getFinancialStats(symbol.toUpperCase())
    
    let response: any = {
      success: true,
      data: stats,
      timestamp: new Date().toISOString()
    }

    // Ajouter les ratios Sharia si demandé
    if (includeSharia) {
      const shariaRatios = calculateShariaRatios(stats)
      response.data.shariaRatios = shariaRatios
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Error fetching financial stats:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch financial statistics',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * Validation des symboles
 */
function isValidSymbol(symbol: string): boolean {
  return /^[A-Z0-9]{1,10}$/.test(symbol)
}
