import { NextRequest, NextResponse } from 'next/server'
import { preloadData } from '@/lib/yahoo-finance'

/**
 * API Route pour précharger les données financières
 * POST /api/finance/preload
 * Body: { symbols: ["AAPL", "MSFT", "GOOGL"] }
 */

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { symbols } = body

    // Validation des paramètres
    if (!symbols || !Array.isArray(symbols)) {
      return NextResponse.json(
        { error: 'Missing or invalid symbols array' },
        { status: 400 }
      )
    }

    if (symbols.length === 0) {
      return NextResponse.json(
        { error: 'Symbols array cannot be empty' },
        { status: 400 }
      )
    }

    if (symbols.length > 100) {
      return NextResponse.json(
        { error: 'Too many symbols. Maximum 100 symbols allowed.' },
        { status: 400 }
      )
    }

    // Validation et nettoyage des symboles
    const validSymbols = symbols
      .map((symbol: any) => {
        if (typeof symbol !== 'string') {
          return null
        }
        return symbol.trim().toUpperCase()
      })
      .filter((symbol: string | null): symbol is string => {
        return symbol !== null && isValidSymbol(symbol)
      })

    if (validSymbols.length === 0) {
      return NextResponse.json(
        { error: 'No valid symbols provided' },
        { status: 400 }
      )
    }

    // Précharger les données
    const startTime = Date.now()
    await preloadData(validSymbols)
    const endTime = Date.now()

    return NextResponse.json({
      success: true,
      message: 'Data preloaded successfully',
      data: {
        symbolsRequested: symbols.length,
        symbolsProcessed: validSymbols.length,
        symbols: validSymbols,
        processingTimeMs: endTime - startTime
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error preloading data:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to preload data',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * Validation des symboles
 */
function isValidSymbol(symbol: string): boolean {
  return /^[A-Z0-9]{1,10}$/.test(symbol)
}

/**
 * GET endpoint pour obtenir la liste des symboles populaires à précharger
 */
export async function GET(request: NextRequest) {
  try {
    // Liste des symboles populaires pour le préchargement
    const popularSymbols = [
      // Tech Giants
      'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META', 'TSLA', 'NVDA', 'NFLX',
      // Finance
      'JPM', 'BAC', 'WFC', 'GS', 'MS', 'C',
      // Healthcare
      'JNJ', 'PFE', 'UNH', 'ABBV', 'MRK', 'TMO',
      // Consumer
      'PG', 'KO', 'PEP', 'WMT', 'HD', 'MCD',
      // Industrial
      'BA', 'CAT', 'GE', 'MMM', 'HON', 'UPS',
      // Energy
      'XOM', 'CVX', 'COP', 'SLB', 'EOG',
      // Utilities
      'NEE', 'DUK', 'SO', 'AEP', 'EXC'
    ]

    return NextResponse.json({
      success: true,
      data: {
        popularSymbols,
        count: popularSymbols.length,
        description: 'Popular symbols recommended for preloading'
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error getting popular symbols:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to get popular symbols',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
