import { NextRequest, NextResponse } from 'next/server'
import { getRevenueBreakdown, generateSankeyData } from '@/lib/revenue-analysis'

/**
 * API Route pour récupérer l'analyse détaillée des revenus
 * GET /api/finance/revenue?symbol=AAPL
 * GET /api/finance/revenue?symbol=AAPL&format=sankey
 */

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const symbol = searchParams.get('symbol')
    const format = searchParams.get('format') // 'sankey' pour les données de diagramme

    // Validation des paramètres
    if (!symbol) {
      return NextResponse.json(
        { error: 'Missing required parameter: symbol' },
        { status: 400 }
      )
    }

    // Validation du format du symbole
    if (!isValidSymbol(symbol.toUpperCase())) {
      return NextResponse.json(
        { error: 'Invalid symbol format' },
        { status: 400 }
      )
    }

    // Récupération des données de revenus
    const revenueData = await getRevenueBreakdown(symbol.toUpperCase())
    
    if (!revenueData) {
      return NextResponse.json(
        { error: 'Revenue data not available for this symbol' },
        { status: 404 }
      )
    }

    let response: any = {
      success: true,
      data: revenueData,
      timestamp: new Date().toISOString()
    }

    // Ajouter les données Sankey si demandé
    if (format === 'sankey') {
      const sankeyData = generateSankeyData(revenueData)
      response.sankeyData = sankeyData
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Error fetching revenue data:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch revenue data',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * Validation des symboles
 */
function isValidSymbol(symbol: string): boolean {
  return /^[A-Z0-9]{1,10}$/.test(symbol)
}
