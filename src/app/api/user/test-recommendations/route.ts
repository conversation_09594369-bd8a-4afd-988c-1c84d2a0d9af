import { NextRequest, NextResponse } from 'next/server'

/**
 * API Route de TEST pour les recommandations utilisateur (sans authentification)
 * GET /api/user/test-recommendations - Récupérer les recommandations de test
 * POST /api/user/test-recommendations - Générer de nouvelles recommandations de test
 */

// Données de test en mémoire
let testRecommendations = [
  {
    id: 'rec-1',
    user_id: 'test-user',
    symbol: 'NVDA',
    recommendation_type: 'buy',
    confidence_score: 85,
    target_price: 950,
    current_price: 875,
    potential_return: 8.57,
    reasoning: 'Leader de l\'IA et des GPU, croissance forte attendue',
    risk_level: 'moderate',
    sharia_compliant: true,
    created_at: '2024-01-20T10:00:00Z',
    expires_at: '2024-02-20T10:00:00Z'
  },
  {
    id: 'rec-2',
    user_id: 'test-user',
    symbol: 'META',
    recommendation_type: 'hold',
    confidence_score: 72,
    target_price: 520,
    current_price: 485,
    potential_return: 7.22,
    reasoning: 'Métaverse en développement, revenus publicitaires stables',
    risk_level: 'moderate',
    sharia_compliant: true,
    created_at: '2024-01-18T15:30:00Z',
    expires_at: '2024-02-18T15:30:00Z'
  },
  {
    id: 'rec-3',
    user_id: 'test-user',
    symbol: 'TSLA',
    recommendation_type: 'watch',
    confidence_score: 68,
    target_price: 280,
    current_price: 245,
    potential_return: 14.29,
    reasoning: 'Volatilité élevée, attendre une meilleure entrée',
    risk_level: 'high',
    sharia_compliant: true,
    created_at: '2024-01-19T09:15:00Z',
    expires_at: '2024-02-19T09:15:00Z'
  }
]

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') // buy, hold, watch, sell
    const limit = parseInt(searchParams.get('limit') || '10')

    // Simuler un délai réseau
    await new Promise(resolve => setTimeout(resolve, 200))

    let recommendations = [...testRecommendations]

    // Filtrer par type si spécifié
    if (type) {
      recommendations = recommendations.filter(rec => rec.recommendation_type === type)
    }

    // Limiter le nombre de résultats
    recommendations = recommendations.slice(0, limit)

    // Trier par score de confiance décroissant
    recommendations.sort((a, b) => b.confidence_score - a.confidence_score)

    return NextResponse.json({
      success: true,
      data: recommendations,
      count: recommendations.length,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in test recommendations GET:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Simuler un délai de génération
    await new Promise(resolve => setTimeout(resolve, 1500))

    // Générer de nouvelles recommandations de test
    const newRecommendations = [
      {
        id: `rec-${Date.now()}-1`,
        user_id: 'test-user',
        symbol: 'AMD',
        recommendation_type: 'buy',
        confidence_score: 78,
        target_price: 165,
        current_price: 145,
        potential_return: 13.79,
        reasoning: 'Concurrent direct de NVIDIA, prix attractif',
        risk_level: 'moderate',
        sharia_compliant: true,
        created_at: new Date().toISOString(),
        expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 jours
      },
      {
        id: `rec-${Date.now()}-2`,
        user_id: 'test-user',
        symbol: 'CRM',
        recommendation_type: 'hold',
        confidence_score: 74,
        target_price: 280,
        current_price: 265,
        potential_return: 5.66,
        reasoning: 'Leader du CRM cloud, croissance stable',
        risk_level: 'low',
        sharia_compliant: true,
        created_at: new Date().toISOString(),
        expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
      }
    ]

    // Ajouter les nouvelles recommandations
    testRecommendations.push(...newRecommendations)

    // Garder seulement les 10 plus récentes
    testRecommendations = testRecommendations
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      .slice(0, 10)

    return NextResponse.json({
      success: true,
      data: {
        generated: newRecommendations,
        count: newRecommendations.length
      },
      message: 'New recommendations generated successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in test recommendations POST:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
