import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

/**
 * API Route pour gérer la watchlist utilisateur
 * GET /api/user/watchlist - Récupérer la watchlist
 * POST /api/user/watchlist - Ajouter une action à la watchlist
 * DELETE /api/user/watchlist - Supprimer une action de la watchlist
 */

export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Vérifier l'authentification
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Récupérer la watchlist avec les informations des actions
    const { data: watchlist, error } = await supabase
      .from('user_watchlists')
      .select(`
        *,
        stocks:symbol (
          symbol,
          name,
          sector,
          is_sharia_compliant,
          sharia_king_since,
          current_price,
          price_change,
          price_change_percent,
          market_cap
        )
      `)
      .eq('user_id', user.id)
      .order('added_at', { ascending: false })

    if (error) {
      console.error('Error fetching watchlist:', error)
      return NextResponse.json(
        { error: 'Failed to fetch watchlist' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: watchlist,
      count: watchlist?.length || 0,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in watchlist GET:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Vérifier l'authentification
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { symbol, notes, targetPrice, alertEnabled } = body

    // Validation
    if (!symbol || typeof symbol !== 'string') {
      return NextResponse.json(
        { error: 'Valid symbol is required' },
        { status: 400 }
      )
    }

    // Vérifier que l'action existe
    const { data: stockExists } = await supabase
      .from('stocks')
      .select('symbol')
      .eq('symbol', symbol.toUpperCase())
      .single()

    if (!stockExists) {
      return NextResponse.json(
        { error: 'Stock not found' },
        { status: 404 }
      )
    }

    // Ajouter à la watchlist
    const { data: watchlistItem, error } = await supabase
      .from('user_watchlists')
      .insert({
        user_id: user.id,
        symbol: symbol.toUpperCase(),
        notes: notes || null,
        target_price: targetPrice || null,
        alert_enabled: alertEnabled || false
      })
      .select()
      .single()

    if (error) {
      if (error.code === '23505') { // Unique constraint violation
        return NextResponse.json(
          { error: 'Stock already in watchlist' },
          { status: 409 }
        )
      }
      console.error('Error adding to watchlist:', error)
      return NextResponse.json(
        { error: 'Failed to add to watchlist' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: watchlistItem,
      message: `${symbol.toUpperCase()} added to watchlist`,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in watchlist POST:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Vérifier l'authentification
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const symbol = searchParams.get('symbol')

    if (!symbol) {
      return NextResponse.json(
        { error: 'Symbol parameter is required' },
        { status: 400 }
      )
    }

    // Supprimer de la watchlist
    const { error } = await supabase
      .from('user_watchlists')
      .delete()
      .eq('user_id', user.id)
      .eq('symbol', symbol.toUpperCase())

    if (error) {
      console.error('Error removing from watchlist:', error)
      return NextResponse.json(
        { error: 'Failed to remove from watchlist' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: `${symbol.toUpperCase()} removed from watchlist`,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in watchlist DELETE:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Vérifier l'authentification
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { symbol, notes, targetPrice, alertEnabled } = body

    if (!symbol) {
      return NextResponse.json(
        { error: 'Symbol is required' },
        { status: 400 }
      )
    }

    // Mettre à jour l'entrée de la watchlist
    const updateData: any = {}
    if (notes !== undefined) updateData.notes = notes
    if (targetPrice !== undefined) updateData.target_price = targetPrice
    if (alertEnabled !== undefined) updateData.alert_enabled = alertEnabled

    const { data: updatedItem, error } = await supabase
      .from('user_watchlists')
      .update(updateData)
      .eq('user_id', user.id)
      .eq('symbol', symbol.toUpperCase())
      .select()
      .single()

    if (error) {
      console.error('Error updating watchlist item:', error)
      return NextResponse.json(
        { error: 'Failed to update watchlist item' },
        { status: 500 }
      )
    }

    if (!updatedItem) {
      return NextResponse.json(
        { error: 'Watchlist item not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: updatedItem,
      message: `${symbol.toUpperCase()} watchlist item updated`,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in watchlist PATCH:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
