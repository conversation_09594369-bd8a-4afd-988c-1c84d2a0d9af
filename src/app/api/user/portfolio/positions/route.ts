import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

/**
 * API Route pour gérer les positions de portefeuille
 * GET /api/user/portfolio/positions?portfolioId=xxx - Récupérer les positions
 * POST /api/user/portfolio/positions - Ajouter une position
 * PATCH /api/user/portfolio/positions - Mettre à jour une position
 * DELETE /api/user/portfolio/positions - Supprimer une position
 */

export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Vérifier l'authentification
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const portfolioId = searchParams.get('portfolioId')

    if (!portfolioId) {
      return NextResponse.json(
        { error: 'Portfolio ID is required' },
        { status: 400 }
      )
    }

    // Vérifier que le portefeuille appartient à l'utilisateur
    const { data: portfolio, error: portfolioError } = await supabase
      .from('user_portfolios')
      .select('id')
      .eq('id', portfolioId)
      .eq('user_id', user.id)
      .single()

    if (portfolioError || !portfolio) {
      return NextResponse.json(
        { error: 'Portfolio not found' },
        { status: 404 }
      )
    }

    // Récupérer les positions avec les informations des actions
    const { data: positions, error } = await supabase
      .from('user_portfolio_positions')
      .select(`
        *,
        stocks:symbol (
          symbol,
          name,
          sector,
          is_sharia_compliant,
          sharia_king_since,
          current_price,
          price_change,
          price_change_percent,
          market_cap
        )
      `)
      .eq('portfolio_id', portfolioId)
      .order('last_updated', { ascending: false })

    if (error) {
      console.error('Error fetching positions:', error)
      return NextResponse.json(
        { error: 'Failed to fetch positions' },
        { status: 500 }
      )
    }

    // Enrichir les positions avec les calculs
    const enrichedPositions = positions?.map(position => {
      const currentPrice = position.stocks?.current_price || position.average_price
      const currentValue = position.shares * currentPrice
      const gainLoss = currentValue - position.total_invested
      const gainLossPercent = position.total_invested > 0 
        ? (gainLoss / position.total_invested) * 100 
        : 0

      return {
        ...position,
        currentPrice,
        currentValue,
        gainLoss,
        gainLossPercent
      }
    }) || []

    return NextResponse.json({
      success: true,
      data: enrichedPositions,
      count: enrichedPositions.length,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in positions GET:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Vérifier l'authentification
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { portfolioId, symbol, shares, averagePrice, targetAllocationPercent } = body

    // Validation
    if (!portfolioId || !symbol || !shares || !averagePrice) {
      return NextResponse.json(
        { error: 'Portfolio ID, symbol, shares, and average price are required' },
        { status: 400 }
      )
    }

    if (shares <= 0 || averagePrice <= 0) {
      return NextResponse.json(
        { error: 'Shares and average price must be positive' },
        { status: 400 }
      )
    }

    // Vérifier que le portefeuille appartient à l'utilisateur
    const { data: portfolio, error: portfolioError } = await supabase
      .from('user_portfolios')
      .select('id')
      .eq('id', portfolioId)
      .eq('user_id', user.id)
      .single()

    if (portfolioError || !portfolio) {
      return NextResponse.json(
        { error: 'Portfolio not found' },
        { status: 404 }
      )
    }

    // Vérifier que l'action existe
    const { data: stockExists } = await supabase
      .from('stocks')
      .select('symbol')
      .eq('symbol', symbol.toUpperCase())
      .single()

    if (!stockExists) {
      return NextResponse.json(
        { error: 'Stock not found' },
        { status: 404 }
      )
    }

    const totalInvested = shares * averagePrice

    // Ajouter la position
    const { data: position, error } = await supabase
      .from('user_portfolio_positions')
      .insert({
        portfolio_id: portfolioId,
        symbol: symbol.toUpperCase(),
        shares,
        average_price: averagePrice,
        total_invested: totalInvested,
        target_allocation_percent: targetAllocationPercent || null
      })
      .select()
      .single()

    if (error) {
      if (error.code === '23505') { // Unique constraint violation
        return NextResponse.json(
          { error: 'Position already exists for this stock' },
          { status: 409 }
        )
      }
      console.error('Error adding position:', error)
      return NextResponse.json(
        { error: 'Failed to add position' },
        { status: 500 }
      )
    }

    // Créer une transaction d'achat
    await supabase
      .from('user_transactions')
      .insert({
        user_id: user.id,
        portfolio_id: portfolioId,
        symbol: symbol.toUpperCase(),
        transaction_type: 'buy',
        shares,
        price_per_share: averagePrice,
        total_amount: totalInvested,
        notes: 'Initial position'
      })

    return NextResponse.json({
      success: true,
      data: position,
      message: `Position added for ${symbol.toUpperCase()}`,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in positions POST:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Vérifier l'authentification
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { positionId, shares, averagePrice, targetAllocationPercent } = body

    if (!positionId) {
      return NextResponse.json(
        { error: 'Position ID is required' },
        { status: 400 }
      )
    }

    // Récupérer la position existante pour vérifier les permissions
    const { data: existingPosition, error: fetchError } = await supabase
      .from('user_portfolio_positions')
      .select(`
        *,
        user_portfolios!inner(user_id)
      `)
      .eq('id', positionId)
      .single()

    if (fetchError || !existingPosition || existingPosition.user_portfolios.user_id !== user.id) {
      return NextResponse.json(
        { error: 'Position not found' },
        { status: 404 }
      )
    }

    // Préparer les données de mise à jour
    const updateData: any = {}
    if (shares !== undefined) {
      updateData.shares = shares
      updateData.total_invested = shares * (averagePrice || existingPosition.average_price)
    }
    if (averagePrice !== undefined) {
      updateData.average_price = averagePrice
      updateData.total_invested = (shares || existingPosition.shares) * averagePrice
    }
    if (targetAllocationPercent !== undefined) {
      updateData.target_allocation_percent = targetAllocationPercent
    }

    // Mettre à jour la position
    const { data: position, error } = await supabase
      .from('user_portfolio_positions')
      .update(updateData)
      .eq('id', positionId)
      .select()
      .single()

    if (error) {
      console.error('Error updating position:', error)
      return NextResponse.json(
        { error: 'Failed to update position' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: position,
      message: 'Position updated successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in positions PATCH:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Vérifier l'authentification
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const positionId = searchParams.get('id')

    if (!positionId) {
      return NextResponse.json(
        { error: 'Position ID is required' },
        { status: 400 }
      )
    }

    // Vérifier que la position appartient à l'utilisateur
    const { data: position, error: fetchError } = await supabase
      .from('user_portfolio_positions')
      .select(`
        *,
        user_portfolios!inner(user_id)
      `)
      .eq('id', positionId)
      .single()

    if (fetchError || !position || position.user_portfolios.user_id !== user.id) {
      return NextResponse.json(
        { error: 'Position not found' },
        { status: 404 }
      )
    }

    // Supprimer la position
    const { error } = await supabase
      .from('user_portfolio_positions')
      .delete()
      .eq('id', positionId)

    if (error) {
      console.error('Error deleting position:', error)
      return NextResponse.json(
        { error: 'Failed to delete position' },
        { status: 500 }
      )
    }

    // Créer une transaction de vente
    await supabase
      .from('user_transactions')
      .insert({
        user_id: user.id,
        portfolio_id: position.portfolio_id,
        symbol: position.symbol,
        transaction_type: 'sell',
        shares: position.shares,
        price_per_share: position.average_price,
        total_amount: position.total_invested,
        notes: 'Position closed'
      })

    return NextResponse.json({
      success: true,
      message: `Position deleted for ${position.symbol}`,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in positions DELETE:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
