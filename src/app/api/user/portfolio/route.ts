import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

/**
 * API Route pour gérer les portefeuilles utilisateur
 * GET /api/user/portfolio - Récupérer les portefeuilles
 * POST /api/user/portfolio - Créer un nouveau portefeuille
 * PATCH /api/user/portfolio - Mettre à jour un portefeuille
 * DELETE /api/user/portfolio - Supprimer un portefeuille
 */

export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Vérifier l'authentification
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const portfolioId = searchParams.get('id')
    const includePositions = searchParams.get('includePositions') === 'true'
    const includeSummary = searchParams.get('includeSummary') === 'true'

    if (portfolioId) {
      // Récupérer un portefeuille spécifique
      let query = supabase
        .from('user_portfolios')
        .select('*')
        .eq('id', portfolioId)
        .eq('user_id', user.id)
        .single()

      const { data: portfolio, error } = await query

      if (error) {
        console.error('Error fetching portfolio:', error)
        return NextResponse.json(
          { error: 'Portfolio not found' },
          { status: 404 }
        )
      }

      let result: any = { portfolio }

      // Inclure les positions si demandé
      if (includePositions) {
        const { data: positions, error: positionsError } = await supabase
          .from('user_portfolio_positions')
          .select(`
            *,
            stocks:symbol (
              symbol,
              name,
              sector,
              is_sharia_compliant,
              sharia_king_since,
              current_price,
              price_change,
              price_change_percent
            )
          `)
          .eq('portfolio_id', portfolioId)
          .order('last_updated', { ascending: false })

        if (positionsError) {
          console.error('Error fetching positions:', positionsError)
        } else {
          result.positions = positions
        }
      }

      // Inclure le résumé si demandé
      if (includeSummary) {
        const { data: summary, error: summaryError } = await supabase
          .from('user_portfolio_summary')
          .select('*')
          .eq('portfolio_id', portfolioId)
          .single()

        if (summaryError) {
          console.error('Error fetching portfolio summary:', summaryError)
        } else {
          result.summary = summary
        }
      }

      return NextResponse.json({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      })

    } else {
      // Récupérer tous les portefeuilles
      const { data: portfolios, error } = await supabase
        .from('user_portfolios')
        .select('*')
        .eq('user_id', user.id)
        .eq('is_active', true)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching portfolios:', error)
        return NextResponse.json(
          { error: 'Failed to fetch portfolios' },
          { status: 500 }
        )
      }

      // Inclure les résumés si demandé
      if (includeSummary) {
        const { data: summaries, error: summariesError } = await supabase
          .from('user_portfolio_summary')
          .select('*')
          .eq('user_id', user.id)

        if (!summariesError && summaries) {
          const summariesMap = summaries.reduce((acc, summary) => {
            acc[summary.portfolio_id] = summary
            return acc
          }, {} as Record<string, any>)

          portfolios.forEach((portfolio: any) => {
            portfolio.summary = summariesMap[portfolio.id]
          })
        }
      }

      return NextResponse.json({
        success: true,
        data: portfolios,
        count: portfolios?.length || 0,
        timestamp: new Date().toISOString()
      })
    }

  } catch (error) {
    console.error('Error in portfolio GET:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Vérifier l'authentification
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { name, description, targetAmount, riskTolerance } = body

    // Validation
    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      return NextResponse.json(
        { error: 'Portfolio name is required' },
        { status: 400 }
      )
    }

    if (riskTolerance && !['conservative', 'moderate', 'aggressive'].includes(riskTolerance)) {
      return NextResponse.json(
        { error: 'Invalid risk tolerance' },
        { status: 400 }
      )
    }

    // Créer le portefeuille
    const { data: portfolio, error } = await supabase
      .from('user_portfolios')
      .insert({
        user_id: user.id,
        name: name.trim(),
        description: description || null,
        target_amount: targetAmount || null,
        risk_tolerance: riskTolerance || 'moderate'
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating portfolio:', error)
      return NextResponse.json(
        { error: 'Failed to create portfolio' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: portfolio,
      message: 'Portfolio created successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in portfolio POST:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Vérifier l'authentification
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { id, name, description, targetAmount, riskTolerance, isActive } = body

    if (!id) {
      return NextResponse.json(
        { error: 'Portfolio ID is required' },
        { status: 400 }
      )
    }

    // Préparer les données de mise à jour
    const updateData: any = {}
    if (name !== undefined) updateData.name = name.trim()
    if (description !== undefined) updateData.description = description
    if (targetAmount !== undefined) updateData.target_amount = targetAmount
    if (riskTolerance !== undefined) updateData.risk_tolerance = riskTolerance
    if (isActive !== undefined) updateData.is_active = isActive

    // Mettre à jour le portefeuille
    const { data: portfolio, error } = await supabase
      .from('user_portfolios')
      .update(updateData)
      .eq('id', id)
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) {
      console.error('Error updating portfolio:', error)
      return NextResponse.json(
        { error: 'Failed to update portfolio' },
        { status: 500 }
      )
    }

    if (!portfolio) {
      return NextResponse.json(
        { error: 'Portfolio not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: portfolio,
      message: 'Portfolio updated successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in portfolio PATCH:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Vérifier l'authentification
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const portfolioId = searchParams.get('id')

    if (!portfolioId) {
      return NextResponse.json(
        { error: 'Portfolio ID is required' },
        { status: 400 }
      )
    }

    // Supprimer le portefeuille (soft delete)
    const { error } = await supabase
      .from('user_portfolios')
      .update({ is_active: false })
      .eq('id', portfolioId)
      .eq('user_id', user.id)

    if (error) {
      console.error('Error deleting portfolio:', error)
      return NextResponse.json(
        { error: 'Failed to delete portfolio' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Portfolio deleted successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in portfolio DELETE:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
