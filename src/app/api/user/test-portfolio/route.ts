import { NextRequest, NextResponse } from 'next/server'

/**
 * API Route de TEST pour les portefeuilles utilisateur (sans authentification)
 * GET /api/user/test-portfolio - Récupérer les portefeuilles de test
 * POST /api/user/test-portfolio - Créer un nouveau portefeuille de test
 */

// Données de test en mémoire
let testPortfolios = [
  {
    id: 'portfolio-1',
    user_id: 'test-user',
    name: 'Portfolio Halal Principal',
    description: 'Mon portefeuille principal d\'investissements halal',
    target_amount: 50000,
    risk_tolerance: 'moderate',
    is_active: true,
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T10:00:00Z',
    summary: {
      total_value: 42500,
      total_invested: 40000,
      total_gain_loss: 2500,
      total_gain_loss_percent: 6.25,
      positions_count: 8,
      sharia_compliant_percent: 100
    }
  },
  {
    id: 'portfolio-2',
    user_id: 'test-user',
    name: 'Portfolio Tech Halal',
    description: 'Investissements dans la technologie conforme à la Sharia',
    target_amount: 25000,
    risk_tolerance: 'aggressive',
    is_active: true,
    created_at: '2024-02-01T14:30:00Z',
    updated_at: '2024-02-01T14:30:00Z',
    summary: {
      total_value: 18750,
      total_invested: 17500,
      total_gain_loss: 1250,
      total_gain_loss_percent: 7.14,
      positions_count: 5,
      sharia_compliant_percent: 100
    }
  }
]

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const portfolioId = searchParams.get('id')
    const includeSummary = searchParams.get('includeSummary') === 'true'

    // Simuler un délai réseau
    await new Promise(resolve => setTimeout(resolve, 200))

    if (portfolioId) {
      // Récupérer un portefeuille spécifique
      const portfolio = testPortfolios.find(p => p.id === portfolioId)
      
      if (!portfolio) {
        return NextResponse.json(
          { error: 'Portfolio not found' },
          { status: 404 }
        )
      }

      let result: any = { portfolio }

      if (includeSummary) {
        result.summary = portfolio.summary
      }

      return NextResponse.json({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      })

    } else {
      // Récupérer tous les portefeuilles
      let portfolios = [...testPortfolios]

      if (!includeSummary) {
        portfolios = portfolios.map(({ summary, ...portfolio }) => portfolio)
      }

      return NextResponse.json({
        success: true,
        data: portfolios,
        count: portfolios.length,
        timestamp: new Date().toISOString()
      })
    }

  } catch (error) {
    console.error('Error in test portfolio GET:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, description, targetAmount, riskTolerance } = body

    // Validation
    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      return NextResponse.json(
        { error: 'Portfolio name is required' },
        { status: 400 }
      )
    }

    if (riskTolerance && !['conservative', 'moderate', 'aggressive'].includes(riskTolerance)) {
      return NextResponse.json(
        { error: 'Invalid risk tolerance' },
        { status: 400 }
      )
    }

    // Simuler un délai réseau
    await new Promise(resolve => setTimeout(resolve, 300))

    // Créer un nouveau portefeuille de test
    const newPortfolio = {
      id: `portfolio-${Date.now()}`,
      user_id: 'test-user',
      name: name.trim(),
      description: description || null,
      target_amount: targetAmount || null,
      risk_tolerance: riskTolerance || 'moderate',
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      summary: {
        total_value: 0,
        total_invested: 0,
        total_gain_loss: 0,
        total_gain_loss_percent: 0,
        positions_count: 0,
        sharia_compliant_percent: 100
      }
    }

    testPortfolios.push(newPortfolio)

    return NextResponse.json({
      success: true,
      data: newPortfolio,
      message: 'Portfolio created successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in test portfolio POST:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
