import { NextRequest, NextResponse } from 'next/server'

/**
 * API Route de TEST pour la watchlist utilisateur (sans authentification)
 * GET /api/user/test-watchlist - Récupérer la watchlist de test
 * POST /api/user/test-watchlist - Ajouter une action à la watchlist de test
 * DELETE /api/user/test-watchlist - Supprimer une action de la watchlist de test
 */

// Données de test en mémoire
let testWatchlist = [
  {
    id: 'watch-1',
    user_id: 'test-user',
    symbol: 'AAPL',
    notes: 'Excellente entreprise tech, conforme Sharia',
    alert_enabled: true,
    alert_price_above: 200,
    alert_price_below: 150,
    created_at: '2024-01-10T09:00:00Z',
    updated_at: '2024-01-10T09:00:00Z'
  },
  {
    id: 'watch-2',
    user_id: 'test-user',
    symbol: 'MSFT',
    notes: 'Leader du cloud computing',
    alert_enabled: true,
    alert_price_above: 450,
    alert_price_below: 350,
    created_at: '2024-01-12T14:30:00Z',
    updated_at: '2024-01-12T14:30:00Z'
  },
  {
    id: 'watch-3',
    user_id: 'test-user',
    symbol: 'GOOGL',
    notes: 'Moteur de recherche dominant',
    alert_enabled: false,
    alert_price_above: null,
    alert_price_below: null,
    created_at: '2024-01-15T11:15:00Z',
    updated_at: '2024-01-15T11:15:00Z'
  }
]

export async function GET(request: NextRequest) {
  try {
    // Simuler un délai réseau
    await new Promise(resolve => setTimeout(resolve, 150))

    return NextResponse.json({
      success: true,
      data: testWatchlist,
      count: testWatchlist.length,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in test watchlist GET:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { symbol, notes, alertEnabled, alertPriceAbove, alertPriceBelow } = body

    // Validation
    if (!symbol || typeof symbol !== 'string' || symbol.trim().length === 0) {
      return NextResponse.json(
        { error: 'Symbol is required' },
        { status: 400 }
      )
    }

    const symbolUpper = symbol.trim().toUpperCase()

    // Vérifier si le symbole existe déjà
    const existingItem = testWatchlist.find(item => item.symbol === symbolUpper)
    if (existingItem) {
      return NextResponse.json(
        { error: 'Symbol already in watchlist' },
        { status: 409 }
      )
    }

    // Simuler un délai réseau
    await new Promise(resolve => setTimeout(resolve, 250))

    // Ajouter à la watchlist
    const newWatchlistItem = {
      id: `watch-${Date.now()}`,
      user_id: 'test-user',
      symbol: symbolUpper,
      notes: notes || null,
      alert_enabled: alertEnabled || false,
      alert_price_above: alertPriceAbove || null,
      alert_price_below: alertPriceBelow || null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    testWatchlist.push(newWatchlistItem)

    return NextResponse.json({
      success: true,
      data: newWatchlistItem,
      message: 'Added to watchlist successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in test watchlist POST:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const symbol = searchParams.get('symbol')

    if (!symbol) {
      return NextResponse.json(
        { error: 'Symbol is required' },
        { status: 400 }
      )
    }

    const symbolUpper = symbol.toUpperCase()

    // Simuler un délai réseau
    await new Promise(resolve => setTimeout(resolve, 200))

    // Supprimer de la watchlist
    const initialLength = testWatchlist.length
    testWatchlist = testWatchlist.filter(item => item.symbol !== symbolUpper)

    if (testWatchlist.length === initialLength) {
      return NextResponse.json(
        { error: 'Symbol not found in watchlist' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Removed from watchlist successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in test watchlist DELETE:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
