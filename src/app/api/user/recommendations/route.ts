import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

/**
 * API Route pour gérer les recommandations utilisateur
 * GET /api/user/recommendations - Récupérer les recommandations
 * POST /api/user/recommendations - Générer de nouvelles recommandations
 * PATCH /api/user/recommendations - Marquer comme lu
 * DELETE /api/user/recommendations - Supprimer une recommandation
 */

export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Vérifier l'authentification
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type')
    const unreadOnly = searchParams.get('unreadOnly') === 'true'
    const limit = parseInt(searchParams.get('limit') || '20')

    let query = supabase
      .from('user_recommendations')
      .select(`
        *,
        stocks:symbol (
          symbol,
          name,
          sector,
          is_sharia_compliant,
          sharia_king_since,
          current_price,
          price_change,
          price_change_percent,
          market_cap
        )
      `)
      .eq('user_id', user.id)

    // Filtres
    if (type) {
      query = query.eq('recommendation_type', type)
    }
    if (unreadOnly) {
      query = query.eq('is_read', false)
    }

    // Exclure les recommandations expirées
    query = query.or('expires_at.is.null,expires_at.gt.' + new Date().toISOString())

    const { data: recommendations, error } = await query
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Error fetching recommendations:', error)
      return NextResponse.json(
        { error: 'Failed to fetch recommendations' },
        { status: 500 }
      )
    }

    // Enrichir avec les calculs de performance
    const enrichedRecommendations = recommendations?.map(rec => {
      const stock = rec.stocks
      const currentPrice = stock?.current_price
      const targetPrice = rec.target_price

      let potentialGain = null
      let potentialGainPercent = null

      if (currentPrice && targetPrice) {
        potentialGain = targetPrice - currentPrice
        potentialGainPercent = (potentialGain / currentPrice) * 100
      }

      return {
        ...rec,
        potentialGain,
        potentialGainPercent
      }
    }) || []

    return NextResponse.json({
      success: true,
      data: enrichedRecommendations,
      count: enrichedRecommendations.length,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in recommendations GET:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Vérifier l'authentification
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Générer des recommandations personnalisées
    const { data: result, error } = await supabase
      .rpc('generate_user_recommendations', { p_user_id: user.id })

    if (error) {
      console.error('Error generating recommendations:', error)
      return NextResponse.json(
        { error: 'Failed to generate recommendations' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: `${result} new recommendations generated`,
      data: { count: result },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in recommendations POST:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Vérifier l'authentification
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { recommendationId, isRead } = body

    if (!recommendationId) {
      return NextResponse.json(
        { error: 'Recommendation ID is required' },
        { status: 400 }
      )
    }

    // Mettre à jour le statut de lecture
    const { data: recommendation, error } = await supabase
      .from('user_recommendations')
      .update({ is_read: isRead !== false })
      .eq('id', recommendationId)
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) {
      console.error('Error updating recommendation:', error)
      return NextResponse.json(
        { error: 'Failed to update recommendation' },
        { status: 500 }
      )
    }

    if (!recommendation) {
      return NextResponse.json(
        { error: 'Recommendation not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: recommendation,
      message: 'Recommendation updated',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in recommendations PATCH:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Vérifier l'authentification
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const recommendationId = searchParams.get('id')

    if (!recommendationId) {
      return NextResponse.json(
        { error: 'Recommendation ID is required' },
        { status: 400 }
      )
    }

    // Supprimer la recommandation
    const { error } = await supabase
      .from('user_recommendations')
      .delete()
      .eq('id', recommendationId)
      .eq('user_id', user.id)

    if (error) {
      console.error('Error deleting recommendation:', error)
      return NextResponse.json(
        { error: 'Failed to delete recommendation' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Recommendation deleted',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in recommendations DELETE:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// Route pour marquer toutes les recommandations comme lues
export async function PUT(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Vérifier l'authentification
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Marquer toutes les recommandations comme lues
    const { data, error } = await supabase
      .from('user_recommendations')
      .update({ is_read: true })
      .eq('user_id', user.id)
      .eq('is_read', false)
      .select('id')

    if (error) {
      console.error('Error marking all recommendations as read:', error)
      return NextResponse.json(
        { error: 'Failed to mark recommendations as read' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: `${data?.length || 0} recommendations marked as read`,
      data: { count: data?.length || 0 },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in recommendations PUT:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
