'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import FinancialDashboard from '@/components/FinancialDashboard'
import {
  TestTube,
  CheckCircle,
  XCircle,
  Clock,
  RefreshCw,
  User,
  Star,
  PieChart,
  TrendingUp
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface TestResult {
  name: string
  status: 'pending' | 'success' | 'error'
  message: string
  duration?: number
}

export default function TestUserFeaturesPage() {
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [isRunningTests, setIsRunningTests] = useState(false)
  const [testUserId] = useState('test-user-id') // Pour les tests

  const updateTestResult = (name: string, status: TestResult['status'], message: string, duration?: number) => {
    setTestResults(prev => {
      const existing = prev.find(r => r.name === name)
      if (existing) {
        existing.status = status
        existing.message = message
        existing.duration = duration
        return [...prev]
      } else {
        return [...prev, { name, status, message, duration }]
      }
    })
  }

  const runUserAPITests = async () => {
    setIsRunningTests(true)
    setTestResults([])

    const tests = [
      {
        name: 'API Watchlist GET',
        test: async () => {
          const start = Date.now()
          const response = await fetch('/api/user/test-watchlist')
          const result = await response.json()
          const duration = Date.now() - start

          if (!response.ok) {
            throw new Error(result.error || 'Failed to fetch watchlist')
          }

          return { message: `${result.count || 0} éléments en watchlist`, duration }
        }
      },
      {
        name: 'API Portfolio GET',
        test: async () => {
          const start = Date.now()
          const response = await fetch('/api/user/test-portfolio?includeSummary=true')
          const result = await response.json()
          const duration = Date.now() - start

          if (!response.ok) {
            throw new Error(result.error || 'Failed to fetch portfolios')
          }

          return { message: `${result.count || 0} portefeuilles trouvés`, duration }
        }
      },
      {
        name: 'API Recommendations GET',
        test: async () => {
          const start = Date.now()
          const response = await fetch('/api/user/test-recommendations')
          const result = await response.json()
          const duration = Date.now() - start

          if (!response.ok) {
            throw new Error(result.error || 'Failed to fetch recommendations')
          }

          return { message: `${result.count || 0} recommandations`, duration }
        }
      },
      {
        name: 'API Generate Recommendations',
        test: async () => {
          const start = Date.now()
          const response = await fetch('/api/user/test-recommendations', {
            method: 'POST'
          })
          const result = await response.json()
          const duration = Date.now() - start

          if (!response.ok) {
            throw new Error(result.error || 'Failed to generate recommendations')
          }

          return { message: `${result.data?.count || 0} nouvelles recommandations`, duration }
        }
      },
      {
        name: 'API Add to Watchlist',
        test: async () => {
          const start = Date.now()
          const testSymbol = 'AMZN' // Utiliser un symbole différent pour éviter les conflits
          const response = await fetch('/api/user/test-watchlist', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              symbol: testSymbol,
              notes: 'Test watchlist automatique',
              alertEnabled: true
            })
          })
          const result = await response.json()
          const duration = Date.now() - start

          if (!response.ok && response.status !== 409) { // 409 = déjà existant
            throw new Error(result.error || 'Failed to add to watchlist')
          }

          return { message: response.status === 409 ? 'Déjà en watchlist' : 'Ajouté avec succès', duration }
        }
      },
      {
        name: 'API Create Portfolio',
        test: async () => {
          const start = Date.now()
          const response = await fetch('/api/user/test-portfolio', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              name: `Portfolio Test ${Date.now()}`,
              description: 'Portfolio de test automatique',
              riskTolerance: 'moderate'
            })
          })
          const result = await response.json()
          const duration = Date.now() - start

          if (!response.ok) {
            throw new Error(result.error || 'Failed to create portfolio')
          }

          return { message: `Portfolio "${result.data.name}" créé`, duration }
        }
      }
    ]

    for (const { name, test } of tests) {
      updateTestResult(name, 'pending', 'En cours...')
      
      try {
        const { message, duration } = await test()
        updateTestResult(name, 'success', message, duration)
      } catch (error) {
        updateTestResult(name, 'error', error instanceof Error ? error.message : 'Erreur inconnue')
      }
      
      // Petit délai entre les tests
      await new Promise(resolve => setTimeout(resolve, 500))
    }

    setIsRunningTests(false)
  }

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500 animate-pulse" />
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />
    }
  }

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return 'border-yellow-200 bg-yellow-50'
      case 'success':
        return 'border-green-200 bg-green-50'
      case 'error':
        return 'border-red-200 bg-red-50'
    }
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      <div className="text-center">
        <h1 className="text-4xl font-bold mb-2">Test des Fonctionnalités Utilisateur</h1>
        <p className="text-muted-foreground">
          Testez l'intégration complète des portefeuilles, watchlists et recommandations
        </p>
      </div>

      {/* Tests des APIs */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TestTube className="w-5 h-5" />
            Tests des APIs Utilisateur
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Button 
              onClick={runUserAPITests} 
              disabled={isRunningTests}
              className="flex items-center gap-2"
            >
              <RefreshCw className={cn("w-4 h-4", isRunningTests && "animate-spin")} />
              {isRunningTests ? 'Tests en cours...' : 'Lancer les tests'}
            </Button>
          </div>

          {/* Résultats des tests */}
          {testResults.length > 0 && (
            <div className="space-y-2">
              <h3 className="font-medium">Résultats des tests</h3>
              {testResults.map((result) => (
                <div
                  key={result.name}
                  className={cn("p-3 rounded-lg border", getStatusColor(result.status))}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(result.status)}
                      <span className="font-medium">{result.name}</span>
                    </div>
                    {result.duration && (
                      <Badge variant="outline" className="text-xs">
                        {result.duration}ms
                      </Badge>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">
                    {result.message}
                  </p>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Démonstrations des composants */}
      <Tabs defaultValue="dashboard" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
          <TabsTrigger value="watchlist">Watchlist</TabsTrigger>
          <TabsTrigger value="financial">Données Financières</TabsTrigger>
          <TabsTrigger value="components">Composants</TabsTrigger>
        </TabsList>

        {/* Dashboard utilisateur complet */}
        <TabsContent value="dashboard" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="w-5 h-5" />
                Dashboard Utilisateur Complet
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <p className="text-muted-foreground">
                  Dashboard utilisateur en cours de développement...
                </p>
                <p className="text-sm text-muted-foreground mt-2">
                  Fonctionnalités : Portefeuilles, Watchlist, Recommandations
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Gestionnaire de watchlist */}
        <TabsContent value="watchlist" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="w-5 h-5" />
                Gestionnaire de Watchlist
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <p className="text-muted-foreground">
                  Gestionnaire de watchlist en cours de développement...
                </p>
                <p className="text-sm text-muted-foreground mt-2">
                  Fonctionnalités : Ajouter/Supprimer actions, Alertes de prix
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Dashboard financier */}
        <TabsContent value="financial" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5" />
                Dashboard Financier
              </CardTitle>
            </CardHeader>
            <CardContent>
              <FinancialDashboard
                defaultSymbols={['AAPL', 'MSFT', 'GOOGL', 'AMZN']}
                showSearch={true}
                maxCards={8}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Composants individuels */}
        <TabsContent value="components" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="w-5 h-5" />
                  Ajouter une Position
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-4">
                  <Button className="w-full" disabled>
                    Tester l'ajout de position (En développement)
                  </Button>
                  <p className="text-xs text-muted-foreground mt-2">
                    Composant en cours de développement
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Statistiques</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">APIs testées</span>
                    <span className="font-medium">6</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Composants</span>
                    <span className="font-medium">5</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Hooks</span>
                    <span className="font-medium">3</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Pages</span>
                    <span className="font-medium">3</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Instructions de test</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <p><strong>1. Tests API :</strong> Cliquez sur "Lancer les tests" pour tester toutes les APIs utilisateur</p>
            <p><strong>2. Dashboard :</strong> Explorez le dashboard complet avec portefeuilles, watchlist et recommandations</p>
            <p><strong>3. Watchlist :</strong> Testez l'ajout/suppression d'actions dans la watchlist</p>
            <p><strong>4. Données financières :</strong> Vérifiez les données en temps réel et le cache</p>
            <p><strong>5. Composants :</strong> Testez les dialogs et interactions individuelles</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
