"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Crown,
  TrendingUp,
  TrendingDown,
  Shield,
  DollarSign,
  PieChart,
  BarChart3,
  Download,
  RefreshCw,
  Eye,
  EyeOff
} from 'lucide-react'
import { exportPortfolioToPDF, exportInvestmentPlanToPDF } from '@/lib/pdf-export'

// Données de test pour le portefeuille
const mockPortfolioData = {
  totalValue: 12450,
  monthlyInvestment: 1000,
  totalInvested: 11000,
  totalGain: 1450,
  gainPercentage: 13.18,
  lastUpdate: new Date().toISOString(),
  allocations: [
    {
      symbol: 'AAPL',
      name: 'Apple Inc.',
      amount: 2500,
      percentage: 20.1,
      currentPrice: 175.50,
      shares: 14.24,
      gain: 320,
      gainPercentage: 14.7,
      isShariKing: true,
      sector: 'Technology'
    },
    {
      symbol: 'MSFT',
      name: 'Microsoft Corporation',
      amount: 2200,
      percentage: 17.7,
      currentPrice: 420.30,
      shares: 5.23,
      gain: 280,
      gainPercentage: 14.6,
      isShariKing: true,
      sector: 'Technology'
    },
    {
      symbol: 'GOOGL',
      name: 'Alphabet Inc.',
      amount: 1800,
      percentage: 14.5,
      currentPrice: 165.80,
      shares: 10.86,
      gain: 190,
      gainPercentage: 11.8,
      isShariKing: true,
      sector: 'Technology'
    },
    {
      symbol: 'NVDA',
      name: 'NVIDIA Corporation',
      amount: 1500,
      percentage: 12.0,
      currentPrice: 875.20,
      shares: 1.71,
      gain: 250,
      gainPercentage: 20.0,
      isShariKing: true,
      sector: 'Technology'
    },
    {
      symbol: 'TSLA',
      name: 'Tesla Inc.',
      amount: 1200,
      percentage: 9.6,
      currentPrice: 248.50,
      shares: 4.83,
      gain: 150,
      gainPercentage: 14.3,
      isShariKing: false,
      sector: 'Automotive'
    },
    {
      symbol: 'JNJ',
      name: 'Johnson & Johnson',
      amount: 1000,
      percentage: 8.0,
      currentPrice: 155.20,
      shares: 6.44,
      gain: 80,
      gainPercentage: 8.7,
      isShariKing: true,
      sector: 'Healthcare'
    },
    {
      symbol: 'PG',
      name: 'Procter & Gamble Co.',
      amount: 950,
      percentage: 7.6,
      currentPrice: 165.40,
      shares: 5.74,
      gain: 95,
      gainPercentage: 11.1,
      isShariKing: true,
      sector: 'Consumer Goods'
    },
    {
      symbol: 'KO',
      name: 'The Coca-Cola Company',
      amount: 800,
      percentage: 6.4,
      currentPrice: 62.85,
      shares: 12.73,
      gain: 60,
      gainPercentage: 8.1,
      isShariKing: false,
      sector: 'Beverages'
    },
    {
      symbol: 'CAT',
      name: 'Caterpillar Inc.',
      amount: 500,
      percentage: 4.0,
      currentPrice: 385.60,
      shares: 1.30,
      gain: 25,
      gainPercentage: 5.3,
      isShariKing: false,
      sector: 'Industrial'
    }
  ]
}

// Calcul des répartitions par secteur
const sectorAllocation = mockPortfolioData.allocations.reduce((acc, allocation) => {
  const sector = allocation.sector
  if (!acc[sector]) {
    acc[sector] = { amount: 0, percentage: 0, count: 0 }
  }
  acc[sector].amount += allocation.amount
  acc[sector].percentage += allocation.percentage
  acc[sector].count += 1
  return acc
}, {} as Record<string, { amount: number; percentage: number; count: number }>)

export default function PortfolioPage() {
  const [showAmounts, setShowAmounts] = useState(true)
  const [isLoading, setIsLoading] = useState(false)

  const shariaKingsCount = mockPortfolioData.allocations.filter(a => a.isShariKing).length
  const shariaKingsValue = mockPortfolioData.allocations
    .filter(a => a.isShariKing)
    .reduce((sum, a) => sum + a.amount, 0)

  const refreshPortfolio = async () => {
    setIsLoading(true)
    // Simuler un refresh
    await new Promise(resolve => setTimeout(resolve, 1000))
    setIsLoading(false)
  }

  const handleExportPDF = () => {
    exportPortfolioToPDF(mockPortfolioData, showAmounts)
  }

  const handleExportInvestmentPlan = () => {
    exportInvestmentPlanToPDF(mockPortfolioData, showAmounts)
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold mb-2">Mon Portefeuille Halal</h1>
          <p className="text-muted-foreground">
            Dernière mise à jour: {new Date(mockPortfolioData.lastUpdate).toLocaleString('fr-FR')}
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAmounts(!showAmounts)}
          >
            {showAmounts ? <EyeOff className="h-4 w-4 mr-2" /> : <Eye className="h-4 w-4 mr-2" />}
            {showAmounts ? 'Masquer' : 'Afficher'} les montants
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={refreshPortfolio}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Actualiser
          </Button>
        </div>
      </div>

      {/* Métriques principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Valeur totale</p>
                <p className="text-2xl font-bold">
                  {showAmounts ? `${mockPortfolioData.totalValue.toLocaleString('fr-FR')}€` : '••••••'}
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Gain total</p>
                <p className="text-2xl font-bold text-green-600">
                  {showAmounts ? `+${mockPortfolioData.totalGain.toLocaleString('fr-FR')}€` : '••••••'}
                </p>
                <p className="text-sm text-green-600">+{mockPortfolioData.gainPercentage}%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Investissement mensuel</p>
                <p className="text-2xl font-bold">
                  {showAmounts ? `${mockPortfolioData.monthlyInvestment.toLocaleString('fr-FR')}€` : '••••••'}
                </p>
              </div>
              <BarChart3 className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Sharia Kings</p>
                <p className="text-2xl font-bold">{shariaKingsCount}</p>
                <p className="text-sm text-muted-foreground">
                  {showAmounts ? `${shariaKingsValue.toLocaleString('fr-FR')}€` : '••••••'}
                </p>
              </div>
              <Crown className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Contenu principal */}
      <Tabs defaultValue="holdings" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="holdings">Positions</TabsTrigger>
          <TabsTrigger value="sectors">Secteurs</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
        </TabsList>

        {/* Onglet Positions */}
        <TabsContent value="holdings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="h-5 w-5" />
                Répartition du Portefeuille
              </CardTitle>
              <CardDescription>
                {mockPortfolioData.allocations.length} positions • {shariaKingsCount} Sharia Kings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockPortfolioData.allocations.map((allocation, index) => (
                  <div
                    key={`${allocation.symbol}-${index}`}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex items-center gap-4">
                      <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center text-sm font-medium">
                        {index + 1}
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{allocation.symbol}</span>
                          {allocation.isShariKing && (
                            <Crown className="h-4 w-4 text-yellow-500" title="Sharia King - Halal depuis 10+ ans" />
                          )}
                          <Badge variant="secondary" className="text-xs">
                            {allocation.sector}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">{allocation.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {allocation.shares.toFixed(2)} actions à {allocation.currentPrice}€
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">
                        {showAmounts ? `${allocation.amount.toLocaleString('fr-FR')}€` : '••••••'}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {allocation.percentage.toFixed(1)}%
                      </div>
                      <div className={`text-sm ${allocation.gain >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {allocation.gain >= 0 ? '+' : ''}{showAmounts ? `${allocation.gain}€` : '••••'} 
                        ({allocation.gainPercentage >= 0 ? '+' : ''}{allocation.gainPercentage}%)
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Onglet Secteurs */}
        <TabsContent value="sectors" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Répartition par Secteur</CardTitle>
              <CardDescription>
                Diversification de votre portefeuille halal
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(sectorAllocation)
                  .sort(([,a], [,b]) => b.percentage - a.percentage)
                  .map(([sector, data]) => (
                    <div key={sector} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="font-medium">{sector}</span>
                        <div className="text-right">
                          <div className="font-medium">
                            {showAmounts ? `${data.amount.toLocaleString('fr-FR')}€` : '••••••'}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {data.percentage.toFixed(1)}% • {data.count} position{data.count > 1 ? 's' : ''}
                          </div>
                        </div>
                      </div>
                      <Progress value={data.percentage} className="h-2" />
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Onglet Performance */}
        <TabsContent value="performance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Analyse de Performance</CardTitle>
              <CardDescription>
                Performance détaillée de vos investissements halal
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-medium">Top Performers</h4>
                  {mockPortfolioData.allocations
                    .sort((a, b) => b.gainPercentage - a.gainPercentage)
                    .slice(0, 3)
                    .map((allocation, index) => (
                      <div key={`top-${allocation.symbol}-${index}`} className="flex items-center justify-between p-3 border rounded">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{allocation.symbol}</span>
                          {allocation.isShariKing && <Crown className="h-4 w-4 text-yellow-500" />}
                        </div>
                        <div className="text-green-600 font-medium">
                          +{allocation.gainPercentage}%
                        </div>
                      </div>
                    ))}
                </div>
                <div className="space-y-4">
                  <h4 className="font-medium">Conformité Sharia</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span>Actions halal</span>
                      <Badge variant="secondary">100%</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Sharia Kings</span>
                      <Badge variant="secondary">{shariaKingsCount}/{mockPortfolioData.allocations.length}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Niveau de pureté</span>
                      <Badge variant="secondary">100%</Badge>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Actions */}
      <Card className="mt-8">
        <CardContent className="pt-6">
          <div className="flex flex-wrap gap-3">
            <Button variant="outline" className="flex items-center gap-2" onClick={handleExportPDF}>
              <Download className="h-4 w-4" />
              Exporter Portefeuille PDF
            </Button>
            <Button variant="outline" className="flex items-center gap-2" onClick={handleExportInvestmentPlan}>
              <Download className="h-4 w-4" />
              Plan d'Investissement PDF
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Exporter en Excel
            </Button>
            <Button className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Vérifier la Conformité Sharia
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
