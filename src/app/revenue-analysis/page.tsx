'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import RevenueSankeyChart from '@/components/RevenueSankeyChart'
import { useShariaComplianceComparison } from '@/hooks/useRevenueAnalysis'
import { Search, TrendingUp, AlertTriangle, CheckCircle } from 'lucide-react'

const SAMPLE_COMPANIES = ['AAPL', 'MSFT', 'GOOGL', 'META', 'NVDA', 'TSLA']

export default function RevenueAnalysisPage() {
  const [selectedSymbol, setSelectedSymbol] = useState('AAPL')
  const [searchSymbol, setSearchSymbol] = useState('')
  
  const { comparison, loading, compliantCount, totalCount } = useShariaComplianceComparison(SAMPLE_COMPANIES)

  const handleSearch = () => {
    if (searchSymbol.trim()) {
      setSelectedSymbol(searchSymbol.toUpperCase().trim())
    }
  }

  const formatCurrency = (amount: number) => {
    if (amount >= 1e9) {
      return `${(amount / 1e9).toFixed(1)}B€`
    } else if (amount >= 1e6) {
      return `${(amount / 1e6).toFixed(1)}M€`
    }
    return `${amount.toFixed(0)}€`
  }

  const formatPercent = (percent: number) => {
    return `${percent.toFixed(1)}%`
  }

  const getComplianceBadge = (rating: string) => {
    switch (rating) {
      case 'compliant':
        return <Badge className="bg-green-100 text-green-800">Conforme</Badge>
      case 'questionable':
        return <Badge className="bg-yellow-100 text-yellow-800">Questionable</Badge>
      case 'non-compliant':
        return <Badge className="bg-red-100 text-red-800">Non-conforme</Badge>
      default:
        return <Badge variant="secondary">Inconnu</Badge>
    }
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* En-tête */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold">Analyse des Revenus Sharia</h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
          Analysez en détail les sources de revenus des entreprises pour évaluer leur conformité 
          aux principes islamiques avec des diagrammes de Sankey interactifs.
        </p>
      </div>

      {/* Recherche */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="w-5 h-5" />
            Rechercher une Entreprise
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 max-w-md">
            <Input
              placeholder="Symbole (ex: AAPL, MSFT)"
              value={searchSymbol}
              onChange={(e) => setSearchSymbol(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            />
            <Button onClick={handleSearch}>
              Analyser
            </Button>
          </div>
          <div className="flex flex-wrap gap-2 mt-4">
            <span className="text-sm text-muted-foreground">Exemples :</span>
            {SAMPLE_COMPANIES.map((symbol) => (
              <Button
                key={symbol}
                variant="outline"
                size="sm"
                onClick={() => setSelectedSymbol(symbol)}
                className={selectedSymbol === symbol ? 'bg-primary text-primary-foreground' : ''}
              >
                {symbol}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="analysis" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="analysis">Analyse Détaillée</TabsTrigger>
          <TabsTrigger value="comparison">Comparaison</TabsTrigger>
        </TabsList>

        {/* Analyse détaillée */}
        <TabsContent value="analysis" className="space-y-6">
          <RevenueSankeyChart symbol={selectedSymbol} />
        </TabsContent>

        {/* Comparaison */}
        <TabsContent value="comparison" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5" />
                Comparaison de Conformité Sharia
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                {compliantCount} sur {totalCount} entreprises sont conformes aux critères Sharia
              </p>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin w-8 h-8 border-2 border-primary border-t-transparent rounded-full mx-auto mb-4" />
                  <p className="text-muted-foreground">Chargement de la comparaison...</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {comparison.map((company) => (
                    <div
                      key={company.symbol}
                      className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors cursor-pointer"
                      onClick={() => setSelectedSymbol(company.symbol)}
                    >
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                          {company.isCompliant ? (
                            <CheckCircle className="w-5 h-5 text-green-500" />
                          ) : (
                            <AlertTriangle className="w-5 h-5 text-red-500" />
                          )}
                          <div>
                            <div className="font-medium">{company.symbol}</div>
                            <div className="text-sm text-muted-foreground">{company.companyName}</div>
                          </div>
                        </div>
                        {getComplianceBadge(company.overallRating)}
                      </div>
                      
                      <div className="text-right">
                        <div className="flex items-center gap-4">
                          <div>
                            <div className="text-sm text-muted-foreground">Revenus Halal</div>
                            <div className="font-medium text-green-600">
                              {formatPercent(company.halalPercentage)}
                            </div>
                          </div>
                          <div>
                            <div className="text-sm text-muted-foreground">Revenus Haram</div>
                            <div className="font-medium text-red-600">
                              {formatPercent(company.haramPercentage)}
                            </div>
                          </div>
                          <div>
                            <div className="text-sm text-muted-foreground">Revenus Totaux</div>
                            <div className="font-medium">
                              {formatCurrency(company.totalRevenue)}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Information sur les sources de données */}
      <Card className="bg-muted/50">
        <CardContent className="p-6">
          <h3 className="font-semibold mb-2">Sources de Données</h3>
          <p className="text-sm text-muted-foreground mb-4">
            Les données de revenus proviennent de plusieurs sources fiables :
          </p>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>• <strong>Financial Modeling Prep :</strong> Données détaillées des segments de revenus</li>
            <li>• <strong>Alpha Vantage :</strong> États financiers officiels</li>
            <li>• <strong>SEC EDGAR :</strong> Rapports réglementaires (10-K, 10-Q)</li>
            <li>• <strong>Estimations expertes :</strong> Pour les entreprises sans données publiques détaillées</li>
          </ul>
          <p className="text-xs text-muted-foreground mt-4">
            Dernière mise à jour : {new Date().toLocaleDateString('fr-FR')}
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
