@import "tailwindcss";

:root {
  /* Mode clair - Plus de contraste avec le vert */
  --background: 0 0% 98%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 142 86% 28%; /* Vert plus foncé pour plus de contraste */
  --primary-foreground: 0 0% 100%;
  --secondary: 142 20% 96%; /* Vert très clair */
  --secondary-foreground: 142 86% 15%;
  --muted: 142 15% 95%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 142 30% 92%;
  --accent-foreground: 142 86% 20%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 142 20% 88%;
  --input: 142 20% 92%;
  --ring: 142 86% 28%;
  --radius: 0.75rem;

  /* Couleurs personnalisées pour les animations */
  --gradient-start: 142 86% 95%;
  --gradient-end: 142 30% 85%;
  --glow-primary: 142 86% 28%;
  --glow-secondary: 142 60% 45%;

  /* Charts avec palette verte harmonieuse */
  --chart-1: 142 76% 45%;
  --chart-2: 173 58% 39%;
  --chart-3: 120 40% 35%;
  --chart-4: 160 50% 50%;
  --chart-5: 180 45% 40%;
}

.dark {
  /* Mode sombre - Inspiré de la landing page actuelle */
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 47.4% 8%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 47.4% 8%;
  --popover-foreground: 210 40% 98%;
  --primary: 142 86% 56%; /* Vert lumineux pour le mode sombre */
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 15%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 20%;
  --accent-foreground: 142 86% 56%;
  --destructive: 0 62.8% 50%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 142 86% 56%;

  /* Couleurs personnalisées pour les animations en mode sombre */
  --gradient-start: 222.2 84% 4.9%;
  --gradient-end: 217.2 32.6% 12%;
  --glow-primary: 142 86% 56%;
  --glow-secondary: 142 70% 45%;

  /* Charts avec palette adaptée au mode sombre */
  --chart-1: 142 86% 56%;
  --chart-2: 160 60% 55%;
  --chart-3: 120 50% 45%;
  --chart-4: 180 55% 50%;
  --chart-5: 200 50% 55%;
}

/* Effets de lueur améliorés */
.glow-primary {
  box-shadow: 0 0 20px hsl(var(--glow-primary) / 0.3);
  transition: box-shadow 0.3s ease;
}

.glow-primary:hover {
  box-shadow: 0 0 30px hsl(var(--glow-primary) / 0.5);
}

.dark .glow-primary {
  box-shadow: 0 0 30px hsl(var(--glow-primary) / 0.6);
}

.dark .glow-primary:hover {
  box-shadow: 0 0 50px hsl(var(--glow-primary) / 0.8);
}

.text-glow {
  text-shadow: 0 0 10px hsl(var(--glow-primary) / 0.5);
}

.dark .text-glow {
  text-shadow: 0 0 20px hsl(var(--glow-primary) / 0.9);
}

.border-glow {
  border-color: hsl(var(--glow-primary));
  box-shadow: 0 0 10px hsl(var(--glow-primary) / 0.3);
}

.dark .border-glow {
  box-shadow: 0 0 20px hsl(var(--glow-primary) / 0.7);
}

/* Animations de gradient */
@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.gradient-animated {
  background: linear-gradient(-45deg,
    hsl(var(--gradient-start)),
    hsl(var(--gradient-end)),
    hsl(var(--glow-primary) / 0.1),
    hsl(var(--glow-secondary) / 0.1)
  );
  background-size: 400% 400%;
  animation: gradient-shift 15s ease infinite;
}

/* Animations de vagues */
@keyframes wave {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.wave-animation {
  animation: wave 6s ease-in-out infinite;
}

@keyframes wave-slow {
  0%, 100% { transform: translateX(0px) translateY(0px); }
  33% { transform: translateX(30px) translateY(-10px); }
  66% { transform: translateX(-20px) translateY(10px); }
}

.wave-slow {
  animation: wave-slow 12s ease-in-out infinite;
}

/* Styles de base */
* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Scrollbar personnalisée */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--primary));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--glow-primary));
}

/* Classes utilitaires pour les transitions fluides */
.transition-theme {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Amélioration des focus states */
.focus-ring {
  outline: none;
}

.focus-ring:focus-visible {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

/* Classes pour les états de hover améliorés */
.hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px hsl(var(--primary) / 0.1);
}

.dark .hover-lift:hover {
  box-shadow: 0 15px 35px hsl(var(--primary) / 0.3);
}

/* Animation pour les éléments qui apparaissent */
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Amélioration des cards avec des effets subtils */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px hsl(var(--primary) / 0.1);
}

.dark .card-hover:hover {
  box-shadow: 0 25px 50px hsl(var(--primary) / 0.25);
}

/* Styles pour les badges et indicateurs */
.status-indicator {
  position: relative;
}

.status-indicator::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 8px;
  height: 8px;
  background: hsl(var(--primary));
  border-radius: 50%;
  animation: pulse 2s infinite;
}

/* Amélioration des inputs */
.input-glow:focus {
  box-shadow: 0 0 0 3px hsl(var(--primary) / 0.1);
  border-color: hsl(var(--primary));
}
