// Fonctions pour mapper les sliders de manière non-linéaire
// Permet d'avoir plus de précision sur les petites valeurs

export interface SliderConfig {
  min: number
  max: number
  step: number
  breakpoints: { value: number; percentage: number }[]
}

// Configuration pour les revenus (0 à 50000€)
export const incomeSliderConfig: SliderConfig = {
  min: 0,
  max: 50000,
  step: 50,
  breakpoints: [
    { value: 0, percentage: 0 },
    { value: 1000, percentage: 20 },    // 20% du slider pour 0-1000€
    { value: 2000, percentage: 35 },    // 15% pour 1000-2000€
    { value: 3000, percentage: 45 },    // 10% pour 2000-3000€
    { value: 5000, percentage: 60 },    // 15% pour 3000-5000€
    { value: 10000, percentage: 80 },   // 20% pour 5000-10000€
    { value: 50000, percentage: 100 }   // 20% pour 10000-50000€
  ]
}

// Configuration pour l'épargne (0 à 10000€)
export const savingsSliderConfig: SliderConfig = {
  min: 0,
  max: 10000,
  step: 25,
  breakpoints: [
    { value: 0, percentage: 0 },
    { value: 100, percentage: 15 },     // 15% pour 0-100€
    { value: 300, percentage: 30 },     // 15% pour 100-300€
    { value: 500, percentage: 45 },     // 15% pour 300-500€
    { value: 1000, percentage: 65 },    // 20% pour 500-1000€
    { value: 2000, percentage: 80 },    // 15% pour 1000-2000€
    { value: 10000, percentage: 100 }   // 20% pour 2000-10000€
  ]
}

// Configuration pour le budget d'investissement (0 à 5000€)
export const budgetSliderConfig: SliderConfig = {
  min: 50,
  max: 5000,
  step: 25,
  breakpoints: [
    { value: 50, percentage: 0 },
    { value: 100, percentage: 15 },     // 15% pour 50-100€
    { value: 200, percentage: 30 },     // 15% pour 100-200€
    { value: 500, percentage: 50 },     // 20% pour 200-500€
    { value: 1000, percentage: 70 },    // 20% pour 500-1000€
    { value: 2000, percentage: 85 },    // 15% pour 1000-2000€
    { value: 5000, percentage: 100 }    // 15% pour 2000-5000€
  ]
}

// Configuration pour les montants d'investissement actuels (0 à 100000€)
export const currentInvestmentSliderConfig: SliderConfig = {
  min: 0,
  max: 100000,
  step: 100,
  breakpoints: [
    { value: 0, percentage: 0 },
    { value: 1000, percentage: 20 },    // 20% pour 0-1000€
    { value: 5000, percentage: 40 },    // 20% pour 1000-5000€
    { value: 10000, percentage: 55 },   // 15% pour 5000-10000€
    { value: 25000, percentage: 70 },   // 15% pour 10000-25000€
    { value: 50000, percentage: 85 },   // 15% pour 25000-50000€
    { value: 100000, percentage: 100 }  // 15% pour 50000-100000€
  ]
}

// Convertir une valeur réelle en pourcentage du slider
export function valueToSliderPercentage(value: number, config: SliderConfig): number {
  if (value <= config.min) return 0
  if (value >= config.max) return 100

  // Trouver les deux breakpoints entre lesquels se trouve la valeur
  for (let i = 0; i < config.breakpoints.length - 1; i++) {
    const current = config.breakpoints[i]
    const next = config.breakpoints[i + 1]

    if (value >= current.value && value <= next.value) {
      // Interpolation linéaire entre les deux breakpoints
      const valueRange = next.value - current.value
      const percentageRange = next.percentage - current.percentage
      const valueOffset = value - current.value
      
      return current.percentage + (valueOffset / valueRange) * percentageRange
    }
  }

  return 100
}

// Convertir un pourcentage du slider en valeur réelle
export function sliderPercentageToValue(percentage: number, config: SliderConfig): number {
  if (percentage <= 0) return config.min
  if (percentage >= 100) return config.max

  // Trouver les deux breakpoints entre lesquels se trouve le pourcentage
  for (let i = 0; i < config.breakpoints.length - 1; i++) {
    const current = config.breakpoints[i]
    const next = config.breakpoints[i + 1]

    if (percentage >= current.percentage && percentage <= next.percentage) {
      // Interpolation linéaire entre les deux breakpoints
      const percentageRange = next.percentage - current.percentage
      const valueRange = next.value - current.value
      const percentageOffset = percentage - current.percentage
      
      const rawValue = current.value + (percentageOffset / percentageRange) * valueRange
      
      // Arrondir selon le step
      return Math.round(rawValue / config.step) * config.step
    }
  }

  return config.max
}

// Obtenir la configuration selon le type de question
export function getSliderConfig(questionId: string): SliderConfig {
  switch (questionId) {
    case 'monthlyIncome':
      return incomeSliderConfig
    case 'monthlySavings':
      return savingsSliderConfig
    case 'monthlyBudget':
      return budgetSliderConfig
    case 'currentInvestmentAmount':
      return currentInvestmentSliderConfig
    default:
      // Configuration par défaut linéaire
      return {
        min: 0,
        max: 100,
        step: 1,
        breakpoints: [
          { value: 0, percentage: 0 },
          { value: 100, percentage: 100 }
        ]
      }
  }
}
