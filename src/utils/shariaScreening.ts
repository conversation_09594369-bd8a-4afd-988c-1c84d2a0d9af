import { Stock, ShariaScreeningCriteria, EXCLUDED_SECTORS, EXCLUDED_COMPANIES } from '@/types'

// Critères AAOIFI standards
export const AAOIFI_CRITERIA: ShariaScreeningCriteria = {
  maxDebtRatio: 33, // 33%
  maxNonHalalRevenue: 5, // 5%
  maxLiquidityRatio: 33, // 33%
  excludedSectors: [...EXCLUDED_SECTORS]
}

// Critères stricts pour niveau de pureté élevé
export const STRICT_CRITERIA: ShariaScreeningCriteria = {
  maxDebtRatio: 25, // 25%
  maxNonHalalRevenue: 3, // 3%
  maxLiquidityRatio: 25, // 25%
  excludedSectors: [...EXCLUDED_SECTORS]
}

/**
 * Vérifie si une action est conforme à la Sharia selon les critères donnés
 */
export function isShariCompliant(
  stock: Stock,
  criteria: ShariaScreeningCriteria = AAOIFI_CRITERIA
): boolean {
  // Vérifier les entreprises spécifiquement exclues
  if (EXCLUDED_COMPANIES.includes(stock.symbol as any)) {
    return false
  }

  // Vérifier les secteurs exclus
  if (criteria.excludedSectors.includes(stock.sector)) {
    return false
  }

  // Vérifier les ratios financiers
  if (stock.debtRatio > criteria.maxDebtRatio) {
    return false
  }

  if (stock.nonHalalRevenueRatio > criteria.maxNonHalalRevenue) {
    return false
  }

  if (stock.liquidityRatio > criteria.maxLiquidityRatio) {
    return false
  }

  return true
}

/**
 * Vérifie si une action est un "Sharia King" (conforme depuis 10+ ans)
 */
export function isShariaKing(stock: Stock): boolean {
  if (!stock.shariaKingSince) return false
  
  const kingDate = new Date(stock.shariaKingSince)
  const tenYearsAgo = new Date()
  tenYearsAgo.setFullYear(tenYearsAgo.getFullYear() - 10)
  
  return kingDate <= tenYearsAgo
}

/**
 * Calcule un score de conformité Sharia (0-100)
 */
export function calculateShariaScore(stock: Stock): number {
  let score = 100

  // Pénalité majeure pour les entreprises spécifiquement exclues
  if (EXCLUDED_COMPANIES.includes(stock.symbol as any)) {
    score = 0
  }

  // Pénalité majeure pour les secteurs exclus
  if (EXCLUDED_SECTORS.includes(stock.sector as any)) {
    score = 0
  }

  // Si déjà exclu, retourner 0
  if (score === 0) {
    return 0
  }

  // Pénalités pour les ratios
  const debtPenalty = Math.max(0, (stock.debtRatio - 33) * 2)
  const revenuePenalty = Math.max(0, (stock.nonHalalRevenueRatio - 5) * 10)
  const liquidityPenalty = Math.max(0, (stock.liquidityRatio - 33) * 2)

  score -= debtPenalty + revenuePenalty + liquidityPenalty

  // Bonus pour les Sharia Kings
  if (isShariaKing(stock)) {
    score += 10
  }

  return Math.max(0, Math.min(100, score))
}

/**
 * Filtre les actions selon le niveau de pureté demandé
 */
export function filterByPurityLevel(
  stocks: Stock[],
  purityLevel: number
): Stock[] {
  return stocks.filter(stock => {
    if (purityLevel >= 99) {
      // Très strict : seulement les Sharia Kings
      return isShariaKing(stock) && isShariCompliant(stock, STRICT_CRITERIA)
    } else if (purityLevel >= 97) {
      // Strict : critères renforcés
      return isShariCompliant(stock, STRICT_CRITERIA)
    } else if (purityLevel >= 95) {
      // Standard : critères AAOIFI
      return isShariCompliant(stock, AAOIFI_CRITERIA)
    } else {
      // Minimum acceptable
      return stock.isShariCompliant
    }
  })
}

/**
 * Obtient les critères selon le niveau de pureté
 */
export function getCriteriaByPurityLevel(purityLevel: number): ShariaScreeningCriteria {
  if (purityLevel >= 97) {
    return STRICT_CRITERIA
  }
  return AAOIFI_CRITERIA
}

/**
 * Génère un rapport de conformité pour une action
 */
export function generateComplianceReport(stock: Stock): {
  isCompliant: boolean
  score: number
  issues: string[]
  strengths: string[]
} {
  const issues: string[] = []
  const strengths: string[] = []

  // Vérifier les entreprises spécifiquement exclues
  if (EXCLUDED_COMPANIES.includes(stock.symbol as any)) {
    issues.push(`Entreprise exclue: ${getExclusionReason(stock.symbol)}`)
  }

  // Vérifier les secteurs exclus
  if (EXCLUDED_SECTORS.includes(stock.sector as any)) {
    issues.push(`Secteur exclu: ${stock.sector}`)
  } else {
    strengths.push(`Secteur autorisé: ${stock.sector}`)
  }

  // Vérifier les critères financiers seulement si pas déjà exclu
  if (!EXCLUDED_COMPANIES.includes(stock.symbol as any) && !EXCLUDED_SECTORS.includes(stock.sector as any)) {
    if (stock.debtRatio > 33) {
      issues.push(`Ratio de dette élevé: ${stock.debtRatio.toFixed(1)}% (max 33%)`)
    } else {
      strengths.push(`Ratio de dette acceptable: ${stock.debtRatio.toFixed(1)}%`)
    }

    if (stock.nonHalalRevenueRatio > 5) {
      issues.push(`Revenus non-halal élevés: ${stock.nonHalalRevenueRatio.toFixed(1)}% (max 5%)`)
    } else {
      strengths.push(`Revenus halal: ${(100 - stock.nonHalalRevenueRatio).toFixed(1)}%`)
    }

    if (stock.liquidityRatio > 33) {
      issues.push(`Ratio de liquidité élevé: ${stock.liquidityRatio.toFixed(1)}% (max 33%)`)
    } else {
      strengths.push(`Ratio de liquidité acceptable: ${stock.liquidityRatio.toFixed(1)}%`)
    }

    if (isShariaKing(stock)) {
      strengths.push(`Sharia King depuis ${stock.shariaKingSince}`)
    }
  }

  const isCompliant = isShariCompliant(stock)
  const score = calculateShariaScore(stock)

  return {
    isCompliant,
    score,
    issues,
    strengths
  }
}

/**
 * Retourne la raison de l'exclusion d'une entreprise
 */
export function getExclusionReason(symbol: string): string {
  const exclusionReasons: Record<string, string> = {
    'GOOGL': 'Revenus publicitaires incluant contenu illicite',
    'GOOG': 'Revenus publicitaires incluant contenu illicite',
    'META': 'Revenus publicitaires incluant contenu illicite',
    'FB': 'Revenus publicitaires incluant contenu illicite',
    'SNAP': 'Contenu inapproprié et publicité illicite',
    'TWTR': 'Contenu inapproprié et publicité illicite',
    'X': 'Contenu inapproprié et publicité illicite',
    'PINS': 'Contenu potentiellement inapproprié',
    'ROKU': 'Streaming avec contenu illicite',
    'NFLX': 'Contenu illicite (films/séries)',
    'DIS': 'Contenu potentiellement inapproprié',
    'SPOT': 'Musique avec contenu illicite'
  }

  return exclusionReasons[symbol] || 'Modèle d\'affaires non conforme à la Sharia'
}

/**
 * Vérifie si une entreprise est dans la liste de boycott
 */
export function isBoycotted(stockName: string, boycottList: string[]): boolean {
  return boycottList.some(boycottedCompany => 
    stockName.toLowerCase().includes(boycottedCompany.toLowerCase()) ||
    boycottedCompany.toLowerCase().includes(stockName.toLowerCase())
  )
}

/**
 * Calcule le score de pureté d'un portefeuille
 */
export function calculatePortfolioPurity(stocks: Stock[]): number {
  if (stocks.length === 0) return 0
  
  const totalScore = stocks.reduce((sum, stock) => sum + calculateShariaScore(stock), 0)
  return totalScore / stocks.length
}
