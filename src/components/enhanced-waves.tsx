'use client'

import { useEffect, useRef, useState } from 'react'

interface EnhancedWavesProps {
  className?: string
  intensity?: 'low' | 'medium' | 'high'
  variant?: 'flowing' | 'ripple' | 'aurora'
}

export function EnhancedWaves({ 
  className = '', 
  intensity = 'medium',
  variant = 'flowing'
}: EnhancedWavesProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    if (!mounted) return
    
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    let animationId: number
    let time = 0

    const resizeCanvas = () => {
      canvas.width = window.innerWidth
      canvas.height = window.innerHeight
    }

    const getIntensityMultiplier = () => {
      switch (intensity) {
        case 'low': return 0.5
        case 'medium': return 1
        case 'high': return 1.5
        default: return 1
      }
    }

    const drawFlowingWaves = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      
      const isDark = document.documentElement.classList.contains('dark')
      const intensityMult = getIntensityMultiplier()
      
      // Vagues principales avec bonne visibilité dans les deux modes
      for (let layer = 0; layer < 4; layer++) {
        const alpha = isDark ?
          (0.08 + layer * 0.04) * intensityMult :
          (0.08 + layer * 0.03) * intensityMult
        
        ctx.fillStyle = `rgba(34, 197, 94, ${alpha})`
        ctx.beginPath()
        ctx.moveTo(0, canvas.height)
        
        for (let x = 0; x <= canvas.width; x += 8) {
          const frequency = 0.005 + layer * 0.002
          const amplitude = 60 + layer * 20
          const offset = layer * 50
          const y = canvas.height - 120 - offset + 
                   Math.sin((x + time * (1 + layer * 0.3)) * frequency) * amplitude +
                   Math.sin((x + time * (1.5 + layer * 0.2)) * frequency * 1.5) * (amplitude * 0.5)
          ctx.lineTo(x, y)
        }
        
        ctx.lineTo(canvas.width, canvas.height)
        ctx.closePath()
        ctx.fill()
      }
    }

    const drawRippleWaves = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      const isDark = document.documentElement.classList.contains('dark')
      const intensityMult = getIntensityMultiplier()

      // Cercles concentriques avec points plus gros et flou
      const centerX = canvas.width / 2
      const centerY = canvas.height / 2

      for (let i = 0; i < 8; i++) {
        const radius = 50 + i * 80 + Math.sin(time * 0.01 + i * 0.5) * 30
        const alpha = isDark ?
          (0.1 - i * 0.01) * intensityMult :
          (0.12 - i * 0.01) * intensityMult

        if (alpha > 0) {
          // Effet de flou
          ctx.shadowColor = `rgba(34, 197, 94, ${alpha * 0.8})`
          ctx.shadowBlur = 8 + i * 2

          ctx.strokeStyle = `rgba(34, 197, 94, ${alpha})`
          ctx.lineWidth = 3 + i * 0.5 // Lignes plus épaisses
          ctx.beginPath()
          ctx.arc(centerX, centerY, radius, 0, Math.PI * 2)
          ctx.stroke()

          // Points sur le cercle pour plus de visibilité
          const pointCount = 12 + i * 2
          for (let j = 0; j < pointCount; j++) {
            const angle = (j / pointCount) * Math.PI * 2
            const pointX = centerX + Math.cos(angle) * radius
            const pointY = centerY + Math.sin(angle) * radius

            ctx.fillStyle = `rgba(34, 197, 94, ${alpha * 1.2})`
            ctx.beginPath()
            ctx.arc(pointX, pointY, 2 + i * 0.3, 0, Math.PI * 2) // Points plus gros
            ctx.fill()
          }

          ctx.shadowBlur = 0
        }
      }
    }

    const drawAuroraWaves = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      
      const isDark = document.documentElement.classList.contains('dark')
      const intensityMult = getIntensityMultiplier()
      
      // Vagues aurora avec effet de lueur
      for (let layer = 0; layer < 3; layer++) {
        const alpha = isDark ?
          (0.12 + layer * 0.06) * intensityMult :
          (0.10 + layer * 0.04) * intensityMult
        
        // Gradient pour effet aurora
        const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height)
        gradient.addColorStop(0, `rgba(34, 197, 94, ${alpha})`)
        gradient.addColorStop(0.5, `rgba(16, 185, 129, ${alpha * 0.8})`)
        gradient.addColorStop(1, `rgba(5, 150, 105, ${alpha * 0.6})`)
        
        ctx.fillStyle = gradient
        ctx.beginPath()
        ctx.moveTo(0, canvas.height)
        
        for (let x = 0; x <= canvas.width; x += 6) {
          const y = canvas.height - 100 - layer * 40 + 
                   Math.sin((x + time * (0.8 + layer * 0.4)) * 0.008) * (50 + layer * 15) +
                   Math.cos((x + time * (1.2 + layer * 0.3)) * 0.006) * (30 + layer * 10)
          ctx.lineTo(x, y)
        }
        
        ctx.lineTo(canvas.width, canvas.height)
        ctx.closePath()
        ctx.fill()
      }
      
      // Particules flottantes pour l'effet aurora (visibles dans les deux modes)
      for (let i = 0; i < 15; i++) {
        const x = (i * canvas.width / 15 + time * 0.3) % canvas.width
        const y = canvas.height * 0.3 + Math.sin(time * 0.008 + i * 0.4) * 100
        const size = isDark ?
          1 + Math.sin(time * 0.01 + i * 0.3) * 1 :
          0.8 + Math.sin(time * 0.01 + i * 0.3) * 0.8

        const particleAlpha = isDark ? 0.6 * intensityMult : 0.4 * intensityMult

        ctx.fillStyle = `rgba(34, 197, 94, ${particleAlpha})`
        ctx.shadowColor = `rgba(34, 197, 94, ${isDark ? 0.8 : 0.6})`
        ctx.shadowBlur = isDark ? 10 : 6
        ctx.beginPath()
        ctx.arc(x, y, size, 0, Math.PI * 2)
        ctx.fill()
        ctx.shadowBlur = 0
      }
    }

    const animate = () => {
      time += 1
      
      switch (variant) {
        case 'flowing':
          drawFlowingWaves()
          break
        case 'ripple':
          drawRippleWaves()
          break
        case 'aurora':
          drawAuroraWaves()
          break
      }
      
      animationId = requestAnimationFrame(animate)
    }

    resizeCanvas()
    animate()

    window.addEventListener('resize', resizeCanvas)

    return () => {
      window.removeEventListener('resize', resizeCanvas)
      cancelAnimationFrame(animationId)
    }
  }, [variant, intensity, mounted])

  if (!mounted) {
    return null
  }

  return (
    <canvas
      ref={canvasRef}
      className={`fixed inset-0 pointer-events-none z-0 ${className}`}
      style={{ mixBlendMode: 'screen' }}
    />
  )
}

// Composant combiné pour des effets multiples
export function CombinedEnhancedWaves({ className = '' }: { className?: string }) {
  return (
    <div className={`fixed inset-0 pointer-events-none z-0 ${className}`}>
      <EnhancedWaves variant="flowing" intensity="low" />
      <EnhancedWaves variant="aurora" intensity="medium" />
    </div>
  )
}
