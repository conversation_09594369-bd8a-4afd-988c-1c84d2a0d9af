'use client'

import { useEffect, useState } from 'react'

interface TintedSectionBackgroundProps {
  className?: string
  tintColor?: 'primary' | 'secondary' | 'accent'
  intensity?: 'subtle' | 'medium' | 'strong'
}

export function TintedSectionBackground({ 
  className = '',
  tintColor = 'primary',
  intensity = 'subtle'
}: TintedSectionBackgroundProps) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return null
  }

  const getTintIntensity = () => {
    switch (intensity) {
      case 'subtle': return { from: '3', via: '2', to: '5' }
      case 'medium': return { from: '5', via: '3', to: '8' }
      case 'strong': return { from: '8', via: '5', to: '12' }
      default: return { from: '3', via: '2', to: '5' }
    }
  }

  const getTintColorClass = () => {
    const intensities = getTintIntensity()
    switch (tintColor) {
      case 'primary':
        return `from-primary/${intensities.from} via-primary/${intensities.via} to-primary/${intensities.to}`
      case 'secondary':
        return `from-secondary/${intensities.from} via-secondary/${intensities.via} to-secondary/${intensities.to}`
      case 'accent':
        return `from-accent/${intensities.from} via-accent/${intensities.via} to-accent/${intensities.to}`
      default:
        return `from-primary/${intensities.from} via-primary/${intensities.via} to-primary/${intensities.to}`
    }
  }

  return (
    <div className={`absolute inset-0 ${className}`}>
      {/* Couche de teinte colorée */}
      <div className={`absolute inset-0 bg-gradient-to-br ${getTintColorClass()}`} />
      
      {/* Couche de mélange avec le fond */}
      <div className="absolute inset-0 bg-gradient-to-br from-background/60 via-muted/40 to-background/80" />
      
      {/* Effet de texture subtile */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-primary/5 to-transparent animate-pulse" 
             style={{ animationDuration: '8s' }} />
      </div>
    </div>
  )
}

// Variantes prédéfinies
export function GreenTintedBackground({ className = '' }: { className?: string }) {
  return (
    <TintedSectionBackground 
      tintColor="primary" 
      intensity="subtle" 
      className={className}
    />
  )
}

export function MediumGreenTintedBackground({ className = '' }: { className?: string }) {
  return (
    <TintedSectionBackground 
      tintColor="primary" 
      intensity="medium" 
      className={className}
    />
  )
}
