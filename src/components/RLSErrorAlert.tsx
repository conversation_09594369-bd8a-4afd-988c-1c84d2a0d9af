'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

export default function RLSErrorAlert() {
  return (
    <Card className="border-red-200 bg-red-50 mb-6">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2 text-red-800">
          <span>🚨</span>
          <span>Erreur 406 - Configuration RLS requise</span>
        </CardTitle>
        <CardDescription className="text-red-700">
          Les politiques de sécurité (RLS) ne sont pas correctement configurées dans Supabase.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="bg-red-100 p-4 rounded-lg">
          <h4 className="font-medium text-red-800 mb-2">Solution rapide :</h4>
          <ol className="list-decimal list-inside space-y-1 text-sm text-red-700">
            <li><PERSON><PERSON> dans Supabase Dashboard → SQL Editor</li>
            <li>Ex<PERSON>cuter le script <code className="bg-red-200 px-1 rounded">fix-rls-policies.sql</code></li>
            <li>Redémarrer l'application Next.js</li>
          </ol>
        </div>

        <div className="bg-yellow-100 p-3 rounded-lg">
          <p className="text-sm text-yellow-800">
            <strong>Diagnostic :</strong> Exécuter <code className="bg-yellow-200 px-1 rounded">diagnostic-rls.sql</code> 
            pour identifier le problème exact.
          </p>
        </div>

        <div className="text-xs text-red-600">
          <p><strong>Fichiers disponibles :</strong></p>
          <ul className="mt-1 space-y-1">
            <li>• <code>fix-rls-policies.sql</code> - Correction des politiques RLS</li>
            <li>• <code>diagnostic-rls.sql</code> - Diagnostic du problème</li>
            <li>• <code>SETUP_DATABASE.md</code> - Guide complet</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}
