'use client'

import { useEffect, useRef, useState } from 'react'
import { motion } from 'framer-motion'

interface AnimatedChartProps {
  type: 'line' | 'bar' | 'pie' | 'area'
  data?: number[]
  className?: string
  title?: string
  color?: 'primary' | 'secondary' | 'accent'
}

export function AnimatedChart({ 
  type, 
  data = [65, 78, 45, 89, 67, 82, 91, 76], 
  className = '',
  title = '',
  color = 'primary'
}: AnimatedChartProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    if (!mounted) return
    
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    let animationId: number
    let progress = 0

    const resizeCanvas = () => {
      const rect = canvas.getBoundingClientRect()
      canvas.width = rect.width * window.devicePixelRatio
      canvas.height = rect.height * window.devicePixelRatio
      ctx.scale(window.devicePixelRatio, window.devicePixelRatio)
    }

    const getColor = (alpha = 1) => {
      const isDark = document.documentElement.classList.contains('dark')
      switch (color) {
        case 'primary':
          return isDark ? `rgba(34, 197, 94, ${alpha})` : `rgba(22, 163, 74, ${alpha})`
        case 'secondary':
          return isDark ? `rgba(156, 163, 175, ${alpha})` : `rgba(107, 114, 128, ${alpha})`
        case 'accent':
          return isDark ? `rgba(59, 130, 246, ${alpha})` : `rgba(37, 99, 235, ${alpha})`
        default:
          return isDark ? `rgba(34, 197, 94, ${alpha})` : `rgba(22, 163, 74, ${alpha})`
      }
    }

    const drawLineChart = () => {
      const width = canvas.width / window.devicePixelRatio
      const height = canvas.height / window.devicePixelRatio
      const padding = 20
      const chartWidth = width - padding * 2
      const chartHeight = height - padding * 2

      ctx.clearRect(0, 0, width, height)

      // Grille de fond avec effet shader
      ctx.strokeStyle = getColor(0.1)
      ctx.lineWidth = 1
      for (let i = 0; i <= 5; i++) {
        const y = padding + (chartHeight / 5) * i
        ctx.beginPath()
        ctx.moveTo(padding, y)
        ctx.lineTo(width - padding, y)
        ctx.stroke()
      }

      // Ligne principale avec gradient
      const gradient = ctx.createLinearGradient(0, 0, 0, height)
      gradient.addColorStop(0, getColor(0.8))
      gradient.addColorStop(1, getColor(0.2))

      ctx.strokeStyle = gradient
      ctx.lineWidth = 3
      ctx.beginPath()

      data.forEach((value, index) => {
        const x = padding + (chartWidth / (data.length - 1)) * index
        const y = padding + chartHeight - (value / 100) * chartHeight * progress
        
        if (index === 0) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      })

      ctx.stroke()

      // Points avec effet de lueur
      data.forEach((value, index) => {
        const x = padding + (chartWidth / (data.length - 1)) * index
        const y = padding + chartHeight - (value / 100) * chartHeight * progress
        
        // Lueur
        ctx.shadowColor = getColor(0.6)
        ctx.shadowBlur = 10
        ctx.fillStyle = getColor(1)
        ctx.beginPath()
        ctx.arc(x, y, 4, 0, Math.PI * 2)
        ctx.fill()
        ctx.shadowBlur = 0
      })
    }

    const drawBarChart = () => {
      const width = canvas.width / window.devicePixelRatio
      const height = canvas.height / window.devicePixelRatio
      const padding = 20
      const chartWidth = width - padding * 2
      const chartHeight = height - padding * 2
      const barWidth = chartWidth / data.length * 0.8

      ctx.clearRect(0, 0, width, height)

      data.forEach((value, index) => {
        const x = padding + (chartWidth / data.length) * index + (chartWidth / data.length - barWidth) / 2
        const barHeight = (value / 100) * chartHeight * progress
        const y = height - padding - barHeight

        // Gradient pour chaque barre
        const gradient = ctx.createLinearGradient(0, y, 0, y + barHeight)
        gradient.addColorStop(0, getColor(0.9))
        gradient.addColorStop(1, getColor(0.4))

        ctx.fillStyle = gradient
        ctx.fillRect(x, y, barWidth, barHeight)

        // Effet de lueur sur le dessus
        ctx.shadowColor = getColor(0.8)
        ctx.shadowBlur = 5
        ctx.fillStyle = getColor(1)
        ctx.fillRect(x, y, barWidth, 2)
        ctx.shadowBlur = 0
      })
    }

    const drawPieChart = () => {
      const width = canvas.width / window.devicePixelRatio
      const height = canvas.height / window.devicePixelRatio
      const centerX = width / 2
      const centerY = height / 2
      const radius = Math.min(width, height) / 2 - 20

      ctx.clearRect(0, 0, width, height)

      const total = data.reduce((sum, value) => sum + value, 0)
      let currentAngle = -Math.PI / 2

      data.forEach((value, index) => {
        const sliceAngle = (value / total) * 2 * Math.PI * progress
        
        // Gradient radial pour chaque section
        const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius)
        gradient.addColorStop(0, getColor(0.8))
        gradient.addColorStop(1, getColor(0.3))

        ctx.fillStyle = gradient
        ctx.beginPath()
        ctx.moveTo(centerX, centerY)
        ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle)
        ctx.closePath()
        ctx.fill()

        // Bordure avec lueur
        ctx.strokeStyle = getColor(1)
        ctx.lineWidth = 2
        ctx.shadowColor = getColor(0.6)
        ctx.shadowBlur = 8
        ctx.stroke()
        ctx.shadowBlur = 0

        currentAngle += sliceAngle
      })
    }

    const drawAreaChart = () => {
      const width = canvas.width / window.devicePixelRatio
      const height = canvas.height / window.devicePixelRatio
      const padding = 20
      const chartWidth = width - padding * 2
      const chartHeight = height - padding * 2

      ctx.clearRect(0, 0, width, height)

      // Zone remplie avec gradient
      const gradient = ctx.createLinearGradient(0, padding, 0, height - padding)
      gradient.addColorStop(0, getColor(0.6))
      gradient.addColorStop(1, getColor(0.1))

      ctx.fillStyle = gradient
      ctx.beginPath()
      ctx.moveTo(padding, height - padding)

      data.forEach((value, index) => {
        const x = padding + (chartWidth / (data.length - 1)) * index
        const y = padding + chartHeight - (value / 100) * chartHeight * progress
        ctx.lineTo(x, y)
      })

      ctx.lineTo(width - padding, height - padding)
      ctx.closePath()
      ctx.fill()

      // Ligne du dessus
      ctx.strokeStyle = getColor(1)
      ctx.lineWidth = 2
      ctx.beginPath()
      data.forEach((value, index) => {
        const x = padding + (chartWidth / (data.length - 1)) * index
        const y = padding + chartHeight - (value / 100) * chartHeight * progress
        
        if (index === 0) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      })
      ctx.stroke()
    }

    const animate = () => {
      progress = Math.min(progress + 0.02, 1)
      
      switch (type) {
        case 'line':
          drawLineChart()
          break
        case 'bar':
          drawBarChart()
          break
        case 'pie':
          drawPieChart()
          break
        case 'area':
          drawAreaChart()
          break
      }
      
      if (progress < 1) {
        animationId = requestAnimationFrame(animate)
      }
    }

    resizeCanvas()
    animate()

    window.addEventListener('resize', resizeCanvas)

    return () => {
      window.removeEventListener('resize', resizeCanvas)
      cancelAnimationFrame(animationId)
    }
  }, [type, data, color, mounted])

  if (!mounted) {
    return null
  }

  return (
    <div className={`relative ${className}`}>
      {title && (
        <h3 className="text-sm font-medium text-muted-foreground mb-2 text-center">
          {title}
        </h3>
      )}
      <canvas
        ref={canvasRef}
        className="w-full h-full"
        style={{ filter: 'drop-shadow(0 4px 8px rgba(0,0,0,0.1))' }}
      />
    </div>
  )
}

// Composant pour afficher plusieurs graphiques
export function ChartsGrid({ className = '' }: { className?: string }) {
  return (
    <div className={className}>
      {/* Graphique principal large */}
      <motion.div
        className="bg-card/80 backdrop-blur-sm p-6 rounded-xl border border-border mb-8"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
      >
        <h3 className="text-xl font-semibold text-foreground mb-4 text-center">
          Performance Portfolio Halal vs Marché Traditionnel
        </h3>
        <AnimatedChart
          type="area"
          data={[100, 105, 112, 108, 118, 125, 132, 128, 138, 145, 152, 148]}
          className="h-64"
          color="primary"
        />
      </motion.div>

      {/* Grille de petits graphiques */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
        <motion.div
          className="bg-card/80 backdrop-blur-sm p-4 rounded-xl border border-border hover:shadow-lg transition-all duration-300"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          viewport={{ once: true }}
        >
          <AnimatedChart
            type="line"
            title="Performance Halal"
            data={[45, 67, 78, 89, 76, 92, 85, 94]}
            className="h-24"
            color="primary"
          />
        </motion.div>

        <motion.div
          className="bg-card/80 backdrop-blur-sm p-4 rounded-xl border border-border hover:shadow-lg transition-all duration-300"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <AnimatedChart
            type="bar"
            title="Secteurs Conformes"
            data={[78, 65, 89, 72, 84, 91]}
            className="h-24"
            color="secondary"
          />
        </motion.div>

        <motion.div
          className="bg-card/80 backdrop-blur-sm p-4 rounded-xl border border-border hover:shadow-lg transition-all duration-300"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
        >
          <AnimatedChart
            type="pie"
            title="Répartition Portfolio"
            data={[35, 25, 20, 20]}
            className="h-24"
            color="accent"
          />
        </motion.div>

        <motion.div
          className="bg-card/80 backdrop-blur-sm p-4 rounded-xl border border-border hover:shadow-lg transition-all duration-300"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <AnimatedChart
            type="area"
            title="Volatilité Réduite"
            data={[30, 45, 60, 75, 65, 80, 90, 85]}
            className="h-24"
            color="primary"
          />
        </motion.div>
      </div>
    </div>
  )
}
