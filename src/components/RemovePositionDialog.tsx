'use client'

import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON><PERSON>riangle, Trash2 } from 'lucide-react'
import { cn } from '@/lib/utils'

interface RemovePositionDialogProps {
  position: {
    id: string
    symbol: string
    shares: number
    averagePrice: number
    currentValue?: number
    gainLoss?: number
    gainLossPercent?: number
    stockInfo?: {
      name?: string
      isShariCompliant?: boolean
      shariaKingSince?: string
    }
  }
  onRemove: (positionId: string) => Promise<void>
  trigger?: React.ReactNode
}

export function RemovePositionDialog({ position, onRemove, trigger }: RemovePositionDialogProps) {
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount)
  }

  const formatPercent = (percent: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'percent',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(percent / 100)
  }

  const handleRemove = async () => {
    setLoading(true)
    try {
      await onRemove(position.id)
      setOpen(false)
    } catch (error) {
      console.error('Error removing position:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button
            variant="destructive"
            size="sm"
            className="h-6 w-6 p-0"
          >
            <Trash2 className="w-3 h-3" />
          </Button>
        )}
      </DialogTrigger>
      
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="w-5 h-5" />
            Supprimer la position
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Informations sur la position */}
          <div className="bg-muted/50 rounded-lg p-4 space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="font-bold text-lg">{position.symbol}</span>
                {position.stockInfo?.isShariCompliant && (
                  <Badge variant="secondary" className="text-xs">
                    Halal
                  </Badge>
                )}
              </div>
              {position.gainLossPercent !== undefined && (
                <Badge variant={position.gainLoss && position.gainLoss >= 0 ? "default" : "destructive"}>
                  {formatPercent(position.gainLossPercent)}
                </Badge>
              )}
            </div>

            {position.stockInfo?.name && (
              <p className="text-sm text-muted-foreground">
                {position.stockInfo.name}
              </p>
            )}

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Actions détenues</span>
                <p className="font-medium">{position.shares}</p>
              </div>
              <div>
                <span className="text-muted-foreground">Prix moyen</span>
                <p className="font-medium">{formatCurrency(position.averagePrice)}</p>
              </div>
              <div>
                <span className="text-muted-foreground">Valeur actuelle</span>
                <p className="font-medium">{formatCurrency(position.currentValue || 0)}</p>
              </div>
              <div>
                <span className="text-muted-foreground">Gain/Perte</span>
                <p className={cn(
                  "font-medium",
                  position.gainLoss && position.gainLoss >= 0 ? "text-green-600" : "text-red-600"
                )}>
                  {formatCurrency(position.gainLoss || 0)}
                </p>
              </div>
            </div>
          </div>

          {/* Avertissement */}
          <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="w-5 h-5 text-destructive mt-0.5 flex-shrink-0" />
              <div className="space-y-1">
                <p className="font-medium text-destructive">
                  Attention : Cette action est irréversible
                </p>
                <p className="text-sm text-muted-foreground">
                  La suppression de cette position effacera définitivement toutes les données associées. 
                  Vous devrez la recréer manuellement si vous souhaitez la suivre à nouveau.
                </p>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={loading}
            >
              Annuler
            </Button>
            <Button
              variant="destructive"
              onClick={handleRemove}
              disabled={loading}
              className="flex items-center gap-2"
            >
              {loading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Suppression...
                </>
              ) : (
                <>
                  <Trash2 className="w-4 h-4" />
                  Supprimer définitivement
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default RemovePositionDialog
