'use client'

import { useEffect, useRef, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { RevenueBreakdown, SankeyData } from '@/types'
import { getRevenueBreakdown, generateSankeyData } from '@/lib/revenue-analysis'
import { TrendingUp, Download, Info, AlertTriangle } from 'lucide-react'

interface RevenueSankeyChartProps {
  symbol: string
  className?: string
}

export function RevenueSankeyChart({ symbol, className }: RevenueSankeyChartProps) {
  const [revenueData, setRevenueData] = useState<RevenueBreakdown | null>(null)
  const [sankeyData, setSankeyData] = useState<SankeyData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const chartRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    loadRevenueData()
  }, [symbol])

  const loadRevenueData = async () => {
    setLoading(true)
    setError(null)

    try {
      const data = await getRevenueBreakdown(symbol)
      if (data) {
        setRevenueData(data)
        setSankeyData(generateSankeyData(data))
      } else {
        setError('Aucune donnée de revenus disponible pour cette entreprise')
      }
    } catch (err) {
      setError('Erreur lors du chargement des données de revenus')
      console.error('Error loading revenue data:', err)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    if (amount >= 1e9) {
      return `${(amount / 1e9).toFixed(1)}B€`
    } else if (amount >= 1e6) {
      return `${(amount / 1e6).toFixed(1)}M€`
    } else if (amount >= 1e3) {
      return `${(amount / 1e3).toFixed(1)}K€`
    }
    return `${amount.toFixed(0)}€`
  }

  const formatPercent = (percent: number) => {
    return `${percent.toFixed(1)}%`
  }

  const getComplianceColor = (rating: string) => {
    switch (rating) {
      case 'compliant': return 'text-green-600'
      case 'questionable': return 'text-yellow-600'
      case 'non-compliant': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getComplianceBadge = (rating: string) => {
    switch (rating) {
      case 'compliant': return <Badge className="bg-green-100 text-green-800">Conforme</Badge>
      case 'questionable': return <Badge className="bg-yellow-100 text-yellow-800">Questionable</Badge>
      case 'non-compliant': return <Badge className="bg-red-100 text-red-800">Non-conforme</Badge>
      default: return <Badge variant="secondary">Inconnu</Badge>
    }
  }

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          <div className="animate-spin w-8 h-8 border-2 border-primary border-t-transparent rounded-full mx-auto mb-4" />
          <p className="text-muted-foreground">Chargement de l'analyse des revenus...</p>
        </CardContent>
      </Card>
    )
  }

  if (error || !revenueData) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          <AlertTriangle className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Données non disponibles</h3>
          <p className="text-muted-foreground mb-4">
            {error || 'Impossible de charger les données de revenus pour cette entreprise'}
          </p>
          <Button onClick={loadRevenueData} variant="outline">
            Réessayer
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* En-tête avec résumé */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5" />
                Analyse des Revenus - {revenueData.companyName}
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Année fiscale {revenueData.fiscalYear} • {formatCurrency(revenueData.totalRevenue)} de revenus totaux
              </p>
            </div>
            <div className="text-right">
              {getComplianceBadge(revenueData.shariaCompliance.overallRating)}
              <Button variant="outline" size="sm" className="ml-2">
                <Download className="w-4 h-4 mr-2" />
                Exporter
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {formatPercent(revenueData.shariaCompliance.halalPercentage)}
              </div>
              <div className="text-sm text-green-700">Revenus Halal</div>
              <div className="text-xs text-muted-foreground">
                {formatCurrency(revenueData.totalRevenue * revenueData.shariaCompliance.halalPercentage / 100)}
              </div>
            </div>
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">
                {formatPercent(revenueData.shariaCompliance.questionablePercentage)}
              </div>
              <div className="text-sm text-yellow-700">Questionable</div>
              <div className="text-xs text-muted-foreground">
                {formatCurrency(revenueData.totalRevenue * revenueData.shariaCompliance.questionablePercentage / 100)}
              </div>
            </div>
            <div className="text-center p-4 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">
                {formatPercent(revenueData.shariaCompliance.haramPercentage)}
              </div>
              <div className="text-sm text-red-700">Revenus Haram</div>
              <div className="text-xs text-muted-foreground">
                {formatCurrency(revenueData.totalRevenue * revenueData.shariaCompliance.haramPercentage / 100)}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Diagramme de Sankey (placeholder pour maintenant) */}
      <Card>
        <CardHeader>
          <CardTitle>Flux des Revenus</CardTitle>
          <p className="text-sm text-muted-foreground">
            Visualisation des sources de revenus et de leur classification Sharia
          </p>
        </CardHeader>
        <CardContent>
          <div ref={chartRef} className="h-96 bg-muted/20 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <Info className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                Diagramme de Sankey en cours de développement
              </p>
              <p className="text-sm text-muted-foreground mt-2">
                Utilisez la bibliothèque D3.js ou Plotly.js pour l'implémentation
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Détail des segments */}
      <Card>
        <CardHeader>
          <CardTitle>Détail des Segments de Revenus</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {revenueData.segments
              .sort((a, b) => b.amount - a.amount)
              .map((segment, index) => (
                <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className={`w-3 h-3 rounded-full ${
                      segment.category === 'halal' ? 'bg-green-500' :
                      segment.category === 'questionable' ? 'bg-yellow-500' : 'bg-red-500'
                    }`} />
                    <div>
                      <div className="font-medium">{segment.name}</div>
                      {segment.description && (
                        <div className="text-sm text-muted-foreground">{segment.description}</div>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">{formatCurrency(segment.amount)}</div>
                    <div className="text-sm text-muted-foreground">{formatPercent(segment.percentage)}</div>
                  </div>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>

      {/* Avertissement pour les entreprises non-conformes */}
      {revenueData.shariaCompliance.overallRating === 'non-compliant' && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            <strong>Attention :</strong> Cette entreprise a plus de 5% de revenus provenant de sources non-halal.
            Elle ne respecte pas les critères AAOIFI pour l'investissement islamique.
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}

export default RevenueSankeyChart
