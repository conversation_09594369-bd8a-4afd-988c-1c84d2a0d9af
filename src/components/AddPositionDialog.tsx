'use client'

import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogT<PERSON>le, DialogTrigger } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useStockSearch } from '@/hooks/useFinancialData'
import { useUserPortfolio } from '@/hooks/useUserPortfolio'
import { Search, Plus, CheckCircle, Crown, AlertTriangle } from 'lucide-react'
import { cn } from '@/lib/utils'

interface AddPositionDialogProps {
  portfolioId: string
  trigger?: React.ReactNode
}

export function AddPositionDialog({ portfolioId, trigger }: AddPositionDialogProps) {
  const [open, setOpen] = useState(false)
  const [selectedStock, setSelectedStock] = useState<any>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [shares, setShares] = useState('')
  const [averagePrice, setAveragePrice] = useState('')
  const [targetAllocation, setTargetAllocation] = useState('')
  const [loading, setLoading] = useState(false)

  const { results: searchResults, loading: searchLoading, search } = useStockSearch()
  const { addPosition } = useUserPortfolio()

  // Recherche avec debounce
  const handleSearch = (query: string) => {
    setSearchQuery(query)
    if (query.length >= 2) {
      search(query)
    }
  }

  // Sélectionner une action
  const handleSelectStock = (stock: any) => {
    setSelectedStock(stock)
    setSearchQuery(stock.symbol)
  }

  // Ajouter la position
  const handleAddPosition = async () => {
    if (!selectedStock || !shares || !averagePrice) return

    setLoading(true)
    try {
      const success = await addPosition({
        portfolioId,
        symbol: selectedStock.symbol,
        shares: parseFloat(shares),
        averagePrice: parseFloat(averagePrice),
        targetAllocationPercent: targetAllocation ? parseFloat(targetAllocation) : undefined
      })

      if (success) {
        // Réinitialiser le formulaire
        setSelectedStock(null)
        setSearchQuery('')
        setShares('')
        setAveragePrice('')
        setTargetAllocation('')
        setOpen(false)
      }
    } catch (error) {
      console.error('Error adding position:', error)
    } finally {
      setLoading(false)
    }
  }

  // Calculer la valeur totale
  const totalValue = shares && averagePrice 
    ? parseFloat(shares) * parseFloat(averagePrice) 
    : 0

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button className="flex items-center gap-2">
            <Plus className="w-4 h-4" />
            Ajouter une position
          </Button>
        )}
      </DialogTrigger>
      
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Ajouter une position au portefeuille</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Recherche d'action */}
          <div className="space-y-2">
            <Label>Rechercher une action</Label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Nom ou symbole de l'action (ex: AAPL, Apple)"
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Résultats de recherche */}
            {searchQuery.length >= 2 && !selectedStock && (
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {searchLoading ? (
                  <div className="text-center py-4 text-muted-foreground">
                    Recherche en cours...
                  </div>
                ) : searchResults.length > 0 ? (
                  searchResults.map((stock) => (
                    <Card 
                      key={stock.symbol} 
                      className="cursor-pointer hover:bg-muted/50 transition-colors"
                      onClick={() => handleSelectStock(stock)}
                    >
                      <CardContent className="p-3">
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{stock.symbol}</span>
                              {stock.sector && (
                                <Badge variant="outline" className="text-xs">
                                  {stock.sector}
                                </Badge>
                              )}
                            </div>
                            <div className="text-sm text-muted-foreground truncate">
                              {stock.name}
                            </div>
                          </div>
                          <div className="flex items-center gap-1">
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <span className="text-xs text-green-600">Sharia</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                ) : (
                  <div className="text-center py-4 text-muted-foreground">
                    Aucun résultat trouvé
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Action sélectionnée */}
          {selectedStock && (
            <Card className="border-primary/50 bg-primary/5">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="font-bold text-lg">{selectedStock.symbol}</span>
                      <Badge variant="default">Sélectionné</Badge>
                      <CheckCircle className="w-4 h-4 text-green-500" />
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {selectedStock.name}
                    </div>
                    {selectedStock.sector && (
                      <Badge variant="outline" className="mt-1">
                        {selectedStock.sector}
                      </Badge>
                    )}
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setSelectedStock(null)
                      setSearchQuery('')
                    }}
                  >
                    Changer
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Détails de la position */}
          {selectedStock && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="shares">Nombre d'actions *</Label>
                <Input
                  id="shares"
                  type="number"
                  placeholder="100"
                  value={shares}
                  onChange={(e) => setShares(e.target.value)}
                  min="0"
                  step="0.0001"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="averagePrice">Prix moyen d'achat ($) *</Label>
                <Input
                  id="averagePrice"
                  type="number"
                  placeholder="150.00"
                  value={averagePrice}
                  onChange={(e) => setAveragePrice(e.target.value)}
                  min="0"
                  step="0.01"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="targetAllocation">Allocation cible (%)</Label>
                <Input
                  id="targetAllocation"
                  type="number"
                  placeholder="5.0"
                  value={targetAllocation}
                  onChange={(e) => setTargetAllocation(e.target.value)}
                  min="0"
                  max="100"
                  step="0.1"
                />
                <div className="text-xs text-muted-foreground">
                  Optionnel : pourcentage du portefeuille
                </div>
              </div>

              <div className="space-y-2">
                <Label>Valeur totale</Label>
                <div className="text-2xl font-bold text-primary">
                  ${totalValue.toLocaleString('en-US', { minimumFractionDigits: 2 })}
                </div>
              </div>
            </div>
          )}

          {/* Validation et avertissements */}
          {selectedStock && shares && averagePrice && (
            <div className="space-y-2">
              {totalValue > 10000 && (
                <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <AlertTriangle className="w-4 h-4 text-yellow-600" />
                  <span className="text-sm text-yellow-800">
                    Position importante : {((totalValue / 100000) * 100).toFixed(1)}% d'un portefeuille de $100k
                  </span>
                </div>
              )}

              <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span className="text-sm text-green-800">
                  Action conforme aux critères Sharia
                </span>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={loading}
            >
              Annuler
            </Button>
            <Button
              onClick={handleAddPosition}
              disabled={!selectedStock || !shares || !averagePrice || loading}
              className="flex items-center gap-2"
            >
              {loading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Ajout en cours...
                </>
              ) : (
                <>
                  <Plus className="w-4 h-4" />
                  Ajouter la position
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default AddPositionDialog
