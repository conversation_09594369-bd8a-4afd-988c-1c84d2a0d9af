'use client'

import { Card, CardContent } from '@/components/ui/card'

export default function SetupBanner() {
  return (
    <Card className="border-blue-200 bg-blue-50 mb-6">
      <CardContent className="pt-6">
        <div className="flex items-start space-x-3">
          <span className="text-blue-600 text-xl">ℹ️</span>
          <div className="flex-1">
            <h3 className="font-medium text-blue-900 mb-1">
              Configuration en cours
            </h3>
            <p className="text-sm text-blue-800 mb-3">
              L'application est presque prête ! Il reste quelques étapes de configuration 
              pour activer l'authentification Google et la base de données.
            </p>
            <div className="text-sm text-blue-700">
              📖 Voir les fichiers SETUP_DATABASE.md et GOOGLE_OAUTH_SETUP.md
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
