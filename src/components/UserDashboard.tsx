'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useUserPortfolio, useUserWatchlist, useUserRecommendations } from '@/hooks/useUserPortfolio'
import RealTimeStockCard from './RealTimeStockCard'
import AddPositionDialog from './AddPositionDialog'
import RemovePositionDialog from './RemovePositionDialog'
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  PieChart,
  Star,
  Bell,
  Plus,
  RefreshCw,
  Crown,
  AlertTriangle,
  CheckCircle
} from 'lucide-react'
import { cn } from '@/lib/utils'

export function UserDashboard() {
  const router = useRouter()

  const {
    portfolios,
    currentPortfolio,
    positions,
    summary,
    loading: portfolioLoading,
    metrics,
    fetchPortfolios,
    setCurrentPortfolio,
    removePosition
  } = useUserPortfolio()

  const {
    watchlist,
    loading: watchlistLoading,
    fetchWatchlist
  } = useUserWatchlist()

  const {
    recommendations,
    loading: recommendationsLoading,
    unreadCount,
    fetchRecommendations,
    generateRecommendations
  } = useUserRecommendations()

  const [refreshing, setRefreshing] = useState(false)

  // Variable de chargement globale
  const loading = portfolioLoading || watchlistLoading || recommendationsLoading

  // Fonction pour supprimer une position
  const handleRemovePosition = async (positionId: string) => {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette position ?')) {
      await removePosition(positionId)
    }
  }

  // Rafraîchir toutes les données
  const handleRefreshAll = async () => {
    setRefreshing(true)
    try {
      await Promise.all([
        fetchPortfolios(),
        fetchWatchlist(),
        fetchRecommendations()
      ])
    } catch (error) {
      console.error('Error refreshing data:', error)
    } finally {
      setRefreshing(false)
    }
  }

  // Formater les montants
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount)
  }

  // Formater les pourcentages
  const formatPercent = (percent: number) => {
    return `${percent >= 0 ? '+' : ''}${percent.toFixed(2)}%`
  }

  return (
    <div className="space-y-6">
      {/* En-tête avec actions */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Mon Dashboard</h1>
          <p className="text-muted-foreground">
            Vue d'ensemble de vos investissements Sharia
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={generateRecommendations}
            disabled={recommendationsLoading}
            className="flex items-center gap-2"
          >
            <Star className="w-4 h-4" />
            Nouvelles recommandations
          </Button>
          <Button
            variant="outline"
            onClick={handleRefreshAll}
            disabled={refreshing}
            className="flex items-center gap-2"
          >
            <RefreshCw className={cn("w-4 h-4", refreshing && "animate-spin")} />
            Actualiser
          </Button>
        </div>
      </div>

      {/* Métriques principales */}
      {currentPortfolio && metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <DollarSign className="w-4 h-4 text-primary" />
                <span className="text-sm text-muted-foreground">Valeur totale</span>
              </div>
              <div className="text-2xl font-bold">
                {formatCurrency(metrics.totalValue)}
              </div>
              <div className={cn(
                "text-sm flex items-center gap-1",
                metrics.gainLossPercent >= 0 ? "text-green-600" : "text-red-600"
              )}>
                {metrics.gainLossPercent >= 0 ? (
                  <TrendingUp className="w-3 h-3" />
                ) : (
                  <TrendingDown className="w-3 h-3" />
                )}
                {formatPercent(metrics.gainLossPercent)} ({metrics.totalGainLoss >= 0 ? '+' : ''}{formatCurrency(metrics.totalGainLoss)})
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <PieChart className="w-4 h-4 text-primary" />
                <span className="text-sm text-muted-foreground">Positions</span>
              </div>
              <div className="text-2xl font-bold">{positions.length}</div>
              <div className="text-sm text-muted-foreground">
                Investi: {formatCurrency(metrics.totalInvested)}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-sm text-muted-foreground">Conformité Sharia</span>
              </div>
              <div className="text-2xl font-bold text-green-600">
                {Math.round(metrics.shariaCompliantPercent)}%
              </div>
              <div className="text-sm text-muted-foreground">Actions halal</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Star className="w-4 h-4 text-primary" />
                <span className="text-sm text-muted-foreground">Watchlist</span>
              </div>
              <div className="text-2xl font-bold">{watchlist.length}</div>
              <div className="text-sm text-muted-foreground">
                {metrics.topPerformer ? `Meilleur: ${metrics.topPerformer.symbol}` : 'Actions suivies'}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Message si aucun portefeuille */}
      {(!currentPortfolio || !metrics) && !loading && (
        <Card>
          <CardContent className="p-8 text-center">
            <PieChart className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-semibold mb-2">Aucune donnée de portefeuille</h3>
            <p className="text-muted-foreground mb-4">
              Sélectionnez un portefeuille ou créez-en un nouveau pour voir vos métriques
            </p>
            <Button onClick={() => router.push('/portfolio-generator')}>
              Créer un portefeuille
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Contenu principal avec onglets */}
      <Tabs defaultValue="portfolio" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="portfolio">Portefeuille</TabsTrigger>
          <TabsTrigger value="watchlist">Watchlist</TabsTrigger>
          <TabsTrigger value="recommendations">
            Recommandations
            {unreadCount > 0 && (
              <Badge variant="destructive" className="ml-2 text-xs">
                {unreadCount}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
        </TabsList>

        {/* Onglet Portefeuille */}
        <TabsContent value="portfolio" className="space-y-4">
          {/* Sélecteur de portefeuille */}
          {portfolios.length > 1 && (
            <Card>
              <CardHeader>
                <CardTitle>Sélectionner un portefeuille</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex gap-2 flex-wrap">
                  {portfolios.map((portfolio) => (
                    <Button
                      key={portfolio.id}
                      variant={currentPortfolio?.id === portfolio.id ? "default" : "outline"}
                      onClick={() => setCurrentPortfolio(portfolio)}
                      className="flex items-center gap-2"
                    >
                      {portfolio.name}
                      {metrics && (
                        <Badge variant="secondary">
                          {formatCurrency(metrics.totalValue)}
                        </Badge>
                      )}
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Message si aucun portefeuille */}
          {portfolios.length === 0 && !loading && (
            <Card>
              <CardContent className="p-8 text-center">
                <PieChart className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-semibold mb-2">Aucun portefeuille trouvé</h3>
                <p className="text-muted-foreground mb-4">
                  Créez votre premier portefeuille pour commencer à investir
                </p>
                <Button onClick={() => router.push('/portfolio-generator')}>
                  Créer un portefeuille
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Positions du portefeuille */}
          {currentPortfolio && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {positions.map((position) => (
                <Card key={position.id} className="relative group">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <span className="font-bold">{position.symbol}</span>
                        {position.stockInfo?.isShariCompliant && (
                          <CheckCircle className="w-4 h-4 text-green-500" />
                        )}
                        {position.stockInfo?.shariaKingSince && (
                          <Crown className="w-4 h-4 text-yellow-500" />
                        )}
                      </div>
                      <Badge variant={position.gainLoss && position.gainLoss >= 0 ? "default" : "destructive"}>
                        {formatPercent(position.gainLossPercent || 0)}
                      </Badge>
                    </div>

                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Actions</span>
                        <span>{position.shares}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Prix moyen</span>
                        <span>{formatCurrency(position.averagePrice)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Valeur actuelle</span>
                        <span className="font-medium">
                          {formatCurrency(position.currentValue || 0)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Gain/Perte</span>
                        <span className={cn(
                          "font-medium",
                          position.gainLoss && position.gainLoss >= 0 ? "text-green-600" : "text-red-600"
                        )}>
                          {formatCurrency(position.gainLoss || 0)}
                        </span>
                      </div>
                    </div>

                    {/* Bouton de suppression au survol */}
                    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <RemovePositionDialog
                        position={position}
                        onRemove={handleRemovePosition}
                        trigger={
                          <Button
                            variant="destructive"
                            size="sm"
                            className="h-6 w-6 p-0"
                          >
                            ×
                          </Button>
                        }
                      />
                    </div>
                  </CardContent>
                </Card>
              ))}

              {/* Carte d'ajout de position */}
              <AddPositionDialog
                portfolioId={currentPortfolio.id}
                trigger={
                  <Card className="border-dashed border-2 border-muted-foreground/25 hover:border-primary/50 transition-colors cursor-pointer">
                    <CardContent className="flex items-center justify-center h-full min-h-[150px]">
                      <div className="text-center text-muted-foreground">
                        <Plus className="w-8 h-8 mx-auto mb-2" />
                        <p className="text-sm">Ajouter une position</p>
                      </div>
                    </CardContent>
                  </Card>
                }
              />
            </div>
          )}
        </TabsContent>

        {/* Onglet Watchlist */}
        <TabsContent value="watchlist" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {watchlist.map((item) => (
              <RealTimeStockCard
                key={item.id}
                symbol={item.symbol}
                showDetails={true}
                className="relative"
              />
            ))}

            {/* Carte d'ajout à la watchlist */}
            <Card className="border-dashed border-2 border-muted-foreground/25 hover:border-primary/50 transition-colors">
              <CardContent className="flex items-center justify-center h-full min-h-[200px]">
                <div className="text-center text-muted-foreground">
                  <Plus className="w-8 h-8 mx-auto mb-2" />
                  <p className="text-sm">Ajouter à la watchlist</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Onglet Recommandations */}
        <TabsContent value="recommendations" className="space-y-4">
          <div className="space-y-4">
            {recommendations.map((rec) => (
              <Card key={rec.id} className={cn(
                "transition-all",
                !rec.isRead && "border-primary/50 bg-primary/5"
              )}>
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="font-bold">{rec.symbol}</span>
                        <Badge variant={
                          rec.recommendationType === 'buy' ? 'default' :
                          rec.recommendationType === 'sell' ? 'destructive' :
                          'secondary'
                        }>
                          {rec.recommendationType.toUpperCase()}
                        </Badge>
                        <Badge variant="outline">
                          Confiance: {Math.round(rec.confidenceScore * 100)}%
                        </Badge>
                        {!rec.isRead && (
                          <Badge variant="destructive" className="text-xs">
                            Nouveau
                          </Badge>
                        )}
                      </div>
                      
                      <p className="text-sm text-muted-foreground mb-2">
                        {rec.reason}
                      </p>
                      
                      <div className="flex gap-4 text-xs text-muted-foreground">
                        {rec.targetPrice && (
                          <span>Prix cible: {formatCurrency(rec.targetPrice)}</span>
                        )}
                        {rec.targetAllocationPercent && (
                          <span>Allocation: {rec.targetAllocationPercent}%</span>
                        )}
                        <span>
                          {new Date(rec.createdAt).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline">
                        Voir détails
                      </Button>
                      <Button size="sm">
                        Ajouter au portefeuille
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}

            {recommendations.length === 0 && (
              <Card>
                <CardContent className="p-8 text-center">
                  <Star className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-medium mb-2">Aucune recommandation</h3>
                  <p className="text-muted-foreground mb-4">
                    Générez des recommandations personnalisées basées sur votre profil
                  </p>
                  <Button onClick={generateRecommendations} disabled={recommendationsLoading}>
                    <Star className="w-4 h-4 mr-2" />
                    Générer des recommandations
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        {/* Onglet Performance */}
        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Meilleure performance */}
            {metrics.topPerformer && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="w-5 h-5 text-green-500" />
                    Meilleure performance
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{metrics.topPerformer.symbol}</div>
                  <div className="text-green-600 font-medium">
                    {formatPercent(metrics.topPerformer.gainLossPercent || 0)}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Gain: {formatCurrency(metrics.topPerformer.gainLoss || 0)}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Pire performance */}
            {metrics.worstPerformer && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingDown className="w-5 h-5 text-red-500" />
                    Performance à surveiller
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{metrics.worstPerformer.symbol}</div>
                  <div className="text-red-600 font-medium">
                    {formatPercent(metrics.worstPerformer.gainLossPercent || 0)}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Perte: {formatCurrency(metrics.worstPerformer.gainLoss || 0)}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Répartition par conformité Sharia */}
          <Card>
            <CardHeader>
              <CardTitle>Conformité Sharia</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span>Actions conformes Sharia</span>
                  <Badge variant="default">
                    {Math.round(metrics.shariaCompliantPercent)}%
                  </Badge>
                </div>
                
                <div className="w-full bg-muted rounded-full h-2">
                  <div 
                    className="bg-primary h-2 rounded-full transition-all"
                    style={{ width: `${metrics.shariaCompliantPercent}%` }}
                  />
                </div>
                
                <div className="text-sm text-muted-foreground">
                  {positions.filter(p => p.stockInfo?.isShariCompliant).length} sur {positions.length} positions
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default UserDashboard
