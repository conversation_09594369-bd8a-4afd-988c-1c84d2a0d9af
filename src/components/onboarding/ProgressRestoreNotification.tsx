'use client'

import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'

interface ProgressRestoreNotificationProps {
  answersCount: number
  currentStep: number
  currentQuestion: number
  onContinue: () => void
  onRestart: () => void
}

export default function ProgressRestoreNotification({
  answersCount,
  currentStep,
  currentQuestion,
  onContinue,
  onRestart
}: ProgressRestoreNotificationProps) {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50"
    >
      <Card className="w-full max-w-md">
        <CardContent className="p-6 text-center">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2 }}
            className="text-6xl mb-4"
          >
            🔄
          </motion.div>
          
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Progression trouvée !
          </h2>
          
          <p className="text-gray-600 mb-6">
            Nous avons trouvé vos réponses précédentes :
          </p>
          
          <div className="bg-blue-50 rounded-lg p-4 mb-6">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-blue-600">{answersCount}</div>
                <div className="text-xs text-blue-800">Réponses</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-600">{currentStep}</div>
                <div className="text-xs text-blue-800">Étape</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-600">{currentQuestion}</div>
                <div className="text-xs text-blue-800">Question</div>
              </div>
            </div>
          </div>
          
          <div className="space-y-3">
            <Button
              onClick={onContinue}
              className="w-full bg-green-600 hover:bg-green-700"
            >
              Continuer où j'en étais 🚀
            </Button>
            
            <Button
              onClick={onRestart}
              variant="outline"
              className="w-full"
            >
              Recommencer depuis le début
            </Button>
          </div>
          
          <p className="text-xs text-gray-500 mt-4">
            Vos réponses sont automatiquement sauvegardées
          </p>
        </CardContent>
      </Card>
    </motion.div>
  )
}
