'use client'

import { motion } from 'framer-motion'
import { OnboardingQuestion as QuestionType } from '@/types'
import { Input } from '@/components/ui/input'
import { Slider } from '@/components/ui/slider'
import { SmartSlider } from '@/components/ui/smart-slider'
import { Card, CardContent } from '@/components/ui/card'
import { useState } from 'react'

interface OnboardingQuestionProps {
  question: QuestionType
  value: any
  onChange: (value: any) => void
  allAnswers?: Record<string, any> // Pour accéder aux autres réponses
}

export default function OnboardingQuestion({ question, value, onChange, allAnswers = {} }: OnboardingQuestionProps) {
  const [localValue, setLocalValue] = useState(value || '')

  const handleInputChange = (newValue: string) => {
    setLocalValue(newValue)
    onChange(newValue)
  }



  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const calculateSavingsRate = () => {
    const income = allAnswers.monthlyIncome || 0
    const savings = value || 0
    if (income === 0) return 0
    return Math.round((savings / income) * 100)
  }

  const getSavingsComment = (rate: number) => {
    if (rate === 0) {
      return {
        text: "Commencer à épargner, même 1€, c'est déjà un premier pas ! 💪",
        color: "text-orange-600",
        bgColor: "bg-orange-50"
      }
    } else if (rate < 5) {
      return {
        text: "Un petit début ! La moyenne française est de 14% 📈",
        color: "text-orange-600",
        bgColor: "bg-orange-50"
      }
    } else if (rate >= 5 && rate < 10) {
      return {
        text: "Pas mal ! Vous êtes sur la bonne voie 👍",
        color: "text-yellow-600",
        bgColor: "bg-yellow-50"
      }
    } else if (rate >= 10 && rate < 14) {
      return {
        text: "Très bien ! Vous approchez de la moyenne française (14%) 🎯",
        color: "text-blue-600",
        bgColor: "bg-blue-50"
      }
    } else if (rate >= 14 && rate < 20) {
      return {
        text: "Excellent ! Vous épargnez plus que la moyenne française 🏆",
        color: "text-green-600",
        bgColor: "bg-green-50"
      }
    } else if (rate >= 20 && rate < 30) {
      return {
        text: "Impressionnant ! Vous êtes un vrai épargnant 💎",
        color: "text-green-700",
        bgColor: "bg-green-100"
      }
    } else if (rate >= 30 && rate < 50) {
      return {
        text: "Wow ! Vous épargnez énormément ! Pensez à vous faire plaisir aussi 😄",
        color: "text-purple-600",
        bgColor: "bg-purple-50"
      }
    } else if (rate >= 50 && rate < 70) {
      return {
        text: "Oula ! Il faut sortir de temps en temps ! 😅 Profitez un peu de la vie !",
        color: "text-red-600",
        bgColor: "bg-red-50"
      }
    } else {
      return {
        text: "Êtes-vous sûr de ces chiffres ? 🤔 Même les moines dépensent plus !",
        color: "text-red-700",
        bgColor: "bg-red-100"
      }
    }
  }

  const getAgeComment = (age: number) => {
    if (age < 25) {
      return {
        text: "Parfait ! Commencer jeune, c'est le secret de la richesse 🚀",
        color: "text-green-600",
        bgColor: "bg-green-50"
      }
    } else if (age >= 25 && age < 35) {
      return {
        text: "Excellent timing ! Vos 30s sont parfaites pour investir 💪",
        color: "text-blue-600",
        bgColor: "bg-blue-50"
      }
    } else if (age >= 35 && age < 45) {
      return {
        text: "Jamais trop tard ! Vos revenus sont probablement au top 📈",
        color: "text-purple-600",
        bgColor: "bg-purple-50"
      }
    } else if (age >= 45 && age < 55) {
      return {
        text: "Sage décision ! Il est temps de préparer l'avenir 🎯",
        color: "text-orange-600",
        bgColor: "bg-orange-50"
      }
    } else if (age >= 55 && age < 65) {
      return {
        text: "Bravo ! Optimiser ses finances avant la retraite 🏖️",
        color: "text-yellow-600",
        bgColor: "bg-yellow-50"
      }
    } else {
      return {
        text: "Félicitations ! Profiter de sa retraite avec style 🌴",
        color: "text-green-600",
        bgColor: "bg-green-50"
      }
    }
  }

  const getChoiceComment = (questionId: string, selectedValue: any) => {
    switch (questionId) {
      case 'hasInvestedBefore':
        if (selectedValue === 'true') {
          return {
            text: "Super ! Votre expérience sera un atout précieux 🎯",
            color: "text-green-600",
            bgColor: "bg-green-50"
          }
        } else {
          return {
            text: "Parfait ! Tout le monde a commencé un jour 🌱",
            color: "text-blue-600",
            bgColor: "bg-blue-50"
          }
        }

      case 'boycottChoice':
        if (selectedValue === 'no_boycott') {
          return {
            text: "Approche pragmatique ! Plus d'opportunités d'investissement 📈",
            color: "text-blue-600",
            bgColor: "bg-blue-50"
          }
        } else {
          return {
            text: "Convictions fortes ! Investir selon ses valeurs 💪",
            color: "text-purple-600",
            bgColor: "bg-purple-50"
          }
        }

      case 'isCurrentlyInvesting':
        if (selectedValue === 'true') {
          return {
            text: "Excellent ! Diversifier c'est la clé du succès 📊",
            color: "text-purple-600",
            bgColor: "bg-purple-50"
          }
        } else {
          return {
            text: "C'est le moment parfait pour commencer ! 🚀",
            color: "text-orange-600",
            bgColor: "bg-orange-50"
          }
        }

      case 'riskTolerance':
        if (selectedValue === 'conservative') {
          return {
            text: "Sage ! La sécurité avant tout, les gains viendront 🛡️",
            color: "text-blue-600",
            bgColor: "bg-blue-50"
          }
        } else if (selectedValue === 'moderate') {
          return {
            text: "Équilibré ! Le juste milieu entre risque et rendement ⚖️",
            color: "text-green-600",
            bgColor: "bg-green-50"
          }
        } else {
          return {
            text: "Audacieux ! Les plus grands gains nécessitent du courage 🚀",
            color: "text-red-600",
            bgColor: "bg-red-50"
          }
        }

      case 'investmentHorizon':
        if (selectedValue === 'short') {
          return {
            text: "Court terme ! Privilégions la sécurité ⚡",
            color: "text-yellow-600",
            bgColor: "bg-yellow-50"
          }
        } else if (selectedValue === 'medium') {
          return {
            text: "Moyen terme ! Un bon équilibre croissance/sécurité 🌳",
            color: "text-green-600",
            bgColor: "bg-green-50"
          }
        } else {
          return {
            text: "Long terme ! Le temps est votre meilleur allié 🌲",
            color: "text-purple-600",
            bgColor: "bg-purple-50"
          }
        }

      case 'investmentExperience':
        if (selectedValue === 'beginner') {
          return {
            text: "Parfait ! Nous allons vous accompagner pas à pas 🎓",
            color: "text-blue-600",
            bgColor: "bg-blue-50"
          }
        } else if (selectedValue === 'intermediate') {
          return {
            text: "Bien ! Vos connaissances seront un plus 📚",
            color: "text-green-600",
            bgColor: "bg-green-50"
          }
        } else {
          return {
            text: "Impressionnant ! Vous savez ce que vous faites 🏆",
            color: "text-purple-600",
            bgColor: "bg-purple-50"
          }
        }

      default:
        return null
    }
  }

  const getMultipleChoiceComment = (questionId: string, selectedValues: any[]) => {
    if (questionId === 'investmentGoals' && selectedValues && selectedValues.length > 0) {
      if (selectedValues.length === 1) {
        return {
          text: "Objectif clair ! La concentration peut être efficace 🎯",
          color: "text-blue-600",
          bgColor: "bg-blue-50"
        }
      } else if (selectedValues.length <= 3) {
        return {
          text: "Bonne diversification d'objectifs ! 👍",
          color: "text-green-600",
          bgColor: "bg-green-50"
        }
      } else {
        return {
          text: "Beaucoup d'objectifs ! Priorisez les plus importants 🤔",
          color: "text-orange-600",
          bgColor: "bg-orange-50"
        }
      }
    }

    if (questionId === 'boycottPreferences' && selectedValues && selectedValues.length > 0) {
      if (selectedValues.length === 1) {
        return {
          text: "Exclusion ciblée ! Une conviction claire 🎯",
          color: "text-orange-600",
          bgColor: "bg-orange-50"
        }
      } else if (selectedValues.length <= 3) {
        return {
          text: `${selectedValues.length} exclusions ! Vous savez ce que vous voulez ✊`,
          color: "text-purple-600",
          bgColor: "bg-purple-50"
        }
      } else {
        return {
          text: `${selectedValues.length} exclusions ! Attention à ne pas trop limiter vos options 🤔`,
          color: "text-red-600",
          bgColor: "bg-red-50"
        }
      }
    }

    return null
  }

  const getBudgetComment = (budget: number, income: number) => {
    if (income === 0) return null
    const budgetRate = Math.round((budget / income) * 100)

    if (budgetRate < 5) {
      return {
        text: "Prudent ! Commencer petit, c'est malin 🐣",
        color: "text-blue-600",
        bgColor: "bg-blue-50"
      }
    } else if (budgetRate >= 5 && budgetRate < 15) {
      return {
        text: "Équilibré ! Un bon compromis entre sécurité et croissance ⚖️",
        color: "text-green-600",
        bgColor: "bg-green-50"
      }
    } else if (budgetRate >= 15 && budgetRate < 25) {
      return {
        text: "Ambitieux ! Vous visez haut, j'aime ça ! 🎯",
        color: "text-purple-600",
        bgColor: "bg-purple-50"
      }
    } else {
      return {
        text: "Attention ! N'investissez que ce que vous pouvez vous permettre de perdre 🚨",
        color: "text-red-600",
        bgColor: "bg-red-50"
      }
    }
  }

  return (
    <div className="space-y-6">
      {/* Titre et sous-titre */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center"
      >
        {question.title && (
          <h2 className="text-2xl font-bold text-gray-900 mb-2">{question.title}</h2>
        )}
        {question.subtitle && (
          <p className="text-gray-600 mb-4">{question.subtitle}</p>
        )}
        <h3 className="text-xl font-semibold text-gray-800">{question.question}</h3>
      </motion.div>

      {/* Contenu selon le type */}
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        {question.type === 'single-choice' && (
          <div className="space-y-4">
            <div className="grid gap-3">
              {question.options?.map((option, index) => (
                <motion.div
                  key={option.value}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Card
                    className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
                      value === option.value
                        ? 'ring-2 ring-green-500 bg-green-50'
                        : 'hover:bg-gray-50'
                    }`}
                    onClick={() => onChange(option.value)}
                  >
                    <CardContent className="p-3 md:p-4">
                      <div className="flex items-center space-x-3">
                        <div className="text-xl md:text-2xl flex-shrink-0">{option.icon}</div>
                        <div className="flex-1 min-w-0">
                          <h4 className="font-semibold text-gray-900 text-sm md:text-base">{option.label}</h4>
                          {option.description && (
                            <p className="text-xs md:text-sm text-gray-600 mt-1 line-clamp-2">{option.description}</p>
                          )}
                        </div>
                        <div className={`w-5 h-5 md:w-4 md:h-4 rounded-full border-2 flex-shrink-0 ${
                          value === option.value
                            ? 'bg-primary border-primary glow-primary'
                            : 'border-muted-foreground'
                        }`}>
                          {value === option.value && (
                            <motion.div
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              className="w-full h-full bg-primary-foreground rounded-full scale-50"
                            />
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>

            {/* Commentaire pour les choix */}
            {value && getChoiceComment(question.id, value) && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className={`p-4 rounded-lg ${getChoiceComment(question.id, value)?.bgColor}`}
              >
                <p className={`text-sm font-medium text-center ${getChoiceComment(question.id, value)?.color}`}>
                  {getChoiceComment(question.id, value)?.text}
                </p>
              </motion.div>
            )}
          </div>
        )}

        {question.type === 'multiple-choice' && (
          <div className="space-y-4">
            <div className="grid gap-3">
              {question.options?.map((option, index) => {
                const isSelected = Array.isArray(value) && value.includes(option.value)

                return (
                  <motion.div
                    key={option.value}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    <Card
                      className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
                        isSelected
                          ? 'ring-2 ring-green-500 bg-green-50'
                          : 'hover:bg-gray-50'
                      }`}
                      onClick={() => {
                        const currentValues = Array.isArray(value) ? value : []
                        if (isSelected) {
                          onChange(currentValues.filter(v => v !== option.value))
                        } else {
                          onChange([...currentValues, option.value])
                        }
                      }}
                    >
                      <CardContent className="p-3 md:p-4">
                        <div className="flex items-center space-x-3">
                          <div className="text-xl md:text-2xl flex-shrink-0">{option.icon}</div>
                          <div className="flex-1 min-w-0">
                            <h4 className="font-semibold text-gray-900 text-sm md:text-base">{option.label}</h4>
                            {option.description && (
                              <p className="text-xs md:text-sm text-gray-600 mt-1 line-clamp-2">{option.description}</p>
                            )}
                          </div>
                          <div className={`w-5 h-5 md:w-4 md:h-4 rounded border-2 flex-shrink-0 ${
                            isSelected
                              ? 'bg-primary border-primary glow-primary'
                              : 'border-muted-foreground'
                          }`}>
                            {isSelected && (
                              <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                className="text-white text-xs flex items-center justify-center h-full"
                              >
                                ✓
                              </motion.div>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                )
              })}
            </div>

            {/* Commentaire pour les choix multiples */}
            {Array.isArray(value) && value.length > 0 && getMultipleChoiceComment(question.id, value) && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className={`p-4 rounded-lg ${getMultipleChoiceComment(question.id, value)?.bgColor}`}
              >
                <p className={`text-sm font-medium text-center ${getMultipleChoiceComment(question.id, value)?.color}`}>
                  {getMultipleChoiceComment(question.id, value)?.text}
                </p>
              </motion.div>
            )}
          </div>
        )}

        {question.type === 'slider' && (
          <div className="space-y-6">
            <div className="text-center">
              <motion.div
                key={value}
                initial={{ scale: 1.2 }}
                animate={{ scale: 1 }}
                className="text-4xl font-bold text-green-600 mb-2"
              >
                {value || question.min || 0}
                {question.id === 'shariaPurityLevel' && '%'}
                {question.id === 'age' && ' ans'}
              </motion.div>

              {/* Commentaire pour l'âge */}
              {question.id === 'age' && value && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={`mt-4 p-3 rounded-lg ${getAgeComment(value).bgColor}`}
                >
                  <p className={`text-sm font-medium ${getAgeComment(value).color}`}>
                    {getAgeComment(value).text}
                  </p>
                </motion.div>
              )}
            </div>

            <div className="px-2 md:px-4">
              {question.id === 'age' || question.id === 'shariaPurityLevel' ? (
                // Slider normal pour l'âge et la pureté Sharia
                <>
                  <Slider
                    value={[value || question.min || 0]}
                    onValueChange={(values) => onChange(values[0])}
                    min={question.min || 0}
                    max={question.max || 100}
                    step={question.step || 1}
                    className="w-full"
                  />
                  <div className="flex justify-between text-sm text-gray-500 mt-2">
                    <span>{question.min}</span>
                    <span>{question.max}</span>
                  </div>
                </>
              ) : (
                // Slider intelligent pour les autres
                <SmartSlider
                  value={value || 0}
                  onChange={onChange}
                  questionId={question.id}
                  className="w-full"
                />
              )}
            </div>
          </div>
        )}

        {question.type === 'input' && (
          <div className="space-y-4">
            <Input
              value={localValue}
              onChange={(e) => handleInputChange(e.target.value)}
              placeholder="Votre réponse..."
              className="text-lg p-4 text-center"
            />
          </div>
        )}

        {question.type === 'currency' && (
          <div className="space-y-6">
            <div className="text-center">
              <motion.div
                key={value}
                initial={{ scale: 1.2 }}
                animate={{ scale: 1 }}
                className="text-3xl font-bold text-green-600 mb-4"
              >
                {formatCurrency(value || 0)}
              </motion.div>

              {/* Commentaire pour l'épargne */}
              {question.id === 'monthlySavings' && value && allAnswers.monthlyIncome && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={`mt-4 p-4 rounded-lg ${getSavingsComment(calculateSavingsRate()).bgColor}`}
                >
                  <div className="flex items-center justify-center space-x-2 mb-2">
                    <span className="text-2xl font-bold text-gray-800">
                      {calculateSavingsRate()}%
                    </span>
                    <span className="text-sm text-gray-600">de taux d'épargne</span>
                  </div>
                  <p className={`text-sm font-medium ${getSavingsComment(calculateSavingsRate()).color}`}>
                    {getSavingsComment(calculateSavingsRate()).text}
                  </p>
                </motion.div>
              )}

              {/* Commentaire pour le budget d'investissement */}
              {question.id === 'monthlyBudget' && value && allAnswers.monthlyIncome && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={`mt-4 p-4 rounded-lg ${getBudgetComment(value, allAnswers.monthlyIncome)?.bgColor}`}
                >
                  <div className="flex items-center justify-center space-x-2 mb-2">
                    <span className="text-2xl font-bold text-gray-800">
                      {Math.round((value / allAnswers.monthlyIncome) * 100)}%
                    </span>
                    <span className="text-sm text-gray-600">de vos revenus</span>
                  </div>
                  <p className={`text-sm font-medium ${getBudgetComment(value, allAnswers.monthlyIncome)?.color}`}>
                    {getBudgetComment(value, allAnswers.monthlyIncome)?.text}
                  </p>
                </motion.div>
              )}
            </div>

            <div className="px-2 md:px-4">
              <SmartSlider
                value={value || 0}
                onChange={onChange}
                questionId={question.id}
                className="w-full"
              />
            </div>

            <div className="text-center">
              <Input
                type="number"
                value={value || ''}
                onChange={(e) => onChange(parseFloat(e.target.value) || 0)}
                placeholder="Ou saisissez un montant..."
                className="text-center max-w-xs mx-auto"
              />
            </div>
          </div>
        )}
      </motion.div>
    </div>
  )
}
