'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Card, CardContent } from '@/components/ui/card'
import { onboardingSteps } from '@/data/onboardingQuestions'
import { OnboardingData } from '@/types'
import { useAuthStore } from '@/stores/useAuthStore'
import { useRouter } from 'next/navigation'
import { useOnboardingProgress } from '@/hooks/useOnboardingProgress'
import OnboardingQuestion from '@/components/onboarding/OnboardingQuestion'
import ProgressRestoreNotification from './ProgressRestoreNotification'
import { FloatingNextButton } from '@/components/ui/floating-next-button'
import { AnimatedBackground } from '@/components/animated-background'
import { ShaderBackground } from '@/components/shader-background'

interface OnboardingFlowProps {
  onComplete: (data: OnboardingData) => void
}

export default function OnboardingFlow({ onComplete }: OnboardingFlowProps) {
  const [isAnimating, setIsAnimating] = useState(false)
  const [showProgressNotification, setShowProgressNotification] = useState(false)
  const [hasShownNotification, setHasShownNotification] = useState(false)
  const { user } = useAuthStore()
  const router = useRouter()

  // Utiliser le hook de progression avec sauvegarde automatique
  const {
    answers,
    currentStepIndex,
    currentQuestionIndex,
    isLoaded,
    updateAnswer,
    updatePosition,
    clearProgress,
    hasProgress,
    getProgressSummary
  } = useOnboardingProgress()

  const currentStep = onboardingSteps[currentStepIndex]
  const allQuestions = currentStep?.questions || []

  // Filtrer les questions selon les réponses précédentes
  const visibleQuestions = allQuestions.filter(question => {
    // Afficher la question profession_custom seulement si "autre" est sélectionné
    if (question.id === 'profession_custom') {
      return answers.profession === 'autre'
    }
    // Afficher la question currentInvestmentAmount seulement si l'utilisateur investit actuellement
    if (question.id === 'currentInvestmentAmount') {
      return answers.isCurrentlyInvesting === 'true'
    }
    // Afficher la question boycottPreferences seulement si l'utilisateur veut boycotter
    if (question.id === 'boycottPreferences') {
      return answers.boycottChoice === 'boycott_companies'
    }
    return true
  })

  const currentQuestion = visibleQuestions[currentQuestionIndex]
  const totalQuestions = onboardingSteps.reduce((total, step) => {
    return total + step.questions.filter(q => {
      if (q.id === 'profession_custom') return answers.profession === 'autre'
      if (q.id === 'currentInvestmentAmount') return answers.isCurrentlyInvesting === 'true'
      if (q.id === 'boycottPreferences') return answers.boycottChoice === 'boycott_companies'
      return true
    }).length
  }, 0)
  const answeredQuestions = Object.keys(answers).length
  const progress = (answeredQuestions / totalQuestions) * 100

  useEffect(() => {
    if (!user) {
      router.push('/auth/login')
    }
  }, [user, router])

  // Afficher la notification si une progression est trouvée (une seule fois au chargement)
  useEffect(() => {
    if (isLoaded && !hasShownNotification) {
      const progressSummary = getProgressSummary()
      if (progressSummary.hasProgress && progressSummary.answersCount > 2) {
        setShowProgressNotification(true)
        setHasShownNotification(true)
      }
    }
  }, [isLoaded]) // Seulement quand isLoaded change

  const handleAnswer = (questionId: string, value: any) => {
    updateAnswer(questionId, value)
  }

  const handleContinueProgress = () => {
    setShowProgressNotification(false)
  }

  const handleRestartProgress = () => {
    clearProgress()
    updatePosition(0, 0)
    setShowProgressNotification(false)
    setHasShownNotification(false) // Permettre de montrer la notification à nouveau si nécessaire
  }

  const handleNext = async () => {
    if (!currentQuestion) return

    setIsAnimating(true)
    
    // Attendre l'animation
    await new Promise(resolve => setTimeout(resolve, 300))

    // Passer à la question suivante
    if (currentQuestionIndex < visibleQuestions.length - 1) {
      updatePosition(currentStepIndex, currentQuestionIndex + 1)
    } else if (currentStepIndex < onboardingSteps.length - 1) {
      updatePosition(currentStepIndex + 1, 0)
    } else {
      // Onboarding terminé - effacer la progression sauvegardée
      const onboardingData = transformAnswersToOnboardingData(answers)
      clearProgress()
      onComplete(onboardingData)
    }

    setIsAnimating(false)
  }

  const handlePrevious = async () => {
    setIsAnimating(true)
    
    await new Promise(resolve => setTimeout(resolve, 300))

    if (currentQuestionIndex > 0) {
      updatePosition(currentStepIndex, currentQuestionIndex - 1)
    } else if (currentStepIndex > 0) {
      // Calculer le nombre de questions visibles dans l'étape précédente
      const prevStep = onboardingSteps[currentStepIndex - 1]
      const prevVisibleQuestions = prevStep.questions.filter(q => {
        if (q.id === 'profession_custom') return answers.profession === 'autre'
        if (q.id === 'currentInvestmentAmount') return answers.isCurrentlyInvesting === 'true'
        if (q.id === 'boycottPreferences') return answers.boycottChoice === 'boycott_companies'
        return true
      })
      updatePosition(currentStepIndex - 1, prevVisibleQuestions.length - 1)
    }

    setIsAnimating(false)
  }

  const canProceed = () => {
    if (!currentQuestion) return false
    const answer = answers[currentQuestion.id]
    
    if (currentQuestion.required) {
      return answer !== undefined && answer !== null && answer !== ''
    }
    return true
  }

  const isLastQuestion = currentStepIndex === onboardingSteps.length - 1 &&
                       currentQuestionIndex === visibleQuestions.length - 1

  // Afficher un loader pendant le chargement des données
  if (!isLoaded) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center"
        >
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="w-16 h-16 border-4 border-green-600 border-t-transparent rounded-full mx-auto mb-4"
          />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Chargement de votre progression...
          </h2>
          <p className="text-gray-600">
            Restauration de vos réponses précédentes
          </p>
        </motion.div>
      </div>
    )
  }

  if (!currentStep || !currentQuestion) {
    return <div>Erreur de chargement...</div>
  }

  return (
    <div className="min-h-screen bg-background relative overflow-hidden">
      {/* Animations de fond */}
      <AnimatedBackground variant="particles" />
      <ShaderBackground variant="mesh" intensity="low" />

      {/* Notification de progression restaurée */}
      {showProgressNotification && (
        <ProgressRestoreNotification
          answersCount={getProgressSummary().answersCount}
          currentStep={getProgressSummary().currentStep}
          currentQuestion={getProgressSummary().currentQuestion}
          onContinue={handleContinueProgress}
          onRestart={handleRestartProgress}
        />
      )}

      {/* Layout avec positions fixes */}
      <div className="min-h-screen flex flex-col relative z-10">
        {/* Header fixe en haut - toujours à la même position */}
        <div className="flex-shrink-0 pt-8 pb-6 px-4">
          <div className="w-full max-w-2xl mx-auto">
            {/* Header avec progression */}
            <motion.div
              className="mb-0"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <motion.div
                    className="text-3xl"
                    animate={{ rotate: [0, 10, -10, 0] }}
                    transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
                  >
                    {currentStep.icon}
                  </motion.div>
                  <div>
                    <h1 className="text-2xl font-bold text-foreground">{currentStep.title}</h1>
                    <p className="text-muted-foreground">{currentStep.description}</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm text-muted-foreground mb-2">
                    Question {answeredQuestions + 1} sur {totalQuestions}
                  </div>
                  {hasProgress() && (
                    <button
                      onClick={() => {
                        setShowProgressNotification(true)
                        setHasShownNotification(true) // Éviter que la notification se redéclenche
                      }}
                      className="text-xs text-muted-foreground hover:text-foreground underline"
                    >
                      Recommencer
                    </button>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Progress value={progress} className="h-2" />
                <div className="flex justify-between text-xs text-gray-500">
                  <span>Progression</span>
                  <span>{Math.round(progress)}%</span>
                </div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Zone de contenu centrale - flexible */}
        <div className="flex-1 flex items-center justify-center px-4 py-4">
          <div className="w-full max-w-2xl">
            {/* Question Card */}
            <AnimatePresence mode="wait">
              <motion.div
                key={`${currentStepIndex}-${currentQuestionIndex}`}
                initial={{ opacity: 0, x: 50, scale: 0.95 }}
                animate={{ opacity: 1, x: 0, scale: 1 }}
                exit={{ opacity: 0, x: -50, scale: 0.95 }}
                transition={{ duration: 0.3, ease: "easeInOut" }}
              >
                <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                  <CardContent className="p-8">
                    <OnboardingQuestion
                      question={currentQuestion}
                      value={answers[currentQuestion.id]}
                      onChange={(value: any) => handleAnswer(currentQuestion.id, value)}
                      allAnswers={answers}
                    />
                  </CardContent>
                </Card>
              </motion.div>
            </AnimatePresence>

            {/* Navigation - Bouton précédent seulement */}
            <motion.div
              className="flex justify-start mt-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              {(currentStepIndex > 0 || currentQuestionIndex > 0) && (
                <Button
                  variant="outline"
                  onClick={handlePrevious}
                  disabled={isAnimating}
                  className="px-4 py-2 text-sm"
                  size="sm"
                >
                  ← Précédent
                </Button>
              )}
            </motion.div>
          </div>
        </div>

        {/* Footer fixe en bas - toujours à la même position */}
        <div className="flex-shrink-0 pb-8 pt-4">
          <div className="w-full max-w-2xl mx-auto px-4">
            {/* Indicateurs de step - toujours en bas */}
            <div className="flex justify-center space-x-2">
              {onboardingSteps.map((step, index) => (
                <motion.div
                  key={step.id}
                  className={`w-3 h-3 rounded-full transition-colors ${
                    index === currentStepIndex
                      ? 'bg-green-600'
                      : index < currentStepIndex
                        ? 'bg-green-300'
                        : 'bg-gray-200'
                  }`}
                  whileHover={{ scale: 1.2 }}
                  animate={index === currentStepIndex ? { scale: [1, 1.2, 1] } : {}}
                  transition={{ duration: 1, repeat: Infinity }}
                />
              ))}
            </div>
          </div>
        </div>

        {/* Bouton flottant pour suivant */}
        <FloatingNextButton
          onClick={handleNext}
          disabled={!canProceed() || isAnimating}
          isAnimating={isAnimating}
          isLastQuestion={isLastQuestion}
        />
      </div>
    </div>
  )



}

function transformAnswersToOnboardingData(answers: Record<string, any>): OnboardingData {
  // Gérer la profession avec le champ personnalisé
  let profession = answers.profession
  if (answers.profession === 'autre' && answers.profession_custom) {
    profession = answers.profession_custom
  }

  // Gérer les préférences de boycott
  let boycottPreferences: string[] = []
  if (answers.boycottChoice === 'boycott_companies' && answers.boycottPreferences) {
    boycottPreferences = answers.boycottPreferences
  }

  return {
    age: answers.age,
    profession: profession,
    monthlyIncome: answers.monthlyIncome,
    monthlySavings: answers.monthlySavings,
    hasInvestedBefore: answers.hasInvestedBefore === 'true',
    isCurrentlyInvesting: answers.isCurrentlyInvesting === 'true',
    currentInvestmentAmount: answers.currentInvestmentAmount,
    investmentExperience: answers.investmentExperience || 'beginner',
    investmentGoals: answers.investmentGoals || [],
    riskTolerance: answers.riskTolerance || 'moderate',
    investmentHorizon: answers.investmentHorizon || 'medium',
    monthlyBudget: answers.monthlyBudget || 100,
    shariaPurityLevel: answers.shariaPurityLevel || 98,
    boycottPreferences: boycottPreferences,
    shariaKnowledge: answers.shariaKnowledge || 'beginner'
  }
}
