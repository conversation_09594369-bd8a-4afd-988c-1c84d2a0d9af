'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { useUserWatchlist } from '@/hooks/useUserPortfolio'
import { useStockSearch } from '@/hooks/useFinancialData'
import RealTimeStockCard from './RealTimeStockCard'
import { 
  Search, 
  Plus, 
  Star, 
  Bell, 
  Trash2, 
  Edit,
  TrendingUp,
  TrendingDown,
  CheckCircle,
  Crown
} from 'lucide-react'
import { cn } from '@/lib/utils'

export function WatchlistManager() {
  const { 
    watchlist, 
    loading, 
    addToWatchlist, 
    removeFromWatchlist, 
    isInWatchlist 
  } = useUserWatchlist()

  const [addDialogOpen, setAddDialogOpen] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [editingItem, setEditingItem] = useState<any>(null)

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Ma Watchlist</h2>
          <p className="text-muted-foreground">
            Suivez vos actions favorites et recevez des alertes
          </p>
        </div>
        <AddToWatchlistDialog 
          open={addDialogOpen}
          onOpenChange={setAddDialogOpen}
          onAdd={addToWatchlist}
        />
      </div>

      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Star className="w-4 h-4 text-primary" />
              <span className="text-sm text-muted-foreground">Total</span>
            </div>
            <div className="text-2xl font-bold">{watchlist.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-green-500" />
              <span className="text-sm text-muted-foreground">Sharia</span>
            </div>
            <div className="text-2xl font-bold">
              {watchlist.filter(item => item.stocks?.is_sharia_compliant).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Crown className="w-4 h-4 text-yellow-500" />
              <span className="text-sm text-muted-foreground">Sharia Kings</span>
            </div>
            <div className="text-2xl font-bold">
              {watchlist.filter(item => item.stocks?.sharia_king_since).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Bell className="w-4 h-4 text-primary" />
              <span className="text-sm text-muted-foreground">Alertes</span>
            </div>
            <div className="text-2xl font-bold">
              {watchlist.filter(item => item.alert_enabled).length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Liste des actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {watchlist.map((item) => (
          <div key={item.id} className="relative group">
            <RealTimeStockCard
              symbol={item.symbol}
              name={item.stocks?.name}
              isShariKing={!!item.stocks?.sharia_king_since}
              showDetails={true}
              className="h-full"
            />
            
            {/* Overlay avec actions */}
            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center gap-2">
              <Button
                size="sm"
                variant="secondary"
                onClick={() => {
                  setEditingItem(item)
                  setEditDialogOpen(true)
                }}
              >
                <Edit className="w-4 h-4" />
              </Button>
              <Button
                size="sm"
                variant="destructive"
                onClick={() => removeFromWatchlist(item.symbol)}
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>

            {/* Badges d'information */}
            <div className="absolute top-2 right-2 flex flex-col gap-1">
              {item.alert_enabled && (
                <Badge variant="secondary" className="text-xs">
                  <Bell className="w-3 h-3 mr-1" />
                  Alerte
                </Badge>
              )}
              {item.target_price && (
                <Badge variant="outline" className="text-xs">
                  Cible: ${item.target_price}
                </Badge>
              )}
            </div>

            {/* Notes */}
            {item.notes && (
              <div className="absolute bottom-2 left-2 right-2">
                <div className="bg-black/75 text-white text-xs p-2 rounded truncate">
                  {item.notes}
                </div>
              </div>
            )}
          </div>
        ))}

        {/* Carte d'ajout */}
        <Card 
          className="border-dashed border-2 border-muted-foreground/25 hover:border-primary/50 transition-colors cursor-pointer"
          onClick={() => setAddDialogOpen(true)}
        >
          <CardContent className="flex items-center justify-center h-full min-h-[200px]">
            <div className="text-center text-muted-foreground">
              <Plus className="w-8 h-8 mx-auto mb-2" />
              <p className="text-sm">Ajouter à la watchlist</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Dialog d'édition */}
      <EditWatchlistDialog
        open={editDialogOpen}
        onOpenChange={setEditDialogOpen}
        item={editingItem}
        onSave={(updates) => {
          // TODO: Implémenter la mise à jour
          setEditDialogOpen(false)
          setEditingItem(null)
        }}
      />

      {/* Message si vide */}
      {watchlist.length === 0 && !loading && (
        <Card>
          <CardContent className="p-8 text-center">
            <Star className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-medium mb-2">Votre watchlist est vide</h3>
            <p className="text-muted-foreground mb-4">
              Ajoutez des actions pour suivre leurs performances et recevoir des alertes
            </p>
            <Button onClick={() => setAddDialogOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Ajouter votre première action
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

// Composant pour ajouter à la watchlist
function AddToWatchlistDialog({ 
  open, 
  onOpenChange, 
  onAdd 
}: { 
  open: boolean
  onOpenChange: (open: boolean) => void
  onAdd: (symbol: string, options?: any) => Promise<boolean>
}) {
  const [selectedStock, setSelectedStock] = useState<any>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [notes, setNotes] = useState('')
  const [targetPrice, setTargetPrice] = useState('')
  const [alertEnabled, setAlertEnabled] = useState(false)
  const [loading, setLoading] = useState(false)

  const { results: searchResults, loading: searchLoading, search } = useStockSearch()

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    if (query.length >= 2) {
      search(query)
    }
  }

  const handleAdd = async () => {
    if (!selectedStock) return

    setLoading(true)
    try {
      const success = await onAdd(selectedStock.symbol, {
        notes: notes || undefined,
        targetPrice: targetPrice ? parseFloat(targetPrice) : undefined,
        alertEnabled
      })

      if (success) {
        // Réinitialiser
        setSelectedStock(null)
        setSearchQuery('')
        setNotes('')
        setTargetPrice('')
        setAlertEnabled(false)
        onOpenChange(false)
      }
    } catch (error) {
      console.error('Error adding to watchlist:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>
        <Button className="flex items-center gap-2">
          <Plus className="w-4 h-4" />
          Ajouter à la watchlist
        </Button>
      </DialogTrigger>
      
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>Ajouter à la watchlist</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Recherche */}
          <div className="space-y-2">
            <Label>Rechercher une action</Label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Nom ou symbole (ex: AAPL)"
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Résultats */}
            {searchQuery.length >= 2 && !selectedStock && (
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {searchResults.map((stock) => (
                  <Card 
                    key={stock.symbol} 
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => setSelectedStock(stock)}
                  >
                    <CardContent className="p-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <span className="font-medium">{stock.symbol}</span>
                          <div className="text-sm text-muted-foreground">
                            {stock.name}
                          </div>
                        </div>
                        <CheckCircle className="w-4 h-4 text-green-500" />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>

          {/* Action sélectionnée */}
          {selectedStock && (
            <>
              <Card className="border-primary/50 bg-primary/5">
                <CardContent className="p-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <span className="font-bold">{selectedStock.symbol}</span>
                      <div className="text-sm text-muted-foreground">
                        {selectedStock.name}
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setSelectedStock(null)}
                    >
                      Changer
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Options */}
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="notes">Notes (optionnel)</Label>
                  <Textarea
                    id="notes"
                    placeholder="Pourquoi cette action vous intéresse..."
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    rows={2}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="targetPrice">Prix cible ($)</Label>
                  <Input
                    id="targetPrice"
                    type="number"
                    placeholder="150.00"
                    value={targetPrice}
                    onChange={(e) => setTargetPrice(e.target.value)}
                    step="0.01"
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="alertEnabled"
                    checked={alertEnabled}
                    onCheckedChange={setAlertEnabled}
                  />
                  <Label htmlFor="alertEnabled">Activer les alertes</Label>
                </div>
              </div>
            </>
          )}

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Annuler
            </Button>
            <Button
              onClick={handleAdd}
              disabled={!selectedStock || loading}
            >
              {loading ? 'Ajout...' : 'Ajouter'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

// Composant pour éditer un élément de la watchlist
function EditWatchlistDialog({ 
  open, 
  onOpenChange, 
  item, 
  onSave 
}: { 
  open: boolean
  onOpenChange: (open: boolean) => void
  item: any
  onSave: (updates: any) => void
}) {
  const [notes, setNotes] = useState('')
  const [targetPrice, setTargetPrice] = useState('')
  const [alertEnabled, setAlertEnabled] = useState(false)

  // Initialiser avec les valeurs existantes
  useState(() => {
    if (item) {
      setNotes(item.notes || '')
      setTargetPrice(item.target_price?.toString() || '')
      setAlertEnabled(item.alert_enabled || false)
    }
  })

  const handleSave = () => {
    onSave({
      notes: notes || null,
      targetPrice: targetPrice ? parseFloat(targetPrice) : null,
      alertEnabled
    })
  }

  if (!item) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Modifier {item.symbol}</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="editNotes">Notes</Label>
            <Textarea
              id="editNotes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="editTargetPrice">Prix cible ($)</Label>
            <Input
              id="editTargetPrice"
              type="number"
              value={targetPrice}
              onChange={(e) => setTargetPrice(e.target.value)}
              step="0.01"
            />
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="editAlertEnabled"
              checked={alertEnabled}
              onCheckedChange={setAlertEnabled}
            />
            <Label htmlFor="editAlertEnabled">Alertes activées</Label>
          </div>

          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Annuler
            </Button>
            <Button onClick={handleSave}>
              Sauvegarder
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default WatchlistManager
