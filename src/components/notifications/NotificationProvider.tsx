"use client"

import { createContext, useContext, useEffect } from 'react'
import { NotificationBanner, useNotifications } from './NotificationBanner'
import { generateTestNotifications } from '@/data/mock-notifications'
import { useRouter } from 'next/navigation'

const NotificationContext = createContext<ReturnType<typeof useNotifications> | null>(null)

export function useNotificationContext() {
  const context = useContext(NotificationContext)
  if (!context) {
    throw new Error('useNotificationContext must be used within a NotificationProvider')
  }
  return context
}

interface NotificationProviderProps {
  children: React.ReactNode
}

export function NotificationProvider({ children }: NotificationProviderProps) {
  const notificationMethods = useNotifications()
  const router = useRouter()

  // Charger les notifications de test au démarrage
  useEffect(() => {
    const testNotifications = generateTestNotifications()
    testNotifications.forEach(notification => {
      notificationMethods.addNotification(notification)
    })
  }, [])

  // Simuler des notifications périodiques
  useEffect(() => {
    const interval = setInterval(() => {
      // Ajouter une notification aléatoire toutes les 30 secondes (pour la démo)
      const randomTypes = ['market-update', 'sharia-change', 'portfolio-rebalance'] as const
      const randomType = randomTypes[Math.floor(Math.random() * randomTypes.length)]
      
      // Seulement 20% de chance d'ajouter une notification
      if (Math.random() < 0.2) {
        const { createMockNotification } = require('@/data/mock-notifications')
        const newNotification = createMockNotification(randomType)
        notificationMethods.addNotification(newNotification)
      }
    }, 30000) // 30 secondes

    return () => clearInterval(interval)
  }, [notificationMethods])

  const handleNotificationAction = (notification: any) => {
    if (notification.actionUrl) {
      router.push(notification.actionUrl)
    }
  }

  return (
    <NotificationContext.Provider value={notificationMethods}>
      {children}
      <NotificationBanner
        notifications={notificationMethods.notifications}
        onDismiss={notificationMethods.dismissNotification}
        onMarkAsRead={notificationMethods.markAsRead}
        onAction={handleNotificationAction}
      />
    </NotificationContext.Provider>
  )
}
