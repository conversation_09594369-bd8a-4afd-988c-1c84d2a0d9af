"use client"

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, AlertTriangle, Info, CheckCircle, AlertCircle, Crown } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

export type NotificationType = 'info' | 'warning' | 'success' | 'error' | 'sharia-alert'

export interface Notification {
  id: string
  type: NotificationType
  title: string
  message: string
  actionLabel?: string
  actionUrl?: string
  isRead: boolean
  createdAt: string
  expiresAt?: string
  priority: 'low' | 'medium' | 'high'
  category: 'portfolio' | 'sharia' | 'market' | 'system'
}

interface NotificationBannerProps {
  notifications: Notification[]
  onDismiss: (id: string) => void
  onMarkAsRead: (id: string) => void
  onAction?: (notification: Notification) => void
}

const getNotificationIcon = (type: NotificationType) => {
  switch (type) {
    case 'info':
      return <Info className="h-5 w-5" />
    case 'warning':
      return <AlertTriangle className="h-5 w-5" />
    case 'success':
      return <CheckCircle className="h-5 w-5" />
    case 'error':
      return <AlertCircle className="h-5 w-5" />
    case 'sharia-alert':
      return <Crown className="h-5 w-5" />
    default:
      return <Info className="h-5 w-5" />
  }
}

const getNotificationColors = (type: NotificationType) => {
  switch (type) {
    case 'info':
      return {
        bg: 'bg-blue-50 dark:bg-blue-950/20',
        border: 'border-blue-200 dark:border-blue-800',
        text: 'text-blue-800 dark:text-blue-200',
        icon: 'text-blue-600 dark:text-blue-400'
      }
    case 'warning':
      return {
        bg: 'bg-yellow-50 dark:bg-yellow-950/20',
        border: 'border-yellow-200 dark:border-yellow-800',
        text: 'text-yellow-800 dark:text-yellow-200',
        icon: 'text-yellow-600 dark:text-yellow-400'
      }
    case 'success':
      return {
        bg: 'bg-green-50 dark:bg-green-950/20',
        border: 'border-green-200 dark:border-green-800',
        text: 'text-green-800 dark:text-green-200',
        icon: 'text-green-600 dark:text-green-400'
      }
    case 'error':
      return {
        bg: 'bg-red-50 dark:bg-red-950/20',
        border: 'border-red-200 dark:border-red-800',
        text: 'text-red-800 dark:text-red-200',
        icon: 'text-red-600 dark:text-red-400'
      }
    case 'sharia-alert':
      return {
        bg: 'bg-purple-50 dark:bg-purple-950/20',
        border: 'border-purple-200 dark:border-purple-800',
        text: 'text-purple-800 dark:text-purple-200',
        icon: 'text-purple-600 dark:text-purple-400'
      }
    default:
      return {
        bg: 'bg-gray-50 dark:bg-gray-950/20',
        border: 'border-gray-200 dark:border-gray-800',
        text: 'text-gray-800 dark:text-gray-200',
        icon: 'text-gray-600 dark:text-gray-400'
      }
  }
}

export function NotificationBanner({ 
  notifications, 
  onDismiss, 
  onMarkAsRead, 
  onAction 
}: NotificationBannerProps) {
  const [visibleNotifications, setVisibleNotifications] = useState<Notification[]>([])

  useEffect(() => {
    // Filtrer les notifications non lues et non expirées
    const activeNotifications = notifications.filter(notification => {
      if (notification.isRead) return false
      if (notification.expiresAt && new Date(notification.expiresAt) < new Date()) return false
      return true
    })

    // Trier par priorité et date
    const sortedNotifications = activeNotifications.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 }
      if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
        return priorityOrder[b.priority] - priorityOrder[a.priority]
      }
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    })

    // Limiter à 3 notifications maximum
    setVisibleNotifications(sortedNotifications.slice(0, 3))
  }, [notifications])

  const handleDismiss = (notification: Notification) => {
    onMarkAsRead(notification.id)
    onDismiss(notification.id)
  }

  const handleAction = (notification: Notification) => {
    onMarkAsRead(notification.id)
    if (onAction) {
      onAction(notification)
    }
  }

  if (visibleNotifications.length === 0) {
    return null
  }

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-md">
      <AnimatePresence>
        {visibleNotifications.map((notification) => {
          const colors = getNotificationColors(notification.type)
          const icon = getNotificationIcon(notification.type)

          return (
            <motion.div
              key={notification.id}
              initial={{ opacity: 0, x: 300, scale: 0.8 }}
              animate={{ opacity: 1, x: 0, scale: 1 }}
              exit={{ opacity: 0, x: 300, scale: 0.8 }}
              transition={{ duration: 0.3, ease: "easeOut" }}
              className={`
                ${colors.bg} ${colors.border} ${colors.text}
                border rounded-lg shadow-lg p-4 backdrop-blur-sm
              `}
            >
              <div className="flex items-start gap-3">
                <div className={`${colors.icon} mt-0.5`}>
                  {icon}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-medium text-sm">{notification.title}</h4>
                    <Badge 
                      variant="secondary" 
                      className="text-xs"
                    >
                      {notification.category}
                    </Badge>
                    {notification.priority === 'high' && (
                      <Badge variant="destructive" className="text-xs">
                        Urgent
                      </Badge>
                    )}
                  </div>
                  
                  <p className="text-sm opacity-90 mb-3">
                    {notification.message}
                  </p>
                  
                  <div className="flex items-center gap-2">
                    {notification.actionLabel && (
                      <Button
                        size="sm"
                        variant="outline"
                        className="text-xs h-7"
                        onClick={() => handleAction(notification)}
                      >
                        {notification.actionLabel}
                      </Button>
                    )}
                    
                    <span className="text-xs opacity-70 ml-auto">
                      {new Date(notification.createdAt).toLocaleTimeString('fr-FR', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </span>
                  </div>
                </div>
                
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-6 w-6 p-0 hover:bg-black/10"
                  onClick={() => handleDismiss(notification)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </motion.div>
          )
        })}
      </AnimatePresence>
    </div>
  )
}

// Hook pour gérer les notifications
export function useNotifications() {
  const [notifications, setNotifications] = useState<Notification[]>([])

  const addNotification = (notification: Omit<Notification, 'id' | 'isRead' | 'createdAt'>) => {
    const newNotification: Notification = {
      ...notification,
      id: Math.random().toString(36).substr(2, 9),
      isRead: false,
      createdAt: new Date().toISOString()
    }
    
    setNotifications(prev => [newNotification, ...prev])
    
    // Auto-dismiss après 10 secondes pour les notifications de faible priorité
    if (notification.priority === 'low') {
      setTimeout(() => {
        markAsRead(newNotification.id)
      }, 10000)
    }
  }

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === id 
          ? { ...notification, isRead: true }
          : notification
      )
    )
  }

  const dismissNotification = (id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id))
  }

  const clearAllNotifications = () => {
    setNotifications([])
  }

  return {
    notifications,
    addNotification,
    markAsRead,
    dismissNotification,
    clearAllNotifications
  }
}
