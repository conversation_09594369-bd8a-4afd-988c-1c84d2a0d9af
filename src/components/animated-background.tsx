'use client'

import { useEffect, useRef, useState } from 'react'

interface AnimatedBackgroundProps {
  variant?: 'waves' | 'particles' | 'gradient'
  className?: string
}

export function AnimatedBackground({ variant = 'waves', className = '' }: AnimatedBackgroundProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    if (!mounted) return

    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    let animationId: number
    let time = 0

    const resizeCanvas = () => {
      canvas.width = window.innerWidth
      canvas.height = window.innerHeight
    }

    const drawWaves = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      
      // Couleurs basées sur le thème CSS
      const isDark = document.documentElement.classList.contains('dark')

      if (isDark) {
        // Mode sombre - couleurs plus visibles et lumineuses
        ctx.fillStyle = 'rgba(34, 197, 94, 0.12)'
      } else {
        // Mode clair - couleurs plus vives et visibles
        ctx.fillStyle = 'rgba(34, 197, 94, 0.15)'
      }

      // Première vague
      ctx.beginPath()
      ctx.moveTo(0, canvas.height)
      
      for (let x = 0; x <= canvas.width; x += 10) {
        const y = canvas.height - 100 + Math.sin((x + time) * 0.01) * 50 + Math.sin((x + time) * 0.02) * 25
        ctx.lineTo(x, y)
      }
      
      ctx.lineTo(canvas.width, canvas.height)
      ctx.closePath()
      ctx.fill()

      // Deuxième vague
      if (isDark) {
        ctx.fillStyle = 'rgba(34, 197, 94, 0.08)'
      } else {
        ctx.fillStyle = 'rgba(34, 197, 94, 0.10)'
      }
      
      ctx.beginPath()
      ctx.moveTo(0, canvas.height)
      
      for (let x = 0; x <= canvas.width; x += 10) {
        const y = canvas.height - 150 + Math.sin((x + time * 1.5) * 0.008) * 40 + Math.sin((x + time * 1.2) * 0.015) * 20
        ctx.lineTo(x, y)
      }
      
      ctx.lineTo(canvas.width, canvas.height)
      ctx.closePath()
      ctx.fill()

      // Troisième vague (plus subtile)
      if (isDark) {
        ctx.fillStyle = 'rgba(34, 197, 94, 0.06)'
      } else {
        ctx.fillStyle = 'rgba(34, 197, 94, 0.08)'
      }
      
      ctx.beginPath()
      ctx.moveTo(0, canvas.height)
      
      for (let x = 0; x <= canvas.width; x += 15) {
        const y = canvas.height - 200 + Math.sin((x + time * 0.8) * 0.006) * 30
        ctx.lineTo(x, y)
      }
      
      ctx.lineTo(canvas.width, canvas.height)
      ctx.closePath()
      ctx.fill()
    }

    const drawParticles = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      
      const isDark = document.documentElement.classList.contains('dark')
      const particleCount = 50
      
      for (let i = 0; i < particleCount; i++) {
        const x = (i * canvas.width / particleCount + time * 0.5) % canvas.width
        const y = canvas.height / 2 + Math.sin(time * 0.01 + i * 0.1) * 100
        const size = 2 + Math.sin(time * 0.02 + i * 0.05) * 1
        
        ctx.beginPath()
        ctx.arc(x, y, size, 0, Math.PI * 2)
        
        if (isDark) {
          ctx.fillStyle = `rgba(34, 197, 94, ${0.3 + Math.sin(time * 0.01 + i * 0.1) * 0.2})`
        } else {
          ctx.fillStyle = `rgba(34, 197, 94, ${0.4 + Math.sin(time * 0.01 + i * 0.1) * 0.2})`
        }
        
        ctx.fill()
      }
    }

    const drawGradient = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      
      const isDark = document.documentElement.classList.contains('dark')
      
      // Gradient animé
      const gradient = ctx.createRadialGradient(
        canvas.width / 2 + Math.sin(time * 0.005) * 200,
        canvas.height / 2 + Math.cos(time * 0.003) * 150,
        0,
        canvas.width / 2,
        canvas.height / 2,
        Math.max(canvas.width, canvas.height)
      )
      
      if (isDark) {
        gradient.addColorStop(0, 'rgba(34, 197, 94, 0.15)')
        gradient.addColorStop(0.5, 'rgba(34, 197, 94, 0.08)')
        gradient.addColorStop(1, 'rgba(34, 197, 94, 0)')
      } else {
        gradient.addColorStop(0, 'rgba(34, 197, 94, 0.1)')
        gradient.addColorStop(0.5, 'rgba(34, 197, 94, 0.05)')
        gradient.addColorStop(1, 'rgba(34, 197, 94, 0)')
      }
      
      ctx.fillStyle = gradient
      ctx.fillRect(0, 0, canvas.width, canvas.height)
    }

    const animate = () => {
      time += 1
      
      switch (variant) {
        case 'waves':
          drawWaves()
          break
        case 'particles':
          drawParticles()
          break
        case 'gradient':
          drawGradient()
          break
      }
      
      animationId = requestAnimationFrame(animate)
    }

    resizeCanvas()
    animate()

    window.addEventListener('resize', resizeCanvas)

    return () => {
      window.removeEventListener('resize', resizeCanvas)
      cancelAnimationFrame(animationId)
    }
  }, [variant, mounted])

  if (!mounted) {
    return null
  }

  return (
    <canvas
      ref={canvasRef}
      className={`fixed inset-0 pointer-events-none z-0 ${className}`}
      style={{ mixBlendMode: 'multiply' }}
    />
  )
}
