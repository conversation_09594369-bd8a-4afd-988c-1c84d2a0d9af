'use client'

import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'

interface FloatingElementsProps {
  count?: number
  variant?: 'geometric' | 'islamic' | 'financial'
  className?: string
}

// Fonction pour générer des nombres pseudo-aléatoires déterministes
function seededRandom(seed: number) {
  const x = Math.sin(seed) * 10000
  return x - Math.floor(x)
}

export function FloatingElements({
  count = 15,
  variant = 'islamic',
  className = ''
}: FloatingElementsProps) {
  const [elements, setElements] = useState<Array<{
    id: number
    x: number
    y: number
    size: number
    duration: number
    delay: number
    symbol: string
  }>>([])
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)

    const getSymbols = () => {
      switch (variant) {
        case 'geometric':
          return ['◆', '◇', '●', '○', '▲', '△', '■', '□']
        case 'islamic':
          return ['🕌', '☪', '✦', '✧', '◈', '◉', '⬟', '⬢']
        case 'financial':
          return ['📈', '💰', '💎', '⭐', '🔸', '🔹', '◆', '◇']
        default:
          return ['🕌', '☪', '✦', '✧']
      }
    }

    const symbols = getSymbols()

    // Utilisation de valeurs déterministes basées sur l'index
    const newElements = Array.from({ length: count }, (_, i) => ({
      id: i,
      x: seededRandom(i * 123.456) * 100,
      y: seededRandom(i * 789.012) * 100,
      size: 0.8 + seededRandom(i * 345.678) * 1.5,
      duration: 15 + seededRandom(i * 901.234) * 20,
      delay: seededRandom(i * 567.890) * 10,
      symbol: symbols[Math.floor(seededRandom(i * 234.567) * symbols.length)]
    }))

    setElements(newElements)
  }, [count, variant])

  // Ne pas rendre pendant l'hydratation pour éviter les erreurs
  if (!mounted) {
    return null
  }

  return (
    <div className={`fixed inset-0 pointer-events-none z-10 overflow-hidden ${className}`}>
      {elements.map((element) => (
        <motion.div
          key={element.id}
          className="absolute text-primary/20 dark:text-primary/50 select-none"
          style={{
            left: `${element.x}%`,
            top: `${element.y}%`,
            fontSize: `${element.size}rem`,
          }}
          animate={{
            y: [0, -30, 0],
            x: [0, 15, -10, 0],
            rotate: [0, 180, 360],
            opacity: [0.2, 0.6, 0.2],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: element.duration,
            delay: element.delay,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        >
          {element.symbol}
        </motion.div>
      ))}
    </div>
  )
}

// Composant spécialisé pour les éléments islamiques
export function IslamicFloatingElements({ className = '' }: { className?: string }) {
  const [mounted, setMounted] = useState(false)
  const [stars, setStars] = useState<Array<{
    id: number
    x: number
    y: number
    duration: number
    delay: number
  }>>([])

  useEffect(() => {
    setMounted(true)

    // Générer des étoiles avec des positions déterministes
    const newStars = Array.from({ length: 25 }, (_, i) => ({
      id: i,
      x: seededRandom(i * 111.111) * 100,
      y: seededRandom(i * 222.222) * 100,
      duration: 3 + seededRandom(i * 333.333) * 4,
      delay: seededRandom(i * 444.444) * 5,
    }))

    setStars(newStars)
  }, [])

  if (!mounted) {
    return null
  }

  return (
    <div className={`fixed inset-0 pointer-events-none z-10 ${className}`}>
      {/* Éléments principaux */}
      <FloatingElements count={8} variant="islamic" />

      {/* Étoiles subtiles */}
      <div className="absolute inset-0">
        {stars.map((star) => (
          <motion.div
            key={star.id}
            className="absolute text-primary/10 dark:text-primary/40"
            style={{
              left: `${star.x}%`,
              top: `${star.y}%`,
              fontSize: '0.5rem',
            }}
            animate={{
              opacity: [0, 1, 0],
              scale: [0.5, 1, 0.5],
            }}
            transition={{
              duration: star.duration,
              delay: star.delay,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          >
            ✦
          </motion.div>
        ))}
      </div>
    </div>
  )
}

// Composant pour les éléments financiers
export function FinancialFloatingElements({ className = '' }: { className?: string }) {
  const financialSymbols = ['📊', '💹', '🔢', '💰', '📈', '💎', '⚡', '🎯']
  
  return (
    <div className={`fixed inset-0 pointer-events-none z-10 ${className}`}>
      {financialSymbols.map((symbol, i) => (
        <motion.div
          key={i}
          className="absolute text-primary/15 dark:text-primary/45 text-2xl"
          style={{
            left: `${10 + (i % 4) * 25}%`,
            top: `${20 + Math.floor(i / 4) * 30}%`,
          }}
          animate={{
            y: [0, -20, 0],
            rotate: [0, 10, -10, 0],
            opacity: [0.1, 0.3, 0.1],
          }}
          transition={{
            duration: 8 + i * 2,
            delay: i * 0.5,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        >
          {symbol}
        </motion.div>
      ))}
    </div>
  )
}
