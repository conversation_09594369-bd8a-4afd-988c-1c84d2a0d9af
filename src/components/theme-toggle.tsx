"use client"

import { <PERSON>, <PERSON>, <PERSON> } from "lucide-react"
import { useTheme } from "next-themes"
import { But<PERSON> } from "@/components/ui/button"
import { useEffect, useState } from "react"

export function ThemeToggle() {
  const { theme, setTheme, resolvedTheme } = useTheme()
  const [mounted, setMounted] = useState(false)
  const [forceUpdate, setForceUpdate] = useState(0)

  useEffect(() => {
    setMounted(true)
  }, [])

  const cycleTheme = () => {
    console.log('🎨 Theme Toggle Debug:')
    console.log('- Current theme:', theme)
    console.log('- Resolved theme:', resolvedTheme)
    console.log('- Mounted:', mounted)
    console.log('- ForceUpdate:', forceUpdate)

    let nextTheme: string
    if (theme === "light") {
      nextTheme = "dark"
    } else if (theme === "dark") {
      nextTheme = "system"
    } else {
      nextTheme = "light"
    }

    console.log('- Switching to:', nextTheme)

    // Set theme using next-themes
    setTheme(nextTheme)

    // Also manually update localStorage as a fallback for Bun
    if (typeof window !== 'undefined') {
      console.log('- Updating localStorage and DOM')
      localStorage.setItem('halal-invest-theme', nextTheme)

      // Force a re-render
      setForceUpdate(prev => {
        const newValue = prev + 1
        console.log('- Force update:', newValue)
        return newValue
      })

      // Manually apply theme class to document for immediate feedback
      const html = document.documentElement
      html.classList.remove('light', 'dark')

      if (nextTheme === 'system') {
        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
        console.log('- System theme detected:', systemTheme)
        html.classList.add(systemTheme)
      } else {
        html.classList.add(nextTheme)
      }

      console.log('- DOM classes after update:', html.classList.toString())
    }
  }

  const getIcon = () => {
    if (!mounted) return <Sun className="h-4 w-4" />

    // Force re-evaluation by using forceUpdate in dependency
    const currentTheme = theme || (typeof window !== 'undefined' ? localStorage.getItem('halal-invest-theme') : null)

    if (currentTheme === "light") {
      return <Sun className="h-4 w-4" />
    } else if (currentTheme === "dark") {
      return <Moon className="h-4 w-4" />
    } else if (currentTheme === "system") {
      return <Monitor className="h-4 w-4" />
    } else {
      // Fallback based on resolved theme
      return resolvedTheme === "dark" ? <Moon className="h-4 w-4" /> : <Sun className="h-4 w-4" />
    }
  }

  const getTooltip = () => {
    if (!mounted) return "Changer le thème"

    const currentTheme = theme || (typeof window !== 'undefined' ? localStorage.getItem('halal-invest-theme') : null)

    if (currentTheme === "light") {
      return "Mode clair (cliquer pour mode sombre)"
    } else if (currentTheme === "dark") {
      return "Mode sombre (cliquer pour mode système)"
    } else if (currentTheme === "system") {
      return "Mode système (cliquer pour mode clair)"
    } else {
      return "Changer le thème"
    }
  }

  // Don't render anything until mounted to prevent hydration issues
  if (!mounted) {
    return (
      <Button
        variant="ghost"
        size="icon"
        className="h-9 w-9"
        disabled
      >
        <Sun className="h-4 w-4" />
        <span className="sr-only">Chargement du thème...</span>
      </Button>
    )
  }

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={cycleTheme}
      className="h-9 w-9"
      title={getTooltip()}
      key={forceUpdate} // Force re-render when forceUpdate changes
    >
      {getIcon()}
      <span className="sr-only">Changer le thème</span>
    </Button>
  )
}
