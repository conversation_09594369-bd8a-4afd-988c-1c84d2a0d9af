'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { useFinancialData } from '@/hooks/useFinancialData'
import { Crown, TrendingUp, TrendingDown, RefreshCw, DollarSign, BarChart3 } from 'lucide-react'
import { cn } from '@/lib/utils'

interface RealTimeStockCardProps {
  symbol: string
  name?: string
  isShariKing?: boolean
  showDetails?: boolean
  className?: string
}

export function RealTimeStockCard({ 
  symbol, 
  name, 
  isShariKing = false, 
  showDetails = false,
  className 
}: RealTimeStockCardProps) {
  const { quote, stats, loading, error, refresh, lastUpdated } = useFinancialData(symbol, {
    refreshInterval: 60000, // 1 minute
    autoRefresh: true
  })

  const [isRefreshing, setIsRefreshing] = useState(false)

  const handleRefresh = async () => {
    setIsRefreshing(true)
    await refresh()
    setIsRefreshing(false)
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: quote?.currency || 'USD',
      minimumFractionDigits: 2
    }).format(price)
  }

  const formatPercentage = (percentage: number) => {
    return `${percentage >= 0 ? '+' : ''}${percentage.toFixed(2)}%`
  }

  const formatMarketCap = (marketCap: number) => {
    if (marketCap >= 1e12) {
      return `$${(marketCap / 1e12).toFixed(2)}T`
    } else if (marketCap >= 1e9) {
      return `$${(marketCap / 1e9).toFixed(2)}B`
    } else if (marketCap >= 1e6) {
      return `$${(marketCap / 1e6).toFixed(2)}M`
    }
    return `$${marketCap.toLocaleString()}`
  }

  if (error) {
    return (
      <Card className={cn("border-destructive/50", className)}>
        <CardContent className="p-4">
          <div className="text-center text-destructive">
            <p className="text-sm">Erreur de chargement</p>
            <p className="text-xs text-muted-foreground">{error}</p>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleRefresh}
              className="mt-2"
            >
              <RefreshCw className="w-3 h-3 mr-1" />
              Réessayer
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={cn("transition-all duration-200 hover:shadow-md", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CardTitle className="text-lg font-bold">{symbol}</CardTitle>
            {isShariKing && (
              <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 border-yellow-300">
                <Crown className="w-3 h-3 mr-1" />
                Sharia King
              </Badge>
            )}
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefresh}
            disabled={isRefreshing || loading}
            className="h-8 w-8 p-0"
          >
            <RefreshCw className={cn("w-4 h-4", (isRefreshing || loading) && "animate-spin")} />
          </Button>
        </div>
        {name && (
          <p className="text-sm text-muted-foreground truncate">{name}</p>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Prix principal */}
        <div className="flex items-center justify-between">
          <div>
            <div className="text-2xl font-bold">
              {quote ? formatPrice(quote.regularMarketPrice) : (
                <div className="h-8 w-20 bg-muted animate-pulse rounded" />
              )}
            </div>
            {quote && (
              <div className={cn(
                "flex items-center gap-1 text-sm",
                quote.regularMarketChange >= 0 ? "text-green-600" : "text-red-600"
              )}>
                {quote.regularMarketChange >= 0 ? (
                  <TrendingUp className="w-3 h-3" />
                ) : (
                  <TrendingDown className="w-3 h-3" />
                )}
                <span>{formatPrice(Math.abs(quote.regularMarketChange))}</span>
                <span>({formatPercentage(quote.regularMarketChangePercent)})</span>
              </div>
            )}
          </div>
          
          {quote?.exchangeName && (
            <Badge variant="outline" className="text-xs">
              {quote.exchangeName}
            </Badge>
          )}
        </div>

        {/* Détails supplémentaires */}
        {showDetails && quote && (
          <div className="grid grid-cols-2 gap-4 pt-2 border-t">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Cap. boursière</span>
                <span className="font-medium">
                  {quote.marketCap ? formatMarketCap(quote.marketCap) : 'N/A'}
                </span>
              </div>
              
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Volume</span>
                <span className="font-medium">
                  {quote.regularMarketVolume ? quote.regularMarketVolume.toLocaleString() : 'N/A'}
                </span>
              </div>
              
              {quote.trailingPE && (
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">P/E</span>
                  <span className="font-medium">{quote.trailingPE.toFixed(2)}</span>
                </div>
              )}
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">52W Min</span>
                <span className="font-medium">{formatPrice(quote.fiftyTwoWeekLow)}</span>
              </div>
              
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">52W Max</span>
                <span className="font-medium">{formatPrice(quote.fiftyTwoWeekHigh)}</span>
              </div>
              
              {quote.dividendYield && (
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Dividende</span>
                  <span className="font-medium">{formatPercentage(quote.dividendYield * 100)}</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Ratios Sharia */}
        {stats?.shariaRatios && (
          <div className="pt-2 border-t">
            <div className="flex items-center gap-2 mb-2">
              <BarChart3 className="w-4 h-4 text-primary" />
              <span className="text-sm font-medium">Conformité Sharia</span>
              <Badge 
                variant={stats.shariaRatios.isCompliant ? "default" : "destructive"}
                className="text-xs"
              >
                {stats.shariaRatios.isCompliant ? "Conforme" : "Non conforme"}
              </Badge>
            </div>
            
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Dette</span>
                <span className={cn(
                  "font-medium",
                  stats.shariaRatios.debtRatio <= 33 ? "text-green-600" : "text-red-600"
                )}>
                  {stats.shariaRatios.debtRatio.toFixed(1)}%
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-muted-foreground">Liquidité</span>
                <span className={cn(
                  "font-medium",
                  stats.shariaRatios.liquidityRatio <= 33 ? "text-green-600" : "text-red-600"
                )}>
                  {stats.shariaRatios.liquidityRatio.toFixed(1)}%
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Dernière mise à jour */}
        {lastUpdated && (
          <div className="text-xs text-muted-foreground text-center pt-2 border-t">
            Mis à jour: {lastUpdated.toLocaleTimeString()}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default RealTimeStockCard
