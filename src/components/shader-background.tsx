'use client'

import { useEffect, useState } from 'react'

interface ShaderBackgroundProps {
  variant?: 'aurora' | 'mesh' | 'liquid'
  className?: string
  intensity?: 'low' | 'medium' | 'high'
}

export function ShaderBackground({ 
  variant = 'aurora', 
  className = '',
  intensity = 'medium'
}: ShaderBackgroundProps) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) return null

  const getIntensityClass = () => {
    const isDark = document.documentElement.classList.contains('dark')

    if (isDark) {
      // Mode sombre - intensité plus élevée pour plus de visibilité
      switch (intensity) {
        case 'low': return 'opacity-50'
        case 'medium': return 'opacity-70'
        case 'high': return 'opacity-90'
        default: return 'opacity-70'
      }
    } else {
      // Mode clair - intensité augmentée pour plus de visibilité
      switch (intensity) {
        case 'low': return 'opacity-40'
        case 'medium': return 'opacity-60'
        case 'high': return 'opacity-80'
        default: return 'opacity-60'
      }
    }
  }

  // Fonction pour générer des nombres pseudo-aléatoires déterministes
  const seededRandom = (seed: number) => {
    const x = Math.sin(seed) * 10000
    return x - Math.floor(x)
  }

  const renderAurora = () => (
    <div className={`fixed inset-0 pointer-events-none z-0 ${getIntensityClass()} ${className}`}>
      {/* Couche principale d'aurora */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/20 via-transparent to-primary/10 animate-pulse"
           style={{ animationDuration: '8s' }} />

      {/* Vagues d'aurora */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent via-primary/15 to-transparent transform rotate-12 animate-bounce"
             style={{ animationDuration: '12s' }} />
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-l from-transparent via-green-400/10 to-transparent transform -rotate-12 animate-bounce"
             style={{ animationDuration: '15s', animationDelay: '2s' }} />
      </div>

      {/* Particules flottantes avec positions déterministes */}
      <div className="absolute inset-0">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-primary/30 rounded-full animate-ping"
            style={{
              left: `${seededRandom(i * 123.456) * 100}%`,
              top: `${seededRandom(i * 789.012) * 100}%`,
              animationDelay: `${seededRandom(i * 345.678) * 5}s`,
              animationDuration: `${3 + seededRandom(i * 901.234) * 4}s`
            }}
          />
        ))}
      </div>
    </div>
  )

  const renderMesh = () => (
    <div className={`fixed inset-0 pointer-events-none z-0 ${getIntensityClass()} ${className}`}>
      {/* Gradient de base */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-green-300/5 to-emerald-400/10" />
      
      {/* Mesh pattern */}
      <div className="absolute inset-0 opacity-40">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-primary/20 to-transparent transform skew-y-12 animate-pulse"
             style={{ animationDuration: '6s' }} />
        <div className="absolute inset-0 bg-gradient-to-l from-transparent via-green-400/15 to-transparent transform -skew-y-12 animate-pulse"
             style={{ animationDuration: '8s', animationDelay: '1s' }} />
      </div>
      
      {/* Cercles animés avec positions déterministes */}
      <div className="absolute inset-0">
        {[...Array(8)].map((_, i) => (
          <div
            key={i}
            className="absolute border border-primary/20 rounded-full animate-ping"
            style={{
              width: `${100 + i * 50}px`,
              height: `${100 + i * 50}px`,
              left: `${20 + (i % 3) * 30}%`,
              top: `${10 + (i % 4) * 25}%`,
              animationDelay: `${i * 0.5}s`,
              animationDuration: `${4 + i * 0.5}s`
            }}
          />
        ))}
      </div>
    </div>
  )

  const renderLiquid = () => (
    <div className={`fixed inset-0 pointer-events-none z-0 ${getIntensityClass()} ${className}`}>
      {/* Fond liquide */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/15 to-green-400/10" />
      
      {/* Bulles liquides avec positions déterministes */}
      <div className="absolute inset-0">
        {[...Array(12)].map((_, i) => (
          <div
            key={i}
            className="absolute bg-gradient-to-br from-primary/30 to-green-400/20 rounded-full blur-xl animate-bounce"
            style={{
              width: `${50 + seededRandom(i * 111.111) * 100}px`,
              height: `${50 + seededRandom(i * 222.222) * 100}px`,
              left: `${seededRandom(i * 333.333) * 90}%`,
              top: `${seededRandom(i * 444.444) * 90}%`,
              animationDelay: `${seededRandom(i * 555.555) * 3}s`,
              animationDuration: `${4 + seededRandom(i * 666.666) * 6}s`
            }}
          />
        ))}
      </div>
      
      {/* Vagues liquides */}
      <div className="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-primary/20 to-transparent">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-primary/30 to-transparent animate-pulse transform origin-bottom scale-y-50"
             style={{ animationDuration: '5s' }} />
      </div>
    </div>
  )

  switch (variant) {
    case 'aurora':
      return renderAurora()
    case 'mesh':
      return renderMesh()
    case 'liquid':
      return renderLiquid()
    default:
      return renderAurora()
  }
}

// Composant combiné pour des effets plus complexes
export function CombinedShaderBackground({ className = '' }: { className?: string }) {
  return (
    <div className={`fixed inset-0 pointer-events-none z-0 ${className}`}>
      <ShaderBackground variant="aurora" intensity="low" />
      <ShaderBackground variant="mesh" intensity="low" />
      <div className="absolute inset-0 bg-gradient-to-br from-background/80 to-background/60" />
    </div>
  )
}
