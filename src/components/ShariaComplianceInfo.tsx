'use client'

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { EXCLUDED_COMPANIES, EXCLUDED_SECTORS } from '@/types'
import { getExclusionReason } from '@/utils/shariaScreening'
import { Shield, AlertTriangle, Info } from 'lucide-react'

export function ShariaComplianceInfo() {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5 text-green-500" />
            Critères de Conformité Sharia
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-medium mb-2">Ratios Financiers (AAOIFI)</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="p-3 bg-muted/50 rounded-lg">
                <div className="font-medium">Ratio de Dette</div>
                <div className="text-muted-foreground">Maximum 33%</div>
              </div>
              <div className="p-3 bg-muted/50 rounded-lg">
                <div className="font-medium">Revenus Non-Halal</div>
                <div className="text-muted-foreground">Maximum 5%</div>
              </div>
              <div className="p-3 bg-muted/50 rounded-lg">
                <div className="font-medium">Ratio de Liquidité</div>
                <div className="text-muted-foreground">Maximum 33%</div>
              </div>
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-2">Secteurs Exclus</h4>
            <div className="flex flex-wrap gap-2">
              {EXCLUDED_SECTORS.map((sector) => (
                <Badge key={sector} variant="destructive" className="text-xs">
                  {sector}
                </Badge>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-orange-500" />
            Entreprises Spécifiquement Exclues
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert className="mb-4">
            <Info className="h-4 w-4" />
            <AlertDescription>
              Ces entreprises sont exclues en raison de leur modèle d'affaires basé sur du contenu 
              ou des services non conformes aux principes islamiques.
            </AlertDescription>
          </Alert>

          <div className="space-y-3">
            {EXCLUDED_COMPANIES.map((symbol) => (
              <div key={symbol} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <Badge variant="destructive">{symbol}</Badge>
                  <span className="text-sm text-muted-foreground">
                    {getCompanyName(symbol)}
                  </span>
                </div>
                <div className="text-sm text-muted-foreground max-w-xs text-right">
                  {getExclusionReason(symbol)}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5 text-yellow-500" />
            Sharia Kings
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              Les "Sharia Kings" sont des entreprises qui maintiennent leur conformité Sharia 
              depuis plus de 10 ans, offrant une stabilité et une fiabilité supplémentaires 
              pour les investisseurs musulmans.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    </div>
  )
}

function getCompanyName(symbol: string): string {
  const companyNames: Record<string, string> = {
    'GOOGL': 'Alphabet Inc.',
    'GOOG': 'Alphabet Inc. (Class A)',
    'META': 'Meta Platforms Inc.',
    'FB': 'Meta Platforms Inc.',
    'SNAP': 'Snap Inc.',
    'TWTR': 'Twitter Inc.',
    'X': 'X Corp.',
    'PINS': 'Pinterest Inc.',
    'ROKU': 'Roku Inc.',
    'NFLX': 'Netflix Inc.',
    'DIS': 'The Walt Disney Company',
    'SPOT': 'Spotify Technology S.A.'
  }

  return companyNames[symbol] || 'Entreprise non identifiée'
}

export default ShariaComplianceInfo
