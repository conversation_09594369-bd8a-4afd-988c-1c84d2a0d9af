'use client'

import React, { useState, useCallback } from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'
import { 
  getSliderConfig, 
  valueToSliderPercentage, 
  sliderPercentageToValue,
  SliderConfig 
} from '@/utils/sliderMapping'

interface SmartSliderProps {
  value: number
  onChange: (value: number) => void
  questionId: string
  className?: string
  disabled?: boolean
}

export function SmartSlider({ 
  value, 
  onChange, 
  questionId, 
  className,
  disabled = false 
}: SmartSliderProps) {
  const config = getSliderConfig(questionId)
  const [isDragging, setIsDragging] = useState(false)
  
  // Convertir la valeur en pourcentage pour l'affichage
  const sliderPercentage = valueToSliderPercentage(value, config)
  
  const handleSliderChange = useCallback((newPercentage: number) => {
    const newValue = sliderPercentageToValue(newPercentage, config)
    onChange(newValue)
  }, [onChange, config])

  const handleMouseDown = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    if (disabled) return
    
    setIsDragging(true)
    const rect = e.currentTarget.getBoundingClientRect()
    const percentage = Math.max(0, Math.min(100, ((e.clientX - rect.left) / rect.width) * 100))
    handleSliderChange(percentage)
  }, [disabled, handleSliderChange])

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || disabled) return
    
    const sliderElement = document.querySelector('[data-slider="true"]') as HTMLElement
    if (!sliderElement) return
    
    const rect = sliderElement.getBoundingClientRect()
    const percentage = Math.max(0, Math.min(100, ((e.clientX - rect.left) / rect.width) * 100))
    handleSliderChange(percentage)
  }, [isDragging, disabled, handleSliderChange])

  const handleMouseUp = useCallback(() => {
    setIsDragging(false)
  }, [])

  // Touch events pour mobile
  const handleTouchStart = useCallback((e: React.TouchEvent<HTMLDivElement>) => {
    if (disabled) return
    
    setIsDragging(true)
    const rect = e.currentTarget.getBoundingClientRect()
    const touch = e.touches[0]
    const percentage = Math.max(0, Math.min(100, ((touch.clientX - rect.left) / rect.width) * 100))
    handleSliderChange(percentage)
  }, [disabled, handleSliderChange])

  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (!isDragging || disabled) return
    
    e.preventDefault() // Empêcher le scroll
    const sliderElement = document.querySelector('[data-slider="true"]') as HTMLElement
    if (!sliderElement) return
    
    const rect = sliderElement.getBoundingClientRect()
    const touch = e.touches[0]
    const percentage = Math.max(0, Math.min(100, ((touch.clientX - rect.left) / rect.width) * 100))
    handleSliderChange(percentage)
  }, [isDragging, disabled, handleSliderChange])

  const handleTouchEnd = useCallback(() => {
    setIsDragging(false)
  }, [])

  // Ajouter les event listeners globaux
  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      document.addEventListener('touchmove', handleTouchMove, { passive: false })
      document.addEventListener('touchend', handleTouchEnd)
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
        document.removeEventListener('touchmove', handleTouchMove)
        document.removeEventListener('touchend', handleTouchEnd)
      }
    }
  }, [isDragging, handleMouseMove, handleMouseUp, handleTouchMove, handleTouchEnd])

  return (
    <div className={cn("relative w-full", className)}>
      {/* Track du slider */}
      <div
        data-slider="true"
        className={cn(
          "relative h-3 bg-gray-200 rounded-full cursor-pointer touch-none",
          "md:h-2", // Plus fin sur desktop
          disabled && "opacity-50 cursor-not-allowed"
        )}
        onMouseDown={handleMouseDown}
        onTouchStart={handleTouchStart}
      >
        {/* Progress bar */}
        <div
          className="absolute top-0 left-0 h-full bg-gradient-to-r from-green-500 to-green-600 rounded-full transition-all duration-200"
          style={{ width: `${sliderPercentage}%` }}
        />
        
        {/* Thumb */}
        <motion.div
          className={cn(
            "absolute top-1/2 -translate-y-1/2 w-6 h-6 bg-white border-2 border-green-500 rounded-full shadow-lg cursor-grab",
            "md:w-5 md:h-5", // Plus petit sur desktop
            "touch-manipulation", // Optimisation touch
            isDragging && "cursor-grabbing scale-110",
            disabled && "cursor-not-allowed"
          )}
          style={{ left: `calc(${sliderPercentage}% - 12px)` }}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 1.2 }}
          animate={{
            scale: isDragging ? 1.2 : 1,
            boxShadow: isDragging 
              ? "0 8px 25px rgba(34, 197, 94, 0.4)" 
              : "0 4px 15px rgba(0, 0, 0, 0.1)"
          }}
        />
      </div>
      
      {/* Marqueurs pour les breakpoints importants */}
      <div className="absolute top-0 w-full h-3 md:h-2 pointer-events-none">
        {config.breakpoints.slice(1, -1).map((breakpoint, index) => (
          <div
            key={index}
            className="absolute top-1/2 -translate-y-1/2 w-1 h-1 bg-gray-400 rounded-full opacity-50"
            style={{ left: `${breakpoint.percentage}%` }}
          />
        ))}
      </div>
      
      {/* Labels des valeurs min/max */}
      <div className="flex justify-between mt-2 text-xs text-gray-500">
        <span>{formatCurrency(config.min)}</span>
        <span>{formatCurrency(config.max)}</span>
      </div>
    </div>
  )
}

function formatCurrency(amount: number): string {
  if (amount >= 1000) {
    return `${Math.round(amount / 1000)}k€`
  }
  return `${amount}€`
}
