'use client'

import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

interface FloatingNextButtonProps {
  onClick: () => void
  disabled?: boolean
  isAnimating?: boolean
  isLastQuestion?: boolean
  className?: string
}

export function FloatingNextButton({
  onClick,
  disabled = false,
  isAnimating = false,
  isLastQuestion = false,
  className
}: FloatingNextButtonProps) {
  return (
    <motion.div
      className={cn(
        "fixed bottom-6 right-6 z-50",
        "md:bottom-8 md:right-8",
        className
      )}
      initial={{ opacity: 0, scale: 0.8, y: 20 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.5 }}
    >
      <motion.div
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <Button
          onClick={onClick}
          disabled={disabled || isAnimating}
          className={cn(
            "h-12 px-8 shadow-lg",
            "bg-green-600 hover:bg-green-700",
            "text-white font-medium",
            "transition-all duration-200",
            "touch-manipulation",
            disabled && "opacity-50 cursor-not-allowed"
          )}
        >
          {isLastQuestion ? 'Terminer ✨' : 'Suivant →'}
        </Button>
      </motion.div>
    </motion.div>
  )
}
