'use client'

import { useState, useEffect, useCallback } from 'react'
import { YahooFinanceQuote, YahooFinancialStats, YahooFinanceHistoricalData } from '@/types'

/**
 * Hook personnalisé pour récupérer les données financières en temps réel
 * Utilise les API routes créées pour Yahoo Finance avec cache intégré
 */

interface UseFinancialDataOptions {
  refreshInterval?: number // en millisecondes
  autoRefresh?: boolean
}

interface FinancialDataState {
  quote: YahooFinanceQuote | null
  stats: YahooFinancialStats | null
  historical: YahooFinanceHistoricalData[] | null
  loading: boolean
  error: string | null
  lastUpdated: Date | null
}

export function useFinancialData(symbol: string, options: UseFinancialDataOptions = {}) {
  const { refreshInterval = 60000, autoRefresh = true } = options // 1 minute par défaut

  const [state, setState] = useState<FinancialDataState>({
    quote: null,
    stats: null,
    historical: null,
    loading: false,
    error: null,
    lastUpdated: null
  })

  // Récupérer la cotation en temps réel
  const fetchQuote = useCallback(async () => {
    if (!symbol) return

    try {
      const response = await fetch(`/api/finance/quote?symbol=${symbol}`)
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch quote')
      }

      setState(prev => ({
        ...prev,
        quote: result.data,
        lastUpdated: new Date(),
        error: null
      }))

    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Unknown error'
      }))
    }
  }, [symbol])

  // Récupérer les statistiques financières
  const fetchStats = useCallback(async () => {
    if (!symbol) return

    try {
      const response = await fetch(`/api/finance/stats?symbol=${symbol}&sharia=true`)
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch stats')
      }

      setState(prev => ({
        ...prev,
        stats: result.data,
        lastUpdated: new Date(),
        error: null
      }))

    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Unknown error'
      }))
    }
  }, [symbol])

  // Récupérer l'historique des prix
  const fetchHistorical = useCallback(async (period: string = '1y') => {
    if (!symbol) return

    try {
      const response = await fetch(`/api/finance/historical?symbol=${symbol}&period=${period}`)
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch historical data')
      }

      setState(prev => ({
        ...prev,
        historical: result.data,
        lastUpdated: new Date(),
        error: null
      }))

    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Unknown error'
      }))
    }
  }, [symbol])

  // Récupérer toutes les données
  const fetchAllData = useCallback(async () => {
    if (!symbol) return

    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      await Promise.all([
        fetchQuote(),
        fetchStats(),
        fetchHistorical()
      ])
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Unknown error'
      }))
    } finally {
      setState(prev => ({ ...prev, loading: false }))
    }
  }, [symbol, fetchQuote, fetchStats, fetchHistorical])

  // Rafraîchir les données
  const refresh = useCallback(() => {
    fetchAllData()
  }, [fetchAllData])

  // Effet pour charger les données initiales
  useEffect(() => {
    if (symbol) {
      fetchAllData()
    }
  }, [symbol, fetchAllData])

  // Effet pour le rafraîchissement automatique
  useEffect(() => {
    if (!autoRefresh || !symbol) return

    const interval = setInterval(() => {
      fetchQuote() // Rafraîchir seulement la cotation pour les mises à jour fréquentes
    }, refreshInterval)

    return () => clearInterval(interval)
  }, [autoRefresh, symbol, refreshInterval, fetchQuote])

  return {
    ...state,
    refresh,
    fetchQuote,
    fetchStats,
    fetchHistorical
  }
}

/**
 * Hook pour récupérer plusieurs cotations en une fois
 */
export function useMultipleQuotes(symbols: string[], options: UseFinancialDataOptions = {}) {
  const { refreshInterval = 60000, autoRefresh = true } = options

  const [quotes, setQuotes] = useState<Record<string, YahooFinanceQuote>>({})
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)

  const fetchQuotes = useCallback(async () => {
    if (!symbols.length) return

    setLoading(true)
    setError(null)

    try {
      const symbolsParam = symbols.join(',')
      const response = await fetch(`/api/finance/quote?symbols=${symbolsParam}`)
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch quotes')
      }

      const quotesMap: Record<string, YahooFinanceQuote> = {}
      result.data.forEach((quote: YahooFinanceQuote) => {
        quotesMap[quote.symbol] = quote
      })

      setQuotes(quotesMap)
      setLastUpdated(new Date())

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }, [symbols])

  // Effet pour charger les données initiales
  useEffect(() => {
    if (symbols.length > 0) {
      fetchQuotes()
    }
  }, [symbols, fetchQuotes])

  // Effet pour le rafraîchissement automatique
  useEffect(() => {
    if (!autoRefresh || !symbols.length) return

    const interval = setInterval(fetchQuotes, refreshInterval)
    return () => clearInterval(interval)
  }, [autoRefresh, symbols.length, refreshInterval, fetchQuotes])

  return {
    quotes,
    loading,
    error,
    lastUpdated,
    refresh: fetchQuotes
  }
}

/**
 * Hook pour rechercher des actions
 */
export function useStockSearch() {
  const [results, setResults] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const search = useCallback(async (query: string) => {
    if (!query || query.length < 2) {
      setResults([])
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/finance/search?q=${encodeURIComponent(query)}`)
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to search stocks')
      }

      setResults(result.data)

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error')
      setResults([])
    } finally {
      setLoading(false)
    }
  }, [])

  const clearResults = useCallback(() => {
    setResults([])
    setError(null)
  }, [])

  return {
    results,
    loading,
    error,
    search,
    clearResults
  }
}
