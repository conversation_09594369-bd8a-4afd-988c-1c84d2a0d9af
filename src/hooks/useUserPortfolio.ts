'use client'

import { useState, useEffect, useCallback } from 'react'
import { useUser } from '@supabase/auth-helpers-react'
import { 
  UserPortfolio, 
  UserPortfolioPosition, 
  EnrichedPortfolioPosition,
  PortfolioSummary,
  UserWatchlist,
  UserRecommendation 
} from '@/types'

/**
 * Hook pour gérer les portefeuilles utilisateur
 */
export function useUserPortfolio() {
  const user = useUser()
  const [portfolios, setPortfolios] = useState<UserPortfolio[]>([])
  const [currentPortfolio, setCurrentPortfolio] = useState<UserPortfolio | null>(null)
  const [positions, setPositions] = useState<EnrichedPortfolioPosition[]>([])
  const [summary, setSummary] = useState<PortfolioSummary | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Récupérer tous les portefeuilles
  const fetchPortfolios = useCallback(async () => {
    if (!user) return

    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/user/portfolio?includeSummary=true')
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch portfolios')
      }

      setPortfolios(result.data)
      
      // Sélectionner le premier portefeuille par défaut
      if (result.data.length > 0 && !currentPortfolio) {
        setCurrentPortfolio(result.data[0])
      }

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }, [user, currentPortfolio])

  // Récupérer un portefeuille spécifique avec ses positions
  const fetchPortfolioDetails = useCallback(async (portfolioId: string) => {
    if (!user) return

    setLoading(true)
    setError(null)

    try {
      const [portfolioResponse, positionsResponse] = await Promise.all([
        fetch(`/api/user/portfolio?id=${portfolioId}&includeSummary=true`),
        fetch(`/api/user/portfolio/positions?portfolioId=${portfolioId}`)
      ])

      const portfolioResult = await portfolioResponse.json()
      const positionsResult = await positionsResponse.json()

      if (!portfolioResponse.ok) {
        throw new Error(portfolioResult.error || 'Failed to fetch portfolio')
      }

      if (!positionsResponse.ok) {
        throw new Error(positionsResult.error || 'Failed to fetch positions')
      }

      setCurrentPortfolio(portfolioResult.data.portfolio)
      setSummary(portfolioResult.data.summary)
      setPositions(positionsResult.data)

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }, [user])

  // Créer un nouveau portefeuille
  const createPortfolio = useCallback(async (portfolioData: {
    name: string
    description?: string
    targetAmount?: number
    riskTolerance?: 'conservative' | 'moderate' | 'aggressive'
  }) => {
    if (!user) return null

    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/user/portfolio', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(portfolioData)
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create portfolio')
      }

      await fetchPortfolios() // Rafraîchir la liste
      return result.data

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error')
      return null
    } finally {
      setLoading(false)
    }
  }, [user, fetchPortfolios])

  // Ajouter une position
  const addPosition = useCallback(async (positionData: {
    portfolioId: string
    symbol: string
    shares: number
    averagePrice: number
    targetAllocationPercent?: number
  }) => {
    if (!user) return null

    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/user/portfolio/positions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(positionData)
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to add position')
      }

      // Rafraîchir les positions
      if (currentPortfolio?.id === positionData.portfolioId) {
        await fetchPortfolioDetails(positionData.portfolioId)
      }

      return result.data

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error')
      return null
    } finally {
      setLoading(false)
    }
  }, [user, currentPortfolio, fetchPortfolioDetails])

  // Mettre à jour une position
  const updatePosition = useCallback(async (positionId: string, updates: {
    shares?: number
    averagePrice?: number
    targetAllocationPercent?: number
  }) => {
    if (!user) return null

    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/user/portfolio/positions', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ positionId, ...updates })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to update position')
      }

      // Rafraîchir les positions
      if (currentPortfolio) {
        await fetchPortfolioDetails(currentPortfolio.id)
      }

      return result.data

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error')
      return null
    } finally {
      setLoading(false)
    }
  }, [user, currentPortfolio, fetchPortfolioDetails])

  // Supprimer une position
  const removePosition = useCallback(async (positionId: string) => {
    if (!user) return false

    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/user/portfolio/positions?id=${positionId}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to remove position')
      }

      // Rafraîchir les positions
      if (currentPortfolio) {
        await fetchPortfolioDetails(currentPortfolio.id)
      }

      return true

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error')
      return false
    } finally {
      setLoading(false)
    }
  }, [user, currentPortfolio, fetchPortfolioDetails])

  // Calculer les métriques du portefeuille
  const calculateMetrics = useCallback(() => {
    if (!positions.length) {
      return {
        totalValue: 0,
        totalInvested: 0,
        totalGainLoss: 0,
        gainLossPercent: 0,
        shariaCompliantPercent: 0,
        topPerformer: null,
        worstPerformer: null
      }
    }

    const totalValue = positions.reduce((sum, pos) => sum + (pos.currentValue || 0), 0)
    const totalInvested = positions.reduce((sum, pos) => sum + pos.totalInvested, 0)
    const totalGainLoss = totalValue - totalInvested
    const gainLossPercent = totalInvested > 0 ? (totalGainLoss / totalInvested) * 100 : 0

    const shariaCompliantPositions = positions.filter(pos => pos.stockInfo?.isShariCompliant)
    const shariaCompliantPercent = positions.length > 0 
      ? (shariaCompliantPositions.length / positions.length) * 100 
      : 0

    const sortedByPerformance = [...positions]
      .filter(pos => pos.gainLossPercent !== undefined)
      .sort((a, b) => (b.gainLossPercent || 0) - (a.gainLossPercent || 0))

    return {
      totalValue,
      totalInvested,
      totalGainLoss,
      gainLossPercent,
      shariaCompliantPercent,
      topPerformer: sortedByPerformance[0] || null,
      worstPerformer: sortedByPerformance[sortedByPerformance.length - 1] || null
    }
  }, [positions])

  // Charger les portefeuilles au montage
  useEffect(() => {
    if (user) {
      fetchPortfolios()
    }
  }, [user, fetchPortfolios])

  return {
    // État
    portfolios,
    currentPortfolio,
    positions,
    summary,
    loading,
    error,
    
    // Actions
    fetchPortfolios,
    fetchPortfolioDetails,
    createPortfolio,
    addPosition,
    updatePosition,
    removePosition,
    setCurrentPortfolio,
    
    // Métriques calculées
    metrics: calculateMetrics()
  }
}

/**
 * Hook pour gérer la watchlist utilisateur
 */
export function useUserWatchlist() {
  const user = useUser()
  const [watchlist, setWatchlist] = useState<UserWatchlist[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Récupérer la watchlist
  const fetchWatchlist = useCallback(async () => {
    if (!user) return

    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/user/watchlist')
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch watchlist')
      }

      setWatchlist(result.data)

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }, [user])

  // Ajouter à la watchlist
  const addToWatchlist = useCallback(async (symbol: string, options?: {
    notes?: string
    targetPrice?: number
    alertEnabled?: boolean
  }) => {
    if (!user) return false

    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/user/watchlist', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ symbol, ...options })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to add to watchlist')
      }

      await fetchWatchlist() // Rafraîchir
      return true

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error')
      return false
    } finally {
      setLoading(false)
    }
  }, [user, fetchWatchlist])

  // Supprimer de la watchlist
  const removeFromWatchlist = useCallback(async (symbol: string) => {
    if (!user) return false

    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/user/watchlist?symbol=${symbol}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to remove from watchlist')
      }

      await fetchWatchlist() // Rafraîchir
      return true

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error')
      return false
    } finally {
      setLoading(false)
    }
  }, [user, fetchWatchlist])

  // Vérifier si un symbole est dans la watchlist
  const isInWatchlist = useCallback((symbol: string) => {
    return watchlist.some(item => item.symbol === symbol.toUpperCase())
  }, [watchlist])

  // Charger la watchlist au montage
  useEffect(() => {
    if (user) {
      fetchWatchlist()
    }
  }, [user, fetchWatchlist])

  return {
    watchlist,
    loading,
    error,
    fetchWatchlist,
    addToWatchlist,
    removeFromWatchlist,
    isInWatchlist
  }
}

/**
 * Hook pour gérer les recommandations utilisateur
 */
export function useUserRecommendations() {
  const user = useUser()
  const [recommendations, setRecommendations] = useState<UserRecommendation[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Récupérer les recommandations
  const fetchRecommendations = useCallback(async (options?: {
    type?: string
    unreadOnly?: boolean
    limit?: number
  }) => {
    if (!user) return

    setLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams()
      if (options?.type) params.append('type', options.type)
      if (options?.unreadOnly) params.append('unreadOnly', 'true')
      if (options?.limit) params.append('limit', options.limit.toString())

      const response = await fetch(`/api/user/recommendations?${params}`)
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch recommendations')
      }

      setRecommendations(result.data)

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }, [user])

  // Générer de nouvelles recommandations
  const generateRecommendations = useCallback(async () => {
    if (!user) return false

    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/user/recommendations', {
        method: 'POST'
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to generate recommendations')
      }

      await fetchRecommendations() // Rafraîchir
      return true

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error')
      return false
    } finally {
      setLoading(false)
    }
  }, [user, fetchRecommendations])

  // Marquer comme lu
  const markAsRead = useCallback(async (recommendationId: string) => {
    if (!user) return false

    try {
      const response = await fetch('/api/user/recommendations', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ recommendationId, isRead: true })
      })

      if (response.ok) {
        setRecommendations(prev => 
          prev.map(rec => 
            rec.id === recommendationId ? { ...rec, isRead: true } : rec
          )
        )
        return true
      }

    } catch (error) {
      console.error('Error marking recommendation as read:', error)
    }

    return false
  }, [user])

  // Charger les recommandations au montage
  useEffect(() => {
    if (user) {
      fetchRecommendations()
    }
  }, [user, fetchRecommendations])

  return {
    recommendations,
    loading,
    error,
    fetchRecommendations,
    generateRecommendations,
    markAsRead,
    unreadCount: recommendations.filter(rec => !rec.isRead).length
  }
}
