'use client'

import { useState, useEffect, useCallback } from 'react'
import { RevenueBreakdown, SankeyData } from '@/types'

interface UseRevenueAnalysisOptions {
  autoRefresh?: boolean
  refreshInterval?: number
}

interface RevenueAnalysisState {
  revenueData: RevenueBreakdown | null
  sankeyData: SankeyData | null
  loading: boolean
  error: string | null
  lastUpdated: Date | null
}

export function useRevenueAnalysis(
  symbol: string | null,
  options: UseRevenueAnalysisOptions = {}
) {
  const { autoRefresh = false, refreshInterval = 300000 } = options // 5 minutes par défaut

  const [state, setState] = useState<RevenueAnalysisState>({
    revenueData: null,
    sankeyData: null,
    loading: false,
    error: null,
    lastUpdated: null
  })

  // Récupérer les données de revenus
  const fetchRevenueData = useCallback(async () => {
    if (!symbol) return

    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const response = await fetch(`/api/finance/revenue?symbol=${symbol}&format=sankey`)
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch revenue data')
      }

      setState(prev => ({
        ...prev,
        revenueData: result.data,
        sankeyData: result.sankeyData,
        lastUpdated: new Date(),
        error: null,
        loading: false
      }))

    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Unknown error',
        loading: false
      }))
    }
  }, [symbol])

  // Rafraîchir les données
  const refresh = useCallback(() => {
    fetchRevenueData()
  }, [fetchRevenueData])

  // Effet pour charger les données initiales
  useEffect(() => {
    if (symbol) {
      fetchRevenueData()
    } else {
      setState({
        revenueData: null,
        sankeyData: null,
        loading: false,
        error: null,
        lastUpdated: null
      })
    }
  }, [symbol, fetchRevenueData])

  // Effet pour le rafraîchissement automatique
  useEffect(() => {
    if (!autoRefresh || !symbol) return

    const interval = setInterval(() => {
      fetchRevenueData()
    }, refreshInterval)

    return () => clearInterval(interval)
  }, [autoRefresh, symbol, refreshInterval, fetchRevenueData])

  return {
    ...state,
    refresh,
    isStale: state.lastUpdated ? Date.now() - state.lastUpdated.getTime() > refreshInterval : false
  }
}

/**
 * Hook pour analyser plusieurs entreprises
 */
export function useMultipleRevenueAnalysis(symbols: string[]) {
  const [analyses, setAnalyses] = useState<Record<string, RevenueBreakdown>>({})
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchMultipleAnalyses = useCallback(async () => {
    if (symbols.length === 0) return

    setLoading(true)
    setError(null)

    try {
      const promises = symbols.map(async (symbol) => {
        const response = await fetch(`/api/finance/revenue?symbol=${symbol}`)
        const result = await response.json()
        
        if (response.ok) {
          return { symbol, data: result.data }
        } else {
          console.warn(`Failed to fetch revenue data for ${symbol}:`, result.error)
          return { symbol, data: null }
        }
      })

      const results = await Promise.all(promises)
      
      const analysesMap: Record<string, RevenueBreakdown> = {}
      results.forEach(({ symbol, data }) => {
        if (data) {
          analysesMap[symbol] = data
        }
      })

      setAnalyses(analysesMap)

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }, [symbols])

  useEffect(() => {
    fetchMultipleAnalyses()
  }, [fetchMultipleAnalyses])

  return {
    analyses,
    loading,
    error,
    refresh: fetchMultipleAnalyses
  }
}

/**
 * Hook pour comparer la conformité Sharia de plusieurs entreprises
 */
export function useShariaComplianceComparison(symbols: string[]) {
  const { analyses, loading, error } = useMultipleRevenueAnalysis(symbols)

  const comparison = Object.entries(analyses).map(([symbol, data]) => ({
    symbol,
    companyName: data.companyName,
    totalRevenue: data.totalRevenue,
    halalPercentage: data.shariaCompliance.halalPercentage,
    haramPercentage: data.shariaCompliance.haramPercentage,
    overallRating: data.shariaCompliance.overallRating,
    isCompliant: data.shariaCompliance.overallRating === 'compliant'
  })).sort((a, b) => b.halalPercentage - a.halalPercentage)

  return {
    comparison,
    loading,
    error,
    compliantCount: comparison.filter(c => c.isCompliant).length,
    totalCount: comparison.length
  }
}
