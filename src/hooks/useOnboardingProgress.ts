'use client'

import { useState, useEffect, useCallback } from 'react'
import Cookies from 'js-cookie'

const ONBOARDING_COOKIE_KEY = 'halal_invest_onboarding_progress'
const COOKIE_EXPIRY_DAYS = 30 // 30 jours d'expiration

interface OnboardingProgress {
  answers: Record<string, any>
  currentStepIndex: number
  currentQuestionIndex: number
  lastUpdated: string
}

export function useOnboardingProgress() {
  const [answers, setAnswers] = useState<Record<string, any>>({})
  const [currentStepIndex, setCurrentStepIndex] = useState(0)
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [isLoaded, setIsLoaded] = useState(false)
  const [saveTimeout, setSaveTimeout] = useState<NodeJS.Timeout | null>(null)

  // Charger les données depuis les cookies au démarrage
  useEffect(() => {
    const savedProgress = Cookies.get(ONBOARDING_COOKIE_KEY)
    
    if (savedProgress) {
      try {
        const progress: OnboardingProgress = JSON.parse(savedProgress)
        
        // Vérifier que les données ne sont pas trop anciennes (plus de 30 jours)
        const lastUpdated = new Date(progress.lastUpdated)
        const now = new Date()
        const daysDiff = (now.getTime() - lastUpdated.getTime()) / (1000 * 3600 * 24)
        
        if (daysDiff <= COOKIE_EXPIRY_DAYS) {
          setAnswers(progress.answers || {})
          setCurrentStepIndex(progress.currentStepIndex || 0)
          setCurrentQuestionIndex(progress.currentQuestionIndex || 0)
          
          console.log('🔄 Progression d\'onboarding restaurée depuis les cookies', {
            answersCount: Object.keys(progress.answers || {}).length,
            step: progress.currentStepIndex,
            question: progress.currentQuestionIndex
          })
        } else {
          // Données trop anciennes, les supprimer
          Cookies.remove(ONBOARDING_COOKIE_KEY)
          console.log('🗑️ Données d\'onboarding expirées, suppression des cookies')
        }
      } catch (error) {
        console.error('❌ Erreur lors du chargement de la progression:', error)
        Cookies.remove(ONBOARDING_COOKIE_KEY)
      }
    }
    
    setIsLoaded(true)
  }, [])

  // Nettoyer le timeout au démontage
  useEffect(() => {
    return () => {
      if (saveTimeout) {
        clearTimeout(saveTimeout)
      }
    }
  }, [saveTimeout])

  // Sauvegarder automatiquement dans les cookies
  const saveProgress = useCallback((
    newAnswers: Record<string, any>,
    stepIndex: number,
    questionIndex: number
  ) => {
    const progress: OnboardingProgress = {
      answers: newAnswers,
      currentStepIndex: stepIndex,
      currentQuestionIndex: questionIndex,
      lastUpdated: new Date().toISOString()
    }

    try {
      Cookies.set(ONBOARDING_COOKIE_KEY, JSON.stringify(progress), {
        expires: COOKIE_EXPIRY_DAYS,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax'
      })
      
      console.log('💾 Progression sauvegardée automatiquement', {
        answersCount: Object.keys(newAnswers).length,
        step: stepIndex,
        question: questionIndex
      })
    } catch (error) {
      console.error('❌ Erreur lors de la sauvegarde:', error)
    }
  }, [])

  // Mettre à jour une réponse et sauvegarder automatiquement avec debounce
  const updateAnswer = useCallback((questionId: string, value: any) => {
    setAnswers(prev => {
      const newAnswers = { ...prev, [questionId]: value }

      // Annuler la sauvegarde précédente si elle existe
      if (saveTimeout) {
        clearTimeout(saveTimeout)
      }

      // Programmer une nouvelle sauvegarde avec debounce
      const newTimeout = setTimeout(() => {
        saveProgress(newAnswers, currentStepIndex, currentQuestionIndex)
        setSaveTimeout(null)
      }, 500) // Délai de 500ms pour le debounce

      setSaveTimeout(newTimeout)

      return newAnswers
    })
  }, [currentStepIndex, currentQuestionIndex, saveProgress, saveTimeout])

  // Mettre à jour la position et sauvegarder
  const updatePosition = useCallback((stepIndex: number, questionIndex: number) => {
    setCurrentStepIndex(stepIndex)
    setCurrentQuestionIndex(questionIndex)
    
    // Sauvegarder la nouvelle position
    setTimeout(() => {
      saveProgress(answers, stepIndex, questionIndex)
    }, 100)
  }, [answers, saveProgress])

  // Effacer la progression (quand l'onboarding est terminé)
  const clearProgress = useCallback(() => {
    Cookies.remove(ONBOARDING_COOKIE_KEY)
    setAnswers({})
    setCurrentStepIndex(0)
    setCurrentQuestionIndex(0)
    console.log('🧹 Progression d\'onboarding effacée')
  }, [])

  // Vérifier s'il y a une progression sauvegardée
  const hasProgress = useCallback(() => {
    return Object.keys(answers).length > 0
  }, [answers])

  // Obtenir un résumé de la progression
  const getProgressSummary = useCallback(() => {
    const answersCount = Object.keys(answers).length
    return {
      answersCount,
      currentStep: currentStepIndex + 1,
      currentQuestion: currentQuestionIndex + 1,
      hasProgress: answersCount > 0,
      lastAnswer: answersCount > 0 ? Object.keys(answers).pop() : null
    }
  }, [answers, currentStepIndex, currentQuestionIndex])

  return {
    // État
    answers,
    currentStepIndex,
    currentQuestionIndex,
    isLoaded,
    
    // Actions
    updateAnswer,
    updatePosition,
    clearProgress,
    
    // Utilitaires
    hasProgress,
    getProgressSummary,
    
    // Pour compatibilité avec l'ancien code
    setAnswers,
    setCurrentStepIndex,
    setCurrentQuestionIndex
  }
}
