import { supabase } from './supabase'
import { getQuote, getFinancialStats } from './yahoo-finance'

/**
 * Moteur de recommandations personnalisées pour les investissements Sharia
 * Génère des recommandations basées sur le profil utilisateur et les critères Sharia
 */

export interface RecommendationCriteria {
  riskTolerance: 'conservative' | 'moderate' | 'aggressive'
  targetAmount?: number
  sectors?: string[]
  excludeSymbols?: string[]
  maxRecommendations?: number
}

export interface RecommendationScore {
  symbol: string
  score: number
  reasons: string[]
  confidenceScore: number
  targetAllocation: number
  targetPrice?: number
}

/**
 * Calcule le score de recommandation pour une action
 */
export const calculateRecommendationScore = async (
  symbol: string,
  criteria: RecommendationCriteria
): Promise<RecommendationScore | null> => {
  try {
    // Récupérer les données de l'action
    const { data: stock, error } = await supabase
      .from('stocks_with_realtime_data')
      .select('*')
      .eq('symbol', symbol)
      .single()

    if (error || !stock) {
      return null
    }

    // Vérifier la conformité Sharia
    if (!stock.is_sharia_compliant) {
      return null
    }

    let score = 0
    const reasons: string[] = []
    let confidenceScore = 0.5

    // 1. Score de base pour la conformité Sharia (20 points)
    score += 20
    reasons.push('Conforme aux critères Sharia')

    // 2. Bonus pour Sharia King (15 points)
    if (stock.sharia_king_since) {
      score += 15
      confidenceScore += 0.2
      const years = new Date().getFullYear() - new Date(stock.sharia_king_since).getFullYear()
      reasons.push(`Sharia King depuis ${years} ans`)
    }

    // 3. Score basé sur la capitalisation boursière (10 points max)
    if (stock.market_cap) {
      if (stock.market_cap > 100000000000) { // > 100B
        score += 10
        reasons.push('Grande capitalisation stable')
        confidenceScore += 0.1
      } else if (stock.market_cap > 10000000000) { // > 10B
        score += 7
        reasons.push('Capitalisation moyenne')
      } else if (stock.market_cap > 1000000000) { // > 1B
        score += 4
        reasons.push('Petite capitalisation')
      }
    }

    // 4. Score basé sur les ratios financiers (15 points max)
    if (stock.calculated_debt_ratio !== null && stock.calculated_liquidity_ratio !== null) {
      // Ratio de dette (plus bas = mieux)
      if (stock.calculated_debt_ratio <= 10) {
        score += 8
        reasons.push('Très faible endettement')
        confidenceScore += 0.1
      } else if (stock.calculated_debt_ratio <= 20) {
        score += 5
        reasons.push('Faible endettement')
      } else if (stock.calculated_debt_ratio <= 33) {
        score += 2
        reasons.push('Endettement acceptable')
      }

      // Ratio de liquidité (modéré = mieux)
      if (stock.calculated_liquidity_ratio >= 10 && stock.calculated_liquidity_ratio <= 25) {
        score += 7
        reasons.push('Liquidité optimale')
        confidenceScore += 0.1
      } else if (stock.calculated_liquidity_ratio <= 33) {
        score += 3
        reasons.push('Liquidité acceptable')
      }
    }

    // 5. Score basé sur la performance récente (10 points max)
    if (stock.price_change_percent !== null) {
      if (stock.price_change_percent > 0) {
        score += Math.min(5, stock.price_change_percent / 2)
        reasons.push('Performance positive récente')
      }
    }

    // 6. Ajustement selon le profil de risque
    const riskMultiplier = {
      conservative: 0.8,
      moderate: 1.0,
      aggressive: 1.2
    }[criteria.riskTolerance]

    score *= riskMultiplier

    // 7. Bonus pour certains secteurs selon le profil de risque
    const sectorBonus = getSectorBonus(stock.sector, criteria.riskTolerance)
    score += sectorBonus.score
    if (sectorBonus.reason) {
      reasons.push(sectorBonus.reason)
    }

    // 8. Calcul de l'allocation cible
    const targetAllocation = calculateTargetAllocation(score, criteria.riskTolerance, stock)

    // 9. Calcul du prix cible (optionnel)
    let targetPrice: number | undefined
    try {
      const quote = await getQuote(symbol)
      if (quote.regularMarketPrice > 0) {
        // Prix cible basé sur le score (entre 5% et 25% d'augmentation)
        const growthPotential = Math.min(0.25, Math.max(0.05, score / 400))
        targetPrice = quote.regularMarketPrice * (1 + growthPotential)
      }
    } catch (error) {
      console.warn(`Could not fetch quote for ${symbol}:`, error)
    }

    // Normaliser le score de confiance
    confidenceScore = Math.min(1.0, Math.max(0.1, confidenceScore))

    return {
      symbol,
      score: Math.round(score),
      reasons,
      confidenceScore,
      targetAllocation,
      targetPrice
    }

  } catch (error) {
    console.error(`Error calculating recommendation score for ${symbol}:`, error)
    return null
  }
}

/**
 * Calcule le bonus de secteur selon le profil de risque
 */
const getSectorBonus = (sector: string | null, riskTolerance: string) => {
  if (!sector) return { score: 0, reason: null }

  const sectorScores: Record<string, Record<string, { score: number; reason: string }>> = {
    conservative: {
      'Consumer Staples': { score: 8, reason: 'Secteur défensif stable' },
      'Utilities': { score: 7, reason: 'Services publics stables' },
      'Healthcare': { score: 6, reason: 'Secteur de la santé résilient' },
      'Telecommunications': { score: 5, reason: 'Télécommunications stables' }
    },
    moderate: {
      'Technology': { score: 8, reason: 'Secteur technologique en croissance' },
      'Healthcare': { score: 7, reason: 'Innovation en santé' },
      'Consumer Discretionary': { score: 6, reason: 'Consommation discrétionnaire' },
      'Industrials': { score: 5, reason: 'Secteur industriel diversifié' }
    },
    aggressive: {
      'Technology': { score: 10, reason: 'Forte croissance technologique' },
      'Consumer Discretionary': { score: 8, reason: 'Potentiel de croissance élevé' },
      'Communication Services': { score: 7, reason: 'Services de communication innovants' },
      'Real Estate': { score: 6, reason: 'Immobilier à potentiel' }
    }
  }

  return sectorScores[riskTolerance]?.[sector] || { score: 0, reason: null }
}

/**
 * Calcule l'allocation cible pour une action
 */
const calculateTargetAllocation = (
  score: number,
  riskTolerance: string,
  stock: any
): number => {
  // Allocation de base selon le score (1% à 10%)
  let baseAllocation = Math.min(10, Math.max(1, score / 10))

  // Ajustement selon le profil de risque
  const riskAdjustment = {
    conservative: 0.7,
    moderate: 1.0,
    aggressive: 1.3
  }[riskTolerance]

  baseAllocation *= riskAdjustment

  // Bonus pour Sharia King
  if (stock.sharia_king_since) {
    baseAllocation *= 1.2
  }

  // Limitation selon la capitalisation
  if (stock.market_cap && stock.market_cap < 1000000000) { // < 1B
    baseAllocation = Math.min(baseAllocation, 3) // Max 3% pour petites caps
  }

  return Math.round(baseAllocation * 100) / 100
}

/**
 * Génère des recommandations personnalisées pour un utilisateur
 */
export const generatePersonalizedRecommendations = async (
  userId: string,
  criteria: RecommendationCriteria
): Promise<RecommendationScore[]> => {
  try {
    // Récupérer les actions déjà en portefeuille
    const { data: existingPositions } = await supabase
      .from('user_portfolio_positions')
      .select('symbol, user_portfolios!inner(user_id)')
      .eq('user_portfolios.user_id', userId)

    const excludeSymbols = [
      ...(criteria.excludeSymbols || []),
      ...(existingPositions?.map(p => p.symbol) || [])
    ]

    // Récupérer les actions candidates
    let query = supabase
      .from('stocks_with_realtime_data')
      .select('symbol, sector, market_cap, is_sharia_compliant, sharia_king_since')
      .eq('is_sharia_compliant', true)
      .not('symbol', 'in', `(${excludeSymbols.join(',')})`)

    // Filtrer par secteurs si spécifié
    if (criteria.sectors && criteria.sectors.length > 0) {
      query = query.in('sector', criteria.sectors)
    }

    const { data: candidates, error } = await query
      .order('market_cap', { ascending: false, nullsLast: true })
      .limit(50) // Limiter pour les performances

    if (error || !candidates) {
      console.error('Error fetching candidate stocks:', error)
      return []
    }

    // Calculer les scores pour chaque candidat
    const recommendations: RecommendationScore[] = []
    
    for (const candidate of candidates) {
      const score = await calculateRecommendationScore(candidate.symbol, criteria)
      if (score && score.score > 30) { // Seuil minimum
        recommendations.push(score)
      }
    }

    // Trier par score et limiter le nombre
    const maxRecommendations = criteria.maxRecommendations || 10
    return recommendations
      .sort((a, b) => b.score - a.score)
      .slice(0, maxRecommendations)

  } catch (error) {
    console.error('Error generating personalized recommendations:', error)
    return []
  }
}

/**
 * Sauvegarde les recommandations en base de données
 */
export const saveRecommendations = async (
  userId: string,
  recommendations: RecommendationScore[]
): Promise<number> => {
  try {
    const recommendationsData = recommendations.map(rec => ({
      user_id: userId,
      symbol: rec.symbol,
      recommendation_type: 'buy' as const,
      reason: rec.reasons.join('. '),
      confidence_score: rec.confidenceScore,
      target_price: rec.targetPrice,
      target_allocation_percent: rec.targetAllocation,
      is_sharia_compliant: true,
      expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 jours
    }))

    const { data, error } = await supabase
      .from('user_recommendations')
      .upsert(recommendationsData, {
        onConflict: 'user_id,symbol,recommendation_type,created_at::date',
        ignoreDuplicates: true
      })

    if (error) {
      console.error('Error saving recommendations:', error)
      return 0
    }

    return data?.length || 0

  } catch (error) {
    console.error('Error in saveRecommendations:', error)
    return 0
  }
}

/**
 * Génère et sauvegarde des recommandations pour un utilisateur
 */
export const generateAndSaveRecommendations = async (
  userId: string,
  criteria?: Partial<RecommendationCriteria>
): Promise<number> => {
  try {
    // Récupérer le profil utilisateur pour les critères par défaut
    const { data: profile } = await supabase
      .from('profiles')
      .select('risk_tolerance')
      .eq('id', userId)
      .single()

    const fullCriteria: RecommendationCriteria = {
      riskTolerance: profile?.risk_tolerance || 'moderate',
      maxRecommendations: 10,
      ...criteria
    }

    // Générer les recommandations
    const recommendations = await generatePersonalizedRecommendations(userId, fullCriteria)

    // Sauvegarder en base
    const savedCount = await saveRecommendations(userId, recommendations)

    console.log(`Generated and saved ${savedCount} recommendations for user ${userId}`)
    return savedCount

  } catch (error) {
    console.error('Error in generateAndSaveRecommendations:', error)
    return 0
  }
}
