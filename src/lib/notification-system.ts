import { supabase } from './supabase'
import { getQuote } from './yahoo-finance'

/**
 * Système de notifications et alertes pour les utilisateurs
 * Gère les alertes de prix, changements de conformité Sharia, et nouvelles recommandations
 */

export interface NotificationConfig {
  userId: string
  enablePriceAlerts: boolean
  enableShariaAlerts: boolean
  enableRecommendationAlerts: boolean
  emailNotifications: boolean
  pushNotifications: boolean
}

export interface PriceAlert {
  id: string
  userId: string
  symbol: string
  alertType: 'price_above' | 'price_below'
  targetPrice: number
  currentPrice: number
  isTriggered: boolean
  message: string
}

/**
 * Vérifie les alertes de prix pour un utilisateur
 */
export const checkPriceAlerts = async (userId: string): Promise<PriceAlert[]> => {
  try {
    // Récupérer les alertes actives de l'utilisateur
    const { data: alerts, error } = await supabase
      .from('user_alerts')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true)
      .eq('is_triggered', false)
      .in('alert_type', ['price_above', 'price_below'])

    if (error || !alerts) {
      console.error('Error fetching price alerts:', error)
      return []
    }

    const triggeredAlerts: PriceAlert[] = []

    // Vérifier chaque alerte
    for (const alert of alerts) {
      try {
        const quote = await getQuote(alert.symbol)
        const currentPrice = quote.regularMarketPrice
        let isTriggered = false
        let message = ''

        // Vérifier les conditions d'alerte
        if (alert.alert_type === 'price_above' && currentPrice >= alert.target_value) {
          isTriggered = true
          message = `${alert.symbol} a atteint $${currentPrice.toFixed(2)} (cible: $${alert.target_value})`
        } else if (alert.alert_type === 'price_below' && currentPrice <= alert.target_value) {
          isTriggered = true
          message = `${alert.symbol} est descendu à $${currentPrice.toFixed(2)} (cible: $${alert.target_value})`
        }

        if (isTriggered) {
          // Marquer l'alerte comme déclenchée
          await supabase
            .from('user_alerts')
            .update({
              is_triggered: true,
              triggered_at: new Date().toISOString(),
              message
            })
            .eq('id', alert.id)

          triggeredAlerts.push({
            id: alert.id,
            userId: alert.user_id,
            symbol: alert.symbol,
            alertType: alert.alert_type,
            targetPrice: alert.target_value,
            currentPrice,
            isTriggered: true,
            message
          })
        }

      } catch (error) {
        console.error(`Error checking alert for ${alert.symbol}:`, error)
      }
    }

    return triggeredAlerts

  } catch (error) {
    console.error('Error in checkPriceAlerts:', error)
    return []
  }
}

/**
 * Vérifie les changements de conformité Sharia
 */
export const checkShariaComplianceChanges = async (userId: string): Promise<any[]> => {
  try {
    // Récupérer les actions en watchlist et portefeuille de l'utilisateur
    const [watchlistResponse, portfolioResponse] = await Promise.all([
      supabase
        .from('user_watchlists')
        .select('symbol')
        .eq('user_id', userId),
      supabase
        .from('user_portfolio_positions')
        .select('symbol, user_portfolios!inner(user_id)')
        .eq('user_portfolios.user_id', userId)
    ])

    const watchlistSymbols = watchlistResponse.data?.map(w => w.symbol) || []
    const portfolioSymbols = portfolioResponse.data?.map(p => p.symbol) || []
    const allSymbols = [...new Set([...watchlistSymbols, ...portfolioSymbols])]

    if (allSymbols.length === 0) {
      return []
    }

    // Vérifier les changements de conformité récents (dernières 24h)
    const yesterday = new Date()
    yesterday.setDate(yesterday.getDate() - 1)

    const { data: recentChanges, error } = await supabase
      .from('stocks')
      .select('symbol, name, is_sharia_compliant, updated_at')
      .in('symbol', allSymbols)
      .gte('updated_at', yesterday.toISOString())

    if (error || !recentChanges) {
      return []
    }

    const notifications = []

    for (const stock of recentChanges) {
      // Créer une notification pour changement de conformité
      const notification = {
        userId,
        symbol: stock.symbol,
        type: 'sharia_change',
        title: `Changement de conformité Sharia`,
        message: `${stock.name} (${stock.symbol}) est ${stock.is_sharia_compliant ? 'maintenant conforme' : 'plus conforme'} aux critères Sharia`,
        isCompliant: stock.is_sharia_compliant,
        timestamp: new Date().toISOString()
      }

      notifications.push(notification)

      // Sauvegarder la notification
      await supabase
        .from('user_alerts')
        .insert({
          user_id: userId,
          symbol: stock.symbol,
          alert_type: 'sharia_change',
          is_active: true,
          is_triggered: true,
          triggered_at: new Date().toISOString(),
          message: notification.message
        })
    }

    return notifications

  } catch (error) {
    console.error('Error checking Sharia compliance changes:', error)
    return []
  }
}

/**
 * Envoie une notification à un utilisateur
 */
export const sendNotification = async (
  userId: string,
  notification: {
    type: 'price_alert' | 'sharia_change' | 'recommendation' | 'portfolio_update'
    title: string
    message: string
    data?: any
  }
): Promise<boolean> => {
  try {
    // Pour l'instant, on sauvegarde juste en base
    // Plus tard, on pourra ajouter email, push notifications, etc.
    
    const { error } = await supabase
      .from('user_notifications')
      .insert({
        user_id: userId,
        type: notification.type,
        title: notification.title,
        message: notification.message,
        data: notification.data,
        is_read: false,
        created_at: new Date().toISOString()
      })

    if (error) {
      console.error('Error saving notification:', error)
      return false
    }

    return true

  } catch (error) {
    console.error('Error sending notification:', error)
    return false
  }
}

/**
 * Récupère les notifications d'un utilisateur
 */
export const getUserNotifications = async (
  userId: string,
  options: {
    unreadOnly?: boolean
    limit?: number
    type?: string
  } = {}
): Promise<any[]> => {
  try {
    let query = supabase
      .from('user_notifications')
      .select('*')
      .eq('user_id', userId)

    if (options.unreadOnly) {
      query = query.eq('is_read', false)
    }

    if (options.type) {
      query = query.eq('type', options.type)
    }

    const { data: notifications, error } = await query
      .order('created_at', { ascending: false })
      .limit(options.limit || 50)

    if (error) {
      console.error('Error fetching notifications:', error)
      return []
    }

    return notifications || []

  } catch (error) {
    console.error('Error in getUserNotifications:', error)
    return []
  }
}

/**
 * Marque une notification comme lue
 */
export const markNotificationAsRead = async (notificationId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('user_notifications')
      .update({ is_read: true, read_at: new Date().toISOString() })
      .eq('id', notificationId)

    return !error

  } catch (error) {
    console.error('Error marking notification as read:', error)
    return false
  }
}

/**
 * Marque toutes les notifications comme lues
 */
export const markAllNotificationsAsRead = async (userId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('user_notifications')
      .update({ is_read: true, read_at: new Date().toISOString() })
      .eq('user_id', userId)
      .eq('is_read', false)

    return !error

  } catch (error) {
    console.error('Error marking all notifications as read:', error)
    return false
  }
}

/**
 * Crée une alerte de prix
 */
export const createPriceAlert = async (
  userId: string,
  symbol: string,
  alertType: 'price_above' | 'price_below',
  targetPrice: number
): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('user_alerts')
      .insert({
        user_id: userId,
        symbol: symbol.toUpperCase(),
        alert_type: alertType,
        target_value: targetPrice,
        is_active: true,
        is_triggered: false
      })

    return !error

  } catch (error) {
    console.error('Error creating price alert:', error)
    return false
  }
}

/**
 * Supprime une alerte
 */
export const deleteAlert = async (alertId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('user_alerts')
      .delete()
      .eq('id', alertId)

    return !error

  } catch (error) {
    console.error('Error deleting alert:', error)
    return false
  }
}

/**
 * Traitement en lot des alertes pour tous les utilisateurs
 * À exécuter périodiquement (ex: toutes les 5 minutes)
 */
export const processBatchAlerts = async (): Promise<{
  priceAlerts: number
  shariaAlerts: number
  errors: number
}> => {
  const results = {
    priceAlerts: 0,
    shariaAlerts: 0,
    errors: 0
  }

  try {
    // Récupérer tous les utilisateurs avec des alertes actives
    const { data: users, error } = await supabase
      .from('user_alerts')
      .select('user_id')
      .eq('is_active', true)
      .eq('is_triggered', false)

    if (error || !users) {
      console.error('Error fetching users with alerts:', error)
      return results
    }

    const uniqueUserIds = [...new Set(users.map(u => u.user_id))]

    // Traiter chaque utilisateur
    for (const userId of uniqueUserIds) {
      try {
        // Vérifier les alertes de prix
        const priceAlerts = await checkPriceAlerts(userId)
        results.priceAlerts += priceAlerts.length

        // Vérifier les changements Sharia
        const shariaAlerts = await checkShariaComplianceChanges(userId)
        results.shariaAlerts += shariaAlerts.length

        // Envoyer les notifications
        for (const alert of priceAlerts) {
          await sendNotification(userId, {
            type: 'price_alert',
            title: 'Alerte de prix',
            message: alert.message,
            data: { symbol: alert.symbol, price: alert.currentPrice }
          })
        }

        for (const alert of shariaAlerts) {
          await sendNotification(userId, {
            type: 'sharia_change',
            title: alert.title,
            message: alert.message,
            data: { symbol: alert.symbol, isCompliant: alert.isCompliant }
          })
        }

      } catch (error) {
        console.error(`Error processing alerts for user ${userId}:`, error)
        results.errors++
      }
    }

    console.log(`Batch alerts processed: ${results.priceAlerts} price alerts, ${results.shariaAlerts} sharia alerts, ${results.errors} errors`)

  } catch (error) {
    console.error('Error in processBatchAlerts:', error)
    results.errors++
  }

  return results
}
