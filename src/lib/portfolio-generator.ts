import { UserProfile, Stock, PortfolioAllocation, ShariaScreeningCriteria, EXCLUDED_SECTORS } from '@/types'

/**
 * Générateur de portefeuille ETF maison halal
 * Crée un portefeuille personnalisé basé sur le profil utilisateur et les critères Sharia
 */

// Critères AAOIFI par défaut
export const DEFAULT_SHARIA_CRITERIA: ShariaScreeningCriteria = {
  maxDebtRatio: 0.33, // 33%
  maxNonHalalRevenue: 0.05, // 5%
  maxLiquidityRatio: 0.33, // 33%
  excludedSectors: [...EXCLUDED_SECTORS]
}

/**
 * Filtre les actions selon les critères Sharia
 */
export function filterShariaCompliantStocks(
  stocks: Stock[], 
  criteria: ShariaScreeningCriteria = DEFAULT_SHARIA_CRITERIA,
  purityLevel: number = 100
): Stock[] {
  return stocks.filter(stock => {
    // Vérifier les secteurs exclus
    if (criteria.excludedSectors.includes(stock.sector)) {
      return false
    }

    // Appliquer les ratios selon le niveau de pureté
    const adjustedCriteria = adjustCriteriaForPurity(criteria, purityLevel)

    // Vérifier les ratios financiers
    if (stock.debtRatio > adjustedCriteria.maxDebtRatio) {
      return false
    }

    if (stock.nonHalalRevenueRatio > adjustedCriteria.maxNonHalalRevenue) {
      return false
    }

    if (stock.liquidityRatio > adjustedCriteria.maxLiquidityRatio) {
      return false
    }

    return true
  })
}

/**
 * Ajuste les critères selon le niveau de pureté (95-100%)
 */
function adjustCriteriaForPurity(
  criteria: ShariaScreeningCriteria, 
  purityLevel: number
): ShariaScreeningCriteria {
  if (purityLevel === 100) {
    return criteria
  }

  // Pour 95%, on assouplit légèrement les critères
  const factor = purityLevel / 100
  return {
    ...criteria,
    maxDebtRatio: criteria.maxDebtRatio * (1 + (1 - factor) * 0.2), // +20% max
    maxNonHalalRevenue: criteria.maxNonHalalRevenue * (1 + (1 - factor) * 0.4), // +40% max
    maxLiquidityRatio: criteria.maxLiquidityRatio * (1 + (1 - factor) * 0.2) // +20% max
  }
}

/**
 * Génère un portefeuille personnalisé basé sur le profil utilisateur
 */
export function generatePersonalizedPortfolio(
  profile: UserProfile,
  availableStocks: Stock[]
): PortfolioAllocation[] {
  // 1. Filtrer les actions halal selon le niveau de pureté
  const shariaCompliantStocks = filterShariaCompliantStocks(
    availableStocks,
    DEFAULT_SHARIA_CRITERIA,
    profile.shariaPurityLevel
  )

  // 2. Exclure les entreprises boycottées
  const filteredStocks = excludeBoycottedCompanies(shariaCompliantStocks, profile.boycottPreferences)

  // 3. Sélectionner les actions selon le profil de risque
  const selectedStocks = selectStocksByRiskProfile(filteredStocks, profile.riskTolerance)

  // 4. Déduplication pour éviter les doublons
  const uniqueStocks = deduplicateStocks(selectedStocks)

  // 5. Calculer les allocations
  const allocations = calculateAllocations(uniqueStocks, profile)

  // 5. Convertir en montants euros
  return convertToEuroAmounts(allocations, profile.monthlyBudget)
}

/**
 * Exclut les entreprises boycottées par l'utilisateur
 */
function excludeBoycottedCompanies(stocks: Stock[], boycottList: string[]): Stock[] {
  if (boycottList.length === 0) return stocks

  return stocks.filter(stock => {
    const companyName = stock.name.toLowerCase()
    return !boycottList.some(boycott => 
      companyName.includes(boycott.toLowerCase())
    )
  })
}

/**
 * Sélectionne les actions selon le profil de risque
 */
function selectStocksByRiskProfile(
  stocks: Stock[], 
  riskTolerance: 'conservative' | 'moderate' | 'aggressive'
): Stock[] {
  // Trier par stabilité (Sharia Kings en premier)
  const sortedStocks = stocks.sort((a, b) => {
    // Priorité aux Sharia Kings
    if (a.shariaKingSince && !b.shariaKingSince) return -1
    if (!a.shariaKingSince && b.shariaKingSince) return 1
    
    // Puis par ratios (plus faibles = plus stables)
    const aRisk = a.debtRatio + a.nonHalalRevenueRatio + a.liquidityRatio
    const bRisk = b.debtRatio + b.nonHalalRevenueRatio + b.liquidityRatio
    return aRisk - bRisk
  })

  // Sélectionner selon le profil de risque
  const selectionSize = getSelectionSizeByRisk(riskTolerance, sortedStocks.length)
  
  switch (riskTolerance) {
    case 'conservative':
      // Prendre les plus stables (début de liste)
      return sortedStocks.slice(0, selectionSize)
    
    case 'moderate':
      // Mélange équilibré sans doublons
      const conservativeCount = Math.floor(selectionSize * 0.6)
      const moderateCount = selectionSize - conservativeCount
      const startIndex = Math.max(conservativeCount, Math.floor(sortedStocks.length * 0.3))
      return [
        ...sortedStocks.slice(0, conservativeCount),
        ...sortedStocks.slice(startIndex, startIndex + moderateCount)
      ]
    
    case 'aggressive':
      // Plus de diversité, incluant des actions moins stables
      const aggressiveSelection = []
      const step = Math.floor(sortedStocks.length / selectionSize)
      for (let i = 0; i < selectionSize && i * step < sortedStocks.length; i++) {
        aggressiveSelection.push(sortedStocks[i * step])
      }
      return aggressiveSelection
    
    default:
      return sortedStocks.slice(0, selectionSize)
  }
}

/**
 * Déduplication des actions pour éviter les doublons
 */
function deduplicateStocks(stocks: Stock[]): Stock[] {
  const seen = new Set<string>()
  return stocks.filter(stock => {
    if (seen.has(stock.symbol)) {
      return false
    }
    seen.add(stock.symbol)
    return true
  })
}

/**
 * Déduplication des allocations pour éviter les doublons
 */
function deduplicateAllocations(allocations: PortfolioAllocation[]): PortfolioAllocation[] {
  const seen = new Map<string, PortfolioAllocation>()

  allocations.forEach(allocation => {
    if (seen.has(allocation.symbol)) {
      // Si on a déjà cette action, on combine les pourcentages
      const existing = seen.get(allocation.symbol)!
      existing.percentage += allocation.percentage
      existing.amount += allocation.amount
    } else {
      seen.set(allocation.symbol, { ...allocation })
    }
  })

  return Array.from(seen.values())
}

/**
 * Détermine la taille de sélection selon le profil de risque
 */
function getSelectionSizeByRisk(
  riskTolerance: 'conservative' | 'moderate' | 'aggressive',
  totalAvailable: number
): number {
  const maxSelection = Math.min(totalAvailable, 20) // Maximum 20 actions

  switch (riskTolerance) {
    case 'conservative':
      return Math.min(8, maxSelection) // 5-8 actions pour la stabilité
    case 'moderate':
      return Math.min(12, maxSelection) // 8-12 actions pour l'équilibre
    case 'aggressive':
      return Math.min(20, maxSelection) // 12-20 actions pour la diversification
    default:
      return Math.min(10, maxSelection)
  }
}

/**
 * Calcule les allocations en pourcentage
 */
function calculateAllocations(
  stocks: Stock[], 
  profile: UserProfile
): PortfolioAllocation[] {
  if (stocks.length === 0) return []

  const allocations: PortfolioAllocation[] = []
  
  // Stratégie d'allocation selon le profil de risque
  switch (profile.riskTolerance) {
    case 'conservative':
      // Allocation concentrée sur les valeurs les plus sûres
      allocations.push(...allocateConservative(stocks))
      break
    
    case 'moderate':
      // Allocation équilibrée
      allocations.push(...allocateModerate(stocks))
      break
    
    case 'aggressive':
      // Allocation diversifiée
      allocations.push(...allocateAggressive(stocks))
      break
  }

  // Déduplication des allocations et normalisation
  const uniqueAllocations = deduplicateAllocations(allocations)
  return normalizeAllocations(uniqueAllocations)
}

/**
 * Allocation conservatrice (concentrée)
 */
function allocateConservative(stocks: Stock[]): PortfolioAllocation[] {
  const allocations: PortfolioAllocation[] = []
  const weights = [30, 25, 20, 15, 10] // Top 5 avec poids décroissants
  
  stocks.slice(0, 5).forEach((stock, index) => {
    allocations.push({
      symbol: stock.symbol,
      name: stock.name,
      percentage: weights[index] || 5,
      amount: 0, // Sera calculé plus tard
      isShariKing: !!stock.shariaKingSince
    })
  })
  
  return allocations
}

/**
 * Allocation modérée (équilibrée)
 */
function allocateModerate(stocks: Stock[]): PortfolioAllocation[] {
  const allocations: PortfolioAllocation[] = []
  const count = Math.min(stocks.length, 10)
  
  // Répartition plus équilibrée
  stocks.slice(0, count).forEach((stock, index) => {
    const baseWeight = 100 / count
    const adjustment = (count - index) * 2 // Légère préférence pour les premiers
    const percentage = baseWeight + adjustment
    
    allocations.push({
      symbol: stock.symbol,
      name: stock.name,
      percentage,
      amount: 0,
      isShariKing: !!stock.shariaKingSince
    })
  })
  
  return allocations
}

/**
 * Allocation agressive (diversifiée)
 */
function allocateAggressive(stocks: Stock[]): PortfolioAllocation[] {
  const allocations: PortfolioAllocation[] = []
  const count = Math.min(stocks.length, 15)
  
  // Répartition quasi-égale pour maximiser la diversification
  stocks.slice(0, count).forEach((stock) => {
    allocations.push({
      symbol: stock.symbol,
      name: stock.name,
      percentage: 100 / count,
      amount: 0,
      isShariKing: !!stock.shariaKingSince
    })
  })
  
  return allocations
}

/**
 * Normalise les allocations pour que le total fasse 100%
 */
function normalizeAllocations(allocations: PortfolioAllocation[]): PortfolioAllocation[] {
  const total = allocations.reduce((sum, alloc) => sum + alloc.percentage, 0)
  
  if (total === 0) return allocations
  
  return allocations.map(alloc => ({
    ...alloc,
    percentage: (alloc.percentage / total) * 100
  }))
}

/**
 * Convertit les pourcentages en montants euros
 */
function convertToEuroAmounts(
  allocations: PortfolioAllocation[], 
  monthlyBudget: number
): PortfolioAllocation[] {
  return allocations.map(alloc => ({
    ...alloc,
    amount: Math.round((alloc.percentage / 100) * monthlyBudget)
  }))
}

/**
 * Identifie les entreprises "Sharia King" (halal depuis 10+ ans)
 */
export function identifyShariaKings(stocks: Stock[]): Stock[] {
  const tenYearsAgo = new Date()
  tenYearsAgo.setFullYear(tenYearsAgo.getFullYear() - 10)
  
  return stocks.filter(stock => {
    if (!stock.shariaKingSince) return false
    
    const kingSince = new Date(stock.shariaKingSince)
    return kingSince <= tenYearsAgo
  })
}
