import { supabase } from './supabase'
import { getQuote, getFinancialStats, getHistoricalData } from './yahoo-finance'
import { YahooFinanceQuote, YahooFinancialStats, YahooFinanceHistoricalData } from '@/types'

/**
 * Service de synchronisation des données financières avec la base de données
 * Sauvegarde les données Yahoo Finance dans Supabase pour un accès rapide
 */

/**
 * Met à jour les données de prix en temps réel pour une action
 */
export const updateStockPrice = async (symbol: string): Promise<boolean> => {
  try {
    const quote = await getQuote(symbol)
    
    const { error } = await supabase
      .from('stocks')
      .update({
        current_price: quote.regularMarketPrice,
        price_change: quote.regularMarketChange,
        price_change_percent: quote.regularMarketChangePercent,
        volume: quote.regularMarketVolume,
        market_cap: quote.marketCap,
        trailing_pe: quote.trailingPE,
        forward_pe: quote.forwardPE,
        dividend_yield: quote.dividendYield,
        fifty_two_week_low: quote.fiftyTwoWeekLow,
        fifty_two_week_high: quote.fiftyTwoWeekHigh,
        currency: quote.currency,
        exchange_name: quote.exchangeName,
        long_name: quote.longName,
        short_name: quote.shortName,
        industry: quote.industry,
        price_last_updated: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('symbol', symbol)

    if (error) {
      console.error(`Error updating stock price for ${symbol}:`, error)
      return false
    }

    console.log(`Stock price updated for ${symbol}`)
    return true

  } catch (error) {
    console.error(`Failed to update stock price for ${symbol}:`, error)
    return false
  }
}

/**
 * Met à jour les statistiques financières pour une action
 */
export const updateFinancialStats = async (symbol: string): Promise<boolean> => {
  try {
    const stats = await getFinancialStats(symbol)
    
    const { error } = await supabase
      .from('stock_financial_stats')
      .upsert({
        symbol,
        total_debt: stats.totalDebt,
        total_cash: stats.totalCash,
        total_revenue: stats.totalRevenue,
        gross_profits: stats.grossProfits,
        operating_cashflow: stats.operatingCashflow,
        free_cashflow: stats.freeCashflow,
        return_on_equity: stats.returnOnEquity,
        return_on_assets: stats.returnOnAssets,
        debt_to_equity: stats.debtToEquity,
        current_ratio: stats.currentRatio,
        quick_ratio: stats.quickRatio,
        last_updated: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })

    if (error) {
      console.error(`Error updating financial stats for ${symbol}:`, error)
      return false
    }

    console.log(`Financial stats updated for ${symbol}`)
    return true

  } catch (error) {
    console.error(`Failed to update financial stats for ${symbol}:`, error)
    return false
  }
}

/**
 * Sauvegarde l'historique des prix pour une action
 */
export const saveHistoricalData = async (
  symbol: string,
  data: YahooFinanceHistoricalData[]
): Promise<boolean> => {
  try {
    const historicalRecords = data.map(item => ({
      symbol,
      date: item.date.toISOString().split('T')[0],
      open_price: item.open,
      high_price: item.high,
      low_price: item.low,
      close_price: item.close,
      adj_close_price: item.adjClose,
      volume: item.volume
    }))

    const { error } = await supabase
      .from('stock_price_history')
      .upsert(historicalRecords, { 
        onConflict: 'symbol,date',
        ignoreDuplicates: true 
      })

    if (error) {
      console.error(`Error saving historical data for ${symbol}:`, error)
      return false
    }

    console.log(`Historical data saved for ${symbol}: ${data.length} records`)
    return true

  } catch (error) {
    console.error(`Failed to save historical data for ${symbol}:`, error)
    return false
  }
}

/**
 * Synchronise toutes les données pour une action
 */
export const syncStockData = async (symbol: string): Promise<{
  priceUpdated: boolean
  statsUpdated: boolean
  historicalSaved: boolean
}> => {
  const results = {
    priceUpdated: false,
    statsUpdated: false,
    historicalSaved: false
  }

  // Mettre à jour le prix en temps réel
  results.priceUpdated = await updateStockPrice(symbol)

  // Mettre à jour les statistiques financières
  results.statsUpdated = await updateFinancialStats(symbol)

  // Sauvegarder l'historique récent (30 jours)
  try {
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
    
    const historicalData = await getHistoricalData(symbol, thirtyDaysAgo)
    results.historicalSaved = await saveHistoricalData(symbol, historicalData)
  } catch (error) {
    console.error(`Failed to sync historical data for ${symbol}:`, error)
  }

  return results
}

/**
 * Synchronise les données pour plusieurs actions
 */
export const syncMultipleStocks = async (
  symbols: string[],
  options: {
    batchSize?: number
    delayBetweenBatches?: number
  } = {}
): Promise<Record<string, any>> => {
  const { batchSize = 10, delayBetweenBatches = 1000 } = options
  const results: Record<string, any> = {}

  console.log(`Starting sync for ${symbols.length} symbols...`)

  // Traiter par lots pour éviter de surcharger l'API
  for (let i = 0; i < symbols.length; i += batchSize) {
    const batch = symbols.slice(i, i + batchSize)
    
    console.log(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(symbols.length / batchSize)}`)

    const batchPromises = batch.map(async (symbol) => {
      try {
        const result = await syncStockData(symbol)
        results[symbol] = { success: true, ...result }
      } catch (error) {
        console.error(`Failed to sync ${symbol}:`, error)
        results[symbol] = { 
          success: false, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        }
      }
    })

    await Promise.allSettled(batchPromises)

    // Délai entre les lots
    if (i + batchSize < symbols.length) {
      await new Promise(resolve => setTimeout(resolve, delayBetweenBatches))
    }
  }

  console.log('Sync completed')
  return results
}

/**
 * Met à jour les ratios Sharia pour toutes les actions
 */
export const updateShariaCompliance = async (): Promise<number> => {
  try {
    // Récupérer toutes les actions avec leurs statistiques financières
    const { data: stocks, error } = await supabase
      .from('stocks_with_realtime_data')
      .select('symbol, calculated_debt_ratio, calculated_liquidity_ratio, is_sharia_compliant_calculated')

    if (error) {
      console.error('Error fetching stocks for Sharia update:', error)
      return 0
    }

    let updatedCount = 0

    for (const stock of stocks || []) {
      const isCompliant = stock.calculated_debt_ratio <= 33 && 
                         stock.calculated_liquidity_ratio <= 33

      // Mettre à jour le statut de conformité Sharia
      const { error: updateError } = await supabase
        .from('stocks')
        .update({
          debt_ratio: stock.calculated_debt_ratio,
          liquidity_ratio: stock.calculated_liquidity_ratio,
          is_sharia_compliant: isCompliant,
          updated_at: new Date().toISOString()
        })
        .eq('symbol', stock.symbol)

      if (!updateError) {
        updatedCount++
      } else {
        console.error(`Error updating Sharia compliance for ${stock.symbol}:`, updateError)
      }
    }

    console.log(`Updated Sharia compliance for ${updatedCount} stocks`)
    return updatedCount

  } catch (error) {
    console.error('Failed to update Sharia compliance:', error)
    return 0
  }
}

/**
 * Nettoie les données anciennes
 */
export const cleanupOldData = async (daysToKeep: number = 365): Promise<{
  historicalDeleted: number
  cacheDeleted: number
}> => {
  const cutoffDate = new Date()
  cutoffDate.setDate(cutoffDate.getDate() - daysToKeep)

  try {
    // Nettoyer l'historique ancien
    const { count: historicalDeleted } = await supabase
      .from('stock_price_history')
      .delete()
      .lt('date', cutoffDate.toISOString().split('T')[0])

    // Nettoyer le cache expiré
    const { data: cacheResult } = await supabase
      .rpc('cleanup_expired_cache')

    console.log(`Cleanup completed: ${historicalDeleted || 0} historical records, ${cacheResult || 0} cache entries`)

    return {
      historicalDeleted: historicalDeleted || 0,
      cacheDeleted: cacheResult || 0
    }

  } catch (error) {
    console.error('Failed to cleanup old data:', error)
    return { historicalDeleted: 0, cacheDeleted: 0 }
  }
}
