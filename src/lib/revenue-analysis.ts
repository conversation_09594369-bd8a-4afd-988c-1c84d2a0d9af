import { RevenueBreakdown, RevenueSegment, SankeyData, SankeyNode, SankeyLink } from '@/types'

/**
 * Service d'analyse des revenus pour la conformité Sharia
 * Récupère et analyse les sources de revenus des entreprises
 */

// Configuration des APIs externes
const ALPHA_VANTAGE_API_KEY = process.env.NEXT_PUBLIC_ALPHA_VANTAGE_API_KEY
const FMP_API_KEY = process.env.NEXT_PUBLIC_FMP_API_KEY

/**
 * Récupère les données de revenus détaillées d'une entreprise
 */
export async function getRevenueBreakdown(symbol: string): Promise<RevenueBreakdown | null> {
  try {
    // Essayer d'abord Financial Modeling Prep (plus détaillé)
    if (FMP_API_KEY) {
      const fmpData = await fetchFromFMP(symbol)
      if (fmpData) return fmpData
    }

    // Fallback vers Alpha Vantage
    if (ALPHA_VANTAGE_API_KEY) {
      const alphaData = await fetchFromAlphaVantage(symbol)
      if (alphaData) return alphaData
    }

    // Fallback vers données mockées pour les entreprises connues
    return getMockRevenueData(symbol)

  } catch (error) {
    console.error(`Error fetching revenue breakdown for ${symbol}:`, error)
    return getMockRevenueData(symbol)
  }
}

/**
 * Récupère les données depuis Financial Modeling Prep
 */
async function fetchFromFMP(symbol: string): Promise<RevenueBreakdown | null> {
  try {
    const response = await fetch(
      `https://financialmodelingprep.com/api/v3/income-statement/${symbol}?limit=1&apikey=${FMP_API_KEY}`
    )
    
    if (!response.ok) return null
    
    const data = await response.json()
    if (!data || data.length === 0) return null

    const incomeStatement = data[0]
    
    // Récupérer aussi les données de segments
    const segmentResponse = await fetch(
      `https://financialmodelingprep.com/api/v4/revenue-product-segmentation?symbol=${symbol}&period=annual&apikey=${FMP_API_KEY}`
    )
    
    let segments: RevenueSegment[] = []
    
    if (segmentResponse.ok) {
      const segmentData = await segmentResponse.json()
      segments = parseSegmentData(segmentData, symbol)
    }

    return {
      symbol,
      companyName: incomeStatement.companyName || symbol,
      totalRevenue: incomeStatement.revenue || 0,
      fiscalYear: incomeStatement.calendarYear || new Date().getFullYear().toString(),
      segments,
      shariaCompliance: calculateShariaCompliance(segments),
      lastUpdated: new Date().toISOString()
    }

  } catch (error) {
    console.error('Error fetching from FMP:', error)
    return null
  }
}

/**
 * Récupère les données depuis Alpha Vantage
 */
async function fetchFromAlphaVantage(symbol: string): Promise<RevenueBreakdown | null> {
  try {
    const response = await fetch(
      `https://www.alphavantage.co/query?function=INCOME_STATEMENT&symbol=${symbol}&apikey=${ALPHA_VANTAGE_API_KEY}`
    )
    
    if (!response.ok) return null
    
    const data = await response.json()
    if (!data.annualReports || data.annualReports.length === 0) return null

    const latestReport = data.annualReports[0]
    
    // Alpha Vantage ne fournit pas de segments détaillés, on utilise des estimations
    const segments = estimateSegments(symbol, parseInt(latestReport.totalRevenue))

    return {
      symbol,
      companyName: symbol, // Alpha Vantage ne fournit pas le nom complet
      totalRevenue: parseInt(latestReport.totalRevenue) || 0,
      fiscalYear: latestReport.fiscalDateEnding?.split('-')[0] || new Date().getFullYear().toString(),
      segments,
      shariaCompliance: calculateShariaCompliance(segments),
      lastUpdated: new Date().toISOString()
    }

  } catch (error) {
    console.error('Error fetching from Alpha Vantage:', error)
    return null
  }
}

/**
 * Parse les données de segments depuis FMP
 */
function parseSegmentData(segmentData: any[], symbol: string): RevenueSegment[] {
  if (!segmentData || segmentData.length === 0) {
    return estimateSegments(symbol, 0)
  }

  const latestData = segmentData[0]
  const segments: RevenueSegment[] = []

  // Parser les segments de revenus
  Object.entries(latestData).forEach(([key, value]) => {
    if (key !== 'date' && key !== 'symbol' && typeof value === 'number' && value > 0) {
      const segment = createRevenueSegment(key, value as number, symbol)
      if (segment) segments.push(segment)
    }
  })

  return segments
}

/**
 * Crée un segment de revenus avec classification Sharia
 */
function createRevenueSegment(name: string, amount: number, symbol: string): RevenueSegment | null {
  const normalizedName = name.toLowerCase()
  
  let isHalal = true
  let category: 'halal' | 'questionable' | 'haram' = 'halal'
  let description = ''

  // Classification basée sur le nom du segment et l'entreprise
  if (normalizedName.includes('advertising') || normalizedName.includes('ads')) {
    isHalal = false
    category = 'haram'
    description = 'Revenus publicitaires pouvant inclure du contenu illicite'
  } else if (normalizedName.includes('interest') || normalizedName.includes('financial')) {
    isHalal = false
    category = 'haram'
    description = 'Revenus d\'intérêts (riba)'
  } else if (normalizedName.includes('gaming') || normalizedName.includes('gambling')) {
    isHalal = false
    category = 'haram'
    description = 'Revenus liés aux jeux d\'argent'
  } else if (normalizedName.includes('alcohol') || normalizedName.includes('tobacco')) {
    isHalal = false
    category = 'haram'
    description = 'Revenus de produits interdits'
  } else if (normalizedName.includes('entertainment') || normalizedName.includes('media')) {
    category = 'questionable'
    description = 'Revenus de divertissement (à examiner)'
  }

  return {
    name: formatSegmentName(name),
    amount,
    percentage: 0, // Sera calculé plus tard
    isHalal,
    category,
    description
  }
}

/**
 * Estime les segments pour les entreprises connues
 */
function estimateSegments(symbol: string, totalRevenue: number): RevenueSegment[] {
  const estimates = getKnownCompanyEstimates(symbol)
  
  return estimates.map(estimate => ({
    ...estimate,
    amount: (totalRevenue * estimate.percentage) / 100
  }))
}

/**
 * Données estimées pour les entreprises connues
 */
function getKnownCompanyEstimates(symbol: string): Omit<RevenueSegment, 'amount'>[] {
  const estimates: Record<string, Omit<RevenueSegment, 'amount'>[]> = {
    'GOOGL': [
      {
        name: 'Google Search',
        percentage: 57,
        isHalal: false,
        category: 'haram',
        description: 'Revenus publicitaires incluant contenu illicite'
      },
      {
        name: 'YouTube Ads',
        percentage: 11,
        isHalal: false,
        category: 'haram',
        description: 'Publicités YouTube avec contenu inapproprié'
      },
      {
        name: 'Google Network',
        percentage: 12,
        isHalal: false,
        category: 'haram',
        description: 'Réseau publicitaire avec contenu illicite'
      },
      {
        name: 'Google Cloud',
        percentage: 8,
        isHalal: true,
        category: 'halal',
        description: 'Services cloud légitimes'
      },
      {
        name: 'Other Bets',
        percentage: 1,
        isHalal: true,
        category: 'halal',
        description: 'Autres investissements technologiques'
      },
      {
        name: 'Google Other',
        percentage: 11,
        isHalal: true,
        category: 'halal',
        description: 'Autres services Google (Play Store, Hardware)'
      }
    ],
    'META': [
      {
        name: 'Facebook Advertising',
        percentage: 97,
        isHalal: false,
        category: 'haram',
        description: 'Publicités Facebook avec contenu illicite'
      },
      {
        name: 'Reality Labs',
        percentage: 3,
        isHalal: true,
        category: 'halal',
        description: 'Réalité virtuelle et métaverse'
      }
    ],
    'AAPL': [
      {
        name: 'iPhone',
        percentage: 52,
        isHalal: true,
        category: 'halal',
        description: 'Ventes d\'iPhone'
      },
      {
        name: 'Services',
        percentage: 22,
        isHalal: true,
        category: 'halal',
        description: 'App Store, iCloud, Apple Pay'
      },
      {
        name: 'Mac',
        percentage: 10,
        isHalal: true,
        category: 'halal',
        description: 'Ordinateurs Mac'
      },
      {
        name: 'iPad',
        percentage: 8,
        isHalal: true,
        category: 'halal',
        description: 'Tablettes iPad'
      },
      {
        name: 'Wearables',
        percentage: 8,
        isHalal: true,
        category: 'halal',
        description: 'Apple Watch et accessoires'
      }
    ]
  }

  return estimates[symbol] || [
    {
      name: 'Core Business',
      percentage: 100,
      isHalal: true,
      category: 'halal',
      description: 'Activité principale'
    }
  ]
}

/**
 * Calcule la conformité Sharia basée sur les segments
 */
function calculateShariaCompliance(segments: RevenueSegment[]): RevenueBreakdown['shariaCompliance'] {
  const totalRevenue = segments.reduce((sum, segment) => sum + segment.amount, 0)
  
  if (totalRevenue === 0) {
    return {
      halalPercentage: 0,
      questionablePercentage: 0,
      haramPercentage: 0,
      overallRating: 'non-compliant'
    }
  }

  // Calculer les pourcentages pour chaque segment
  segments.forEach(segment => {
    segment.percentage = (segment.amount / totalRevenue) * 100
  })

  const halalAmount = segments.filter(s => s.category === 'halal').reduce((sum, s) => sum + s.amount, 0)
  const questionableAmount = segments.filter(s => s.category === 'questionable').reduce((sum, s) => sum + s.amount, 0)
  const haramAmount = segments.filter(s => s.category === 'haram').reduce((sum, s) => sum + s.amount, 0)

  const halalPercentage = (halalAmount / totalRevenue) * 100
  const questionablePercentage = (questionableAmount / totalRevenue) * 100
  const haramPercentage = (haramAmount / totalRevenue) * 100

  let overallRating: 'compliant' | 'questionable' | 'non-compliant'
  
  if (haramPercentage > 5) {
    overallRating = 'non-compliant'
  } else if (haramPercentage > 0 || questionablePercentage > 10) {
    overallRating = 'questionable'
  } else {
    overallRating = 'compliant'
  }

  return {
    halalPercentage,
    questionablePercentage,
    haramPercentage,
    overallRating
  }
}

/**
 * Données mockées pour les tests
 */
function getMockRevenueData(symbol: string): RevenueBreakdown | null {
  const mockData = getKnownCompanyEstimates(symbol)
  
  if (mockData.length === 0) return null

  const totalRevenue = 100000000000 // 100B par défaut
  const segments = mockData.map(estimate => ({
    ...estimate,
    amount: (totalRevenue * estimate.percentage) / 100
  }))

  return {
    symbol,
    companyName: `${symbol} Corporation`,
    totalRevenue,
    fiscalYear: new Date().getFullYear().toString(),
    segments,
    shariaCompliance: calculateShariaCompliance(segments),
    lastUpdated: new Date().toISOString()
  }
}

/**
 * Formate le nom d'un segment
 */
function formatSegmentName(name: string): string {
  return name
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, str => str.toUpperCase())
    .trim()
}

/**
 * Génère les données pour un diagramme de Sankey
 */
export function generateSankeyData(revenueBreakdown: RevenueBreakdown): SankeyData {
  const nodes: SankeyNode[] = []
  const links: SankeyLink[] = []

  // Nœud de l'entreprise
  nodes.push({
    id: 'company',
    name: revenueBreakdown.companyName,
    category: 'company'
  })

  // Nœuds de classification
  nodes.push(
    { id: 'halal', name: 'Halal', category: 'classification', color: '#22c55e' },
    { id: 'questionable', name: 'Questionable', category: 'classification', color: '#f59e0b' },
    { id: 'haram', name: 'Haram', category: 'classification', color: '#ef4444' }
  )

  // Nœuds et liens pour chaque segment
  revenueBreakdown.segments.forEach((segment, index) => {
    const segmentId = `segment-${index}`
    
    // Nœud du segment
    nodes.push({
      id: segmentId,
      name: segment.name,
      category: 'source'
    })

    // Lien du segment vers l'entreprise
    links.push({
      source: segmentId,
      target: 'company',
      value: segment.amount
    })

    // Lien de l'entreprise vers la classification
    links.push({
      source: 'company',
      target: segment.category,
      value: segment.amount,
      color: segment.category === 'halal' ? '#22c55e' : 
             segment.category === 'questionable' ? '#f59e0b' : '#ef4444'
    })
  })

  return { nodes, links }
}
