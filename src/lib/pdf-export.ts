import jsPDF from 'jspdf'
import 'jspdf-autotable'

// Étendre le type jsPDF pour inclure autoTable
declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF
  }
}

interface PortfolioData {
  totalValue: number
  monthlyInvestment: number
  totalInvested: number
  totalGain: number
  gainPercentage: number
  lastUpdate: string
  allocations: Array<{
    symbol: string
    name: string
    amount: number
    percentage: number
    currentPrice: number
    shares: number
    gain: number
    gainPercentage: number
    isShariKing: boolean
    sector: string
  }>
}

/**
 * Exporte le portefeuille en PDF
 */
export function exportPortfolioToPDF(portfolioData: PortfolioData, showAmounts: boolean = true) {
  const doc = new jsPDF()
  
  // Configuration
  const pageWidth = doc.internal.pageSize.width
  const margin = 20
  let yPosition = margin

  // Header avec logo et titre
  doc.setFontSize(24)
  doc.setFont('helvetica', 'bold')
  doc.text('🕌 Halal Invest', margin, yPosition)
  
  yPosition += 10
  doc.setFontSize(18)
  doc.text('Mon Portefeuille Halal', margin, yPosition)
  
  yPosition += 8
  doc.setFontSize(10)
  doc.setFont('helvetica', 'normal')
  doc.text(`Généré le ${new Date().toLocaleString('fr-FR')}`, margin, yPosition)
  doc.text(`Dernière mise à jour: ${new Date(portfolioData.lastUpdate).toLocaleString('fr-FR')}`, margin, yPosition + 4)

  yPosition += 20

  // Métriques principales
  doc.setFontSize(14)
  doc.setFont('helvetica', 'bold')
  doc.text('Résumé du Portefeuille', margin, yPosition)
  yPosition += 10

  const metrics = [
    ['Valeur totale', showAmounts ? `${portfolioData.totalValue.toLocaleString('fr-FR')} €` : 'Masqué'],
    ['Investissement total', showAmounts ? `${portfolioData.totalInvested.toLocaleString('fr-FR')} €` : 'Masqué'],
    ['Gain total', showAmounts ? `${portfolioData.totalGain.toLocaleString('fr-FR')} € (+${portfolioData.gainPercentage}%)` : 'Masqué'],
    ['Investissement mensuel', showAmounts ? `${portfolioData.monthlyInvestment.toLocaleString('fr-FR')} €` : 'Masqué'],
    ['Nombre de positions', portfolioData.allocations.length.toString()],
    ['Sharia Kings', portfolioData.allocations.filter(a => a.isShariKing).length.toString()]
  ]

  doc.autoTable({
    startY: yPosition,
    head: [['Métrique', 'Valeur']],
    body: metrics,
    theme: 'grid',
    headStyles: { fillColor: [41, 128, 185] },
    margin: { left: margin, right: margin },
    styles: { fontSize: 10 }
  })

  yPosition = (doc as any).lastAutoTable.finalY + 20

  // Vérifier si on a besoin d'une nouvelle page
  if (yPosition > 200) {
    doc.addPage()
    yPosition = margin
  }

  // Détail des positions
  doc.setFontSize(14)
  doc.setFont('helvetica', 'bold')
  doc.text('Détail des Positions', margin, yPosition)
  yPosition += 10

  const positionsData = portfolioData.allocations.map((allocation, index) => [
    (index + 1).toString(),
    allocation.symbol,
    allocation.name.length > 25 ? allocation.name.substring(0, 25) + '...' : allocation.name,
    allocation.sector,
    allocation.isShariKing ? '👑' : '',
    showAmounts ? `${allocation.amount.toLocaleString('fr-FR')} €` : 'Masqué',
    `${allocation.percentage.toFixed(1)}%`,
    allocation.shares.toFixed(2),
    showAmounts ? `${allocation.gain >= 0 ? '+' : ''}${allocation.gain} €` : 'Masqué',
    `${allocation.gainPercentage >= 0 ? '+' : ''}${allocation.gainPercentage}%`
  ])

  doc.autoTable({
    startY: yPosition,
    head: [['#', 'Symbole', 'Nom', 'Secteur', 'King', 'Montant', '%', 'Actions', 'Gain €', 'Gain %']],
    body: positionsData,
    theme: 'striped',
    headStyles: { fillColor: [41, 128, 185] },
    margin: { left: margin, right: margin },
    styles: { fontSize: 8 },
    columnStyles: {
      0: { cellWidth: 10 },
      1: { cellWidth: 20 },
      2: { cellWidth: 35 },
      3: { cellWidth: 25 },
      4: { cellWidth: 12 },
      5: { cellWidth: 25 },
      6: { cellWidth: 15 },
      7: { cellWidth: 18 },
      8: { cellWidth: 20 },
      9: { cellWidth: 15 }
    }
  })

  yPosition = (doc as any).lastAutoTable.finalY + 20

  // Vérifier si on a besoin d'une nouvelle page pour les secteurs
  if (yPosition > 220) {
    doc.addPage()
    yPosition = margin
  }

  // Répartition par secteur
  doc.setFontSize(14)
  doc.setFont('helvetica', 'bold')
  doc.text('Répartition par Secteur', margin, yPosition)
  yPosition += 10

  // Calculer la répartition par secteur
  const sectorAllocation = portfolioData.allocations.reduce((acc, allocation) => {
    const sector = allocation.sector
    if (!acc[sector]) {
      acc[sector] = { amount: 0, percentage: 0, count: 0 }
    }
    acc[sector].amount += allocation.amount
    acc[sector].percentage += allocation.percentage
    acc[sector].count += 1
    return acc
  }, {} as Record<string, { amount: number; percentage: number; count: number }>)

  const sectorsData = Object.entries(sectorAllocation)
    .sort(([,a], [,b]) => b.percentage - a.percentage)
    .map(([sector, data]) => [
      sector,
      showAmounts ? `${data.amount.toLocaleString('fr-FR')} €` : 'Masqué',
      `${data.percentage.toFixed(1)}%`,
      data.count.toString()
    ])

  doc.autoTable({
    startY: yPosition,
    head: [['Secteur', 'Montant', 'Pourcentage', 'Positions']],
    body: sectorsData,
    theme: 'grid',
    headStyles: { fillColor: [41, 128, 185] },
    margin: { left: margin, right: margin },
    styles: { fontSize: 10 }
  })

  yPosition = (doc as any).lastAutoTable.finalY + 20

  // Footer avec conformité Sharia
  if (yPosition > 250) {
    doc.addPage()
    yPosition = margin
  }

  doc.setFontSize(12)
  doc.setFont('helvetica', 'bold')
  doc.text('Conformité Sharia', margin, yPosition)
  yPosition += 8

  doc.setFontSize(10)
  doc.setFont('helvetica', 'normal')
  doc.text('✓ Toutes les positions respectent les critères AAOIFI', margin, yPosition)
  yPosition += 5
  doc.text('✓ Aucun secteur interdit (banques conventionnelles, alcool, etc.)', margin, yPosition)
  yPosition += 5
  doc.text('✓ Ratios financiers conformes (dette <33%, revenus non-halal <5%)', margin, yPosition)
  yPosition += 5
  doc.text(`✓ ${portfolioData.allocations.filter(a => a.isShariKing).length} Sharia Kings (halal depuis 10+ ans)`, margin, yPosition)

  // Footer de page
  const pageCount = doc.getNumberOfPages()
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i)
    doc.setFontSize(8)
    doc.setFont('helvetica', 'normal')
    doc.text(
      `Halal Invest - Page ${i}/${pageCount} - Confidentiel`,
      pageWidth / 2,
      doc.internal.pageSize.height - 10,
      { align: 'center' }
    )
  }

  // Télécharger le PDF
  const fileName = `portefeuille-halal-${new Date().toISOString().split('T')[0]}.pdf`
  doc.save(fileName)
}

/**
 * Exporte le plan d'investissement en PDF
 */
export function exportInvestmentPlanToPDF(portfolioData: PortfolioData, showAmounts: boolean = true) {
  const doc = new jsPDF()
  
  const pageWidth = doc.internal.pageSize.width
  const margin = 20
  let yPosition = margin

  // Header
  doc.setFontSize(24)
  doc.setFont('helvetica', 'bold')
  doc.text('🕌 Halal Invest', margin, yPosition)
  
  yPosition += 10
  doc.setFontSize(18)
  doc.text('Plan d\'Investissement Mensuel', margin, yPosition)
  
  yPosition += 8
  doc.setFontSize(10)
  doc.setFont('helvetica', 'normal')
  doc.text(`Généré le ${new Date().toLocaleString('fr-FR')}`, margin, yPosition)

  yPosition += 20

  // Instructions
  doc.setFontSize(12)
  doc.setFont('helvetica', 'bold')
  doc.text('Instructions d\'Investissement', margin, yPosition)
  yPosition += 10

  doc.setFontSize(10)
  doc.setFont('helvetica', 'normal')
  doc.text(`Budget mensuel total: ${showAmounts ? portfolioData.monthlyInvestment.toLocaleString('fr-FR') + ' €' : 'Masqué'}`, margin, yPosition)
  yPosition += 8

  // Plan d'achat détaillé
  const investmentPlan = portfolioData.allocations.map((allocation, index) => [
    (index + 1).toString(),
    allocation.symbol,
    allocation.name.length > 30 ? allocation.name.substring(0, 30) + '...' : allocation.name,
    allocation.isShariKing ? '👑' : '',
    showAmounts ? `${Math.round((allocation.percentage / 100) * portfolioData.monthlyInvestment)} €` : 'Masqué',
    `${allocation.percentage.toFixed(1)}%`,
    showAmounts ? `~${(Math.round((allocation.percentage / 100) * portfolioData.monthlyInvestment) / allocation.currentPrice).toFixed(2)}` : 'Masqué'
  ])

  doc.autoTable({
    startY: yPosition,
    head: [['#', 'Symbole', 'Nom de l\'entreprise', 'King', 'Montant', '%', 'Actions']],
    body: investmentPlan,
    theme: 'striped',
    headStyles: { fillColor: [41, 128, 185] },
    margin: { left: margin, right: margin },
    styles: { fontSize: 9 },
    columnStyles: {
      0: { cellWidth: 10 },
      1: { cellWidth: 20 },
      2: { cellWidth: 50 },
      3: { cellWidth: 12 },
      4: { cellWidth: 25 },
      5: { cellWidth: 15 },
      6: { cellWidth: 20 }
    }
  })

  yPosition = (doc as any).lastAutoTable.finalY + 20

  // Notes importantes
  doc.setFontSize(12)
  doc.setFont('helvetica', 'bold')
  doc.text('Notes Importantes', margin, yPosition)
  yPosition += 10

  doc.setFontSize(9)
  doc.setFont('helvetica', 'normal')
  const notes = [
    '• Ce plan est basé sur votre profil de risque et vos préférences éthiques',
    '• Les montants sont calculés selon votre budget mensuel déclaré',
    '• Les prix des actions peuvent varier, ajustez les quantités en conséquence',
    '• Vérifiez toujours la conformité Sharia avant d\'investir',
    '• Les entreprises marquées 👑 sont des "Sharia Kings" (halal depuis 10+ ans)',
    '• Consultez un conseiller financier pour des investissements importants'
  ]

  notes.forEach(note => {
    doc.text(note, margin, yPosition)
    yPosition += 5
  })

  // Footer
  doc.setFontSize(8)
  doc.setFont('helvetica', 'normal')
  doc.text(
    'Halal Invest - Plan d\'investissement confidentiel',
    pageWidth / 2,
    doc.internal.pageSize.height - 10,
    { align: 'center' }
  )

  // Télécharger le PDF
  const fileName = `plan-investissement-halal-${new Date().toISOString().split('T')[0]}.pdf`
  doc.save(fileName)
}
