import { generatePersonalizedPortfolio } from '../portfolio-generator'
import { UserProfile, Stock } from '@/types'

// Mock data pour les tests
const mockStocks: Stock[] = [
  {
    id: '1',
    symbol: 'AAPL',
    name: 'Apple Inc.',
    sector: 'Technology',
    isShariCompliant: true,
    shariaKingSince: '2015-01-01',
    debtRatio: 15,
    nonHalalRevenueRatio: 2,
    liquidityRatio: 10,
    currentPrice: 150,
    lastChecked: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '2',
    symbol: 'MSFT',
    name: 'Microsoft Corporation',
    sector: 'Technology',
    isShariCompliant: true,
    shariaKingSince: '2016-01-01',
    debtRatio: 20,
    nonHalalRevenueRatio: 3,
    liquidityRatio: 12,
    currentPrice: 300,
    lastChecked: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '3',
    symbol: 'GOOGL',
    name: 'Alphabet Inc.',
    sector: 'Technology',
    isShariCompliant: true,
    debtRatio: 25,
    nonHalalRevenueRatio: 4,
    liquidityRatio: 15,
    currentPrice: 2500,
    lastChecked: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '4',
    symbol: 'JNJ',
    name: 'Johnson & Johnson',
    sector: 'Healthcare',
    isShariCompliant: true,
    shariaKingSince: '2014-01-01',
    debtRatio: 18,
    nonHalalRevenueRatio: 1,
    liquidityRatio: 8,
    currentPrice: 160,
    lastChecked: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '5',
    symbol: 'PG',
    name: 'Procter & Gamble Co.',
    sector: 'Consumer Goods',
    isShariCompliant: true,
    shariaKingSince: '2013-01-01',
    debtRatio: 22,
    nonHalalRevenueRatio: 2,
    liquidityRatio: 11,
    currentPrice: 140,
    lastChecked: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
]

const mockProfile: UserProfile = {
  id: 'test-user',
  riskTolerance: 'moderate',
  investmentHorizon: 'long-term',
  monthlyBudget: 1000,
  shariaPurityLevel: 98,
  boycottPreferences: [],
  ethicalPreferences: [],
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
}

describe('Portfolio Generator', () => {
  test('should not generate duplicate stocks in portfolio', () => {
    const portfolio = generatePersonalizedPortfolio(mockProfile, mockStocks)
    
    // Vérifier qu'il n'y a pas de doublons
    const symbols = portfolio.map(allocation => allocation.symbol)
    const uniqueSymbols = [...new Set(symbols)]
    
    expect(symbols.length).toBe(uniqueSymbols.length)
    expect(symbols).toEqual(uniqueSymbols)
  })

  test('should generate valid allocations for conservative profile', () => {
    const conservativeProfile = { ...mockProfile, riskTolerance: 'conservative' as const }
    const portfolio = generatePersonalizedPortfolio(conservativeProfile, mockStocks)
    
    // Vérifier qu'il n'y a pas de doublons
    const symbols = portfolio.map(allocation => allocation.symbol)
    const uniqueSymbols = [...new Set(symbols)]
    expect(symbols.length).toBe(uniqueSymbols.length)
    
    // Vérifier que le total fait environ 100%
    const totalPercentage = portfolio.reduce((sum, allocation) => sum + allocation.percentage, 0)
    expect(totalPercentage).toBeCloseTo(100, 1)
  })

  test('should generate valid allocations for moderate profile', () => {
    const moderateProfile = { ...mockProfile, riskTolerance: 'moderate' as const }
    const portfolio = generatePersonalizedPortfolio(moderateProfile, mockStocks)
    
    // Vérifier qu'il n'y a pas de doublons
    const symbols = portfolio.map(allocation => allocation.symbol)
    const uniqueSymbols = [...new Set(symbols)]
    expect(symbols.length).toBe(uniqueSymbols.length)
    
    // Vérifier que le total fait environ 100%
    const totalPercentage = portfolio.reduce((sum, allocation) => sum + allocation.percentage, 0)
    expect(totalPercentage).toBeCloseTo(100, 1)
  })

  test('should generate valid allocations for aggressive profile', () => {
    const aggressiveProfile = { ...mockProfile, riskTolerance: 'aggressive' as const }
    const portfolio = generatePersonalizedPortfolio(aggressiveProfile, mockStocks)
    
    // Vérifier qu'il n'y a pas de doublons
    const symbols = portfolio.map(allocation => allocation.symbol)
    const uniqueSymbols = [...new Set(symbols)]
    expect(symbols.length).toBe(uniqueSymbols.length)
    
    // Vérifier que le total fait environ 100%
    const totalPercentage = portfolio.reduce((sum, allocation) => sum + allocation.percentage, 0)
    expect(totalPercentage).toBeCloseTo(100, 1)
  })

  test('should handle empty stock list gracefully', () => {
    const portfolio = generatePersonalizedPortfolio(mockProfile, [])
    expect(portfolio).toEqual([])
  })

  test('should prioritize Sharia Kings for conservative profile', () => {
    const conservativeProfile = { ...mockProfile, riskTolerance: 'conservative' as const }
    const portfolio = generatePersonalizedPortfolio(conservativeProfile, mockStocks)
    
    // Vérifier qu'il n'y a pas de doublons
    const symbols = portfolio.map(allocation => allocation.symbol)
    const uniqueSymbols = [...new Set(symbols)]
    expect(symbols.length).toBe(uniqueSymbols.length)
    
    // Les premières allocations devraient être des Sharia Kings
    const shariaKings = portfolio.filter(allocation => allocation.isShariKing)
    expect(shariaKings.length).toBeGreaterThan(0)
  })
})
