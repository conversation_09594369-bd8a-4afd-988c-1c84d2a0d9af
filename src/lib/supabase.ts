import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Types pour la base de données
export type Database = {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          risk_tolerance: 'conservative' | 'moderate' | 'aggressive'
          investment_horizon: 'short' | 'medium' | 'long'
          monthly_budget: number
          sharia_purity_level: number // 95-100
          boycott_preferences: string[] | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          risk_tolerance?: 'conservative' | 'moderate' | 'aggressive'
          investment_horizon?: 'short' | 'medium' | 'long'
          monthly_budget?: number
          sharia_purity_level?: number
          boycott_preferences?: string[] | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          risk_tolerance?: 'conservative' | 'moderate' | 'aggressive'
          investment_horizon?: 'short' | 'medium' | 'long'
          monthly_budget?: number
          sharia_purity_level?: number
          boycott_preferences?: string[] | null
          created_at?: string
          updated_at?: string
        }
      }
      stocks: {
        Row: {
          id: string
          symbol: string
          name: string
          sector: string
          is_sharia_compliant: boolean
          sharia_king_since: string | null // Date depuis laquelle c'est un Sharia King
          debt_ratio: number
          non_halal_revenue_ratio: number
          liquidity_ratio: number
          last_checked: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          symbol: string
          name: string
          sector: string
          is_sharia_compliant?: boolean
          sharia_king_since?: string | null
          debt_ratio?: number
          non_halal_revenue_ratio?: number
          liquidity_ratio?: number
          last_checked?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          symbol?: string
          name?: string
          sector?: string
          is_sharia_compliant?: boolean
          sharia_king_since?: string | null
          debt_ratio?: number
          non_halal_revenue_ratio?: number
          liquidity_ratio?: number
          last_checked?: string
          created_at?: string
          updated_at?: string
        }
      }
      portfolios: {
        Row: {
          id: string
          user_id: string
          name: string
          total_amount: number
          allocations: {
            symbol: string
            percentage: number
            amount: number
          }[]
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          total_amount: number
          allocations: {
            symbol: string
            percentage: number
            amount: number
          }[]
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          total_amount?: number
          allocations?: {
            symbol: string
            percentage: number
            amount: number
          }[]
          created_at?: string
          updated_at?: string
        }
      }
      notifications: {
        Row: {
          id: string
          user_id: string
          type: 'sharia_compliance_change' | 'portfolio_update' | 'new_recommendation'
          title: string
          message: string
          is_read: boolean
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          type: 'sharia_compliance_change' | 'portfolio_update' | 'new_recommendation'
          title: string
          message: string
          is_read?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          type?: 'sharia_compliance_change' | 'portfolio_update' | 'new_recommendation'
          title?: string
          message?: string
          is_read?: boolean
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
