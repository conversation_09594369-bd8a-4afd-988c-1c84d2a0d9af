import NodeCache from 'node-cache'

/**
 * Gestionnaire de cache intelligent pour les données financières
 * Gère différents types de cache avec TTL adaptatifs et stratégies de récupération
 */

export interface CacheConfig {
  stdTTL: number
  checkperiod: number
  useClones: boolean
  maxKeys?: number
}

export interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number
  accessCount: number
  lastAccessed: number
}

export class FinancialDataCache {
  private cache: NodeCache
  private config: CacheConfig
  private accessStats: Map<string, { hits: number; misses: number }> = new Map()

  constructor(config: CacheConfig) {
    this.config = config
    this.cache = new NodeCache(config)
    
    // Événements de cache
    this.cache.on('set', (key, value) => {
      console.log(`Cache SET: ${key}`)
    })
    
    this.cache.on('del', (key, value) => {
      console.log(`Cache DEL: ${key}`)
    })
    
    this.cache.on('expired', (key, value) => {
      console.log(`Cache EXPIRED: ${key}`)
    })
  }

  /**
   * Récupère une valeur du cache avec statistiques d'accès
   */
  get<T>(key: string): CacheEntry<T> | undefined {
    const entry = this.cache.get<CacheEntry<T>>(key)
    
    if (entry) {
      // Mettre à jour les statistiques d'accès
      entry.accessCount++
      entry.lastAccessed = Date.now()
      this.cache.set(key, entry, entry.ttl)
      
      // Statistiques globales
      const stats = this.accessStats.get(key) || { hits: 0, misses: 0 }
      stats.hits++
      this.accessStats.set(key, stats)
      
      return entry
    } else {
      // Miss
      const stats = this.accessStats.get(key) || { hits: 0, misses: 0 }
      stats.misses++
      this.accessStats.set(key, stats)
      
      return undefined
    }
  }

  /**
   * Stocke une valeur dans le cache avec métadonnées
   */
  set<T>(key: string, data: T, ttl: number): boolean {
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl,
      accessCount: 0,
      lastAccessed: Date.now()
    }
    
    return this.cache.set(key, entry, ttl)
  }

  /**
   * Récupère ou calcule une valeur avec cache intelligent
   */
  async getOrSet<T>(
    key: string,
    fetchFunction: () => Promise<T>,
    ttl: number,
    options?: {
      staleWhileRevalidate?: boolean
      maxStaleTime?: number
    }
  ): Promise<T> {
    const entry = this.get<T>(key)
    const now = Date.now()
    
    // Cache hit valide
    if (entry && (now - entry.timestamp) < entry.ttl * 1000) {
      return entry.data
    }
    
    // Stale-while-revalidate: retourner les données périmées et rafraîchir en arrière-plan
    if (options?.staleWhileRevalidate && entry) {
      const staleTime = now - entry.timestamp
      const maxStaleTime = (options.maxStaleTime || ttl * 2) * 1000
      
      if (staleTime < maxStaleTime) {
        // Rafraîchir en arrière-plan
        this.refreshInBackground(key, fetchFunction, ttl)
        return entry.data
      }
    }
    
    // Cache miss ou données trop anciennes
    try {
      const data = await fetchFunction()
      this.set(key, data, ttl)
      return data
    } catch (error) {
      // En cas d'erreur, retourner les données en cache si disponibles
      if (entry) {
        console.warn(`API error, using stale cache for ${key}:`, error)
        return entry.data
      }
      throw error
    }
  }

  /**
   * Rafraîchit les données en arrière-plan
   */
  private async refreshInBackground<T>(
    key: string,
    fetchFunction: () => Promise<T>,
    ttl: number
  ): Promise<void> {
    try {
      const data = await fetchFunction()
      this.set(key, data, ttl)
      console.log(`Background refresh completed for ${key}`)
    } catch (error) {
      console.warn(`Background refresh failed for ${key}:`, error)
    }
  }

  /**
   * Supprime une clé du cache
   */
  del(key: string): number {
    this.accessStats.delete(key)
    return this.cache.del(key)
  }

  /**
   * Supprime plusieurs clés
   */
  delMany(keys: string[]): number {
    keys.forEach(key => this.accessStats.delete(key))
    return this.cache.del(keys)
  }

  /**
   * Vide tout le cache
   */
  flushAll(): void {
    this.cache.flushAll()
    this.accessStats.clear()
  }

  /**
   * Obtient les statistiques du cache
   */
  getStats() {
    const cacheStats = this.cache.getStats()
    const keys = this.cache.keys()
    
    // Calculer les statistiques par clé
    const keyStats = Array.from(this.accessStats.entries()).map(([key, stats]) => ({
      key,
      hits: stats.hits,
      misses: stats.misses,
      hitRate: stats.hits / (stats.hits + stats.misses),
      exists: keys.includes(key)
    }))
    
    return {
      global: cacheStats,
      keys: keyStats,
      totalKeys: keys.length,
      memoryUsage: process.memoryUsage()
    }
  }

  /**
   * Nettoie les entrées les moins utilisées
   */
  cleanup(maxKeys?: number): number {
    const keys = this.cache.keys()
    const limit = maxKeys || this.config.maxKeys || 1000
    
    if (keys.length <= limit) {
      return 0
    }
    
    // Récupérer les métadonnées de toutes les entrées
    const entries = keys.map(key => {
      const entry = this.cache.get<CacheEntry<any>>(key)
      return {
        key,
        accessCount: entry?.accessCount || 0,
        lastAccessed: entry?.lastAccessed || 0,
        age: Date.now() - (entry?.timestamp || 0)
      }
    })
    
    // Trier par score (moins utilisées et plus anciennes en premier)
    entries.sort((a, b) => {
      const scoreA = a.accessCount / (a.age / 1000 / 60) // accès par minute
      const scoreB = b.accessCount / (b.age / 1000 / 60)
      return scoreA - scoreB
    })
    
    // Supprimer les entrées les moins utiles
    const toDelete = entries.slice(0, keys.length - limit)
    const deletedKeys = toDelete.map(entry => entry.key)
    
    this.delMany(deletedKeys)
    
    console.log(`Cleaned up ${deletedKeys.length} cache entries`)
    return deletedKeys.length
  }

  /**
   * Préchauffe le cache avec des données
   */
  async warmup<T>(
    entries: Array<{ key: string; fetchFunction: () => Promise<T>; ttl: number }>
  ): Promise<void> {
    console.log(`Warming up cache with ${entries.length} entries...`)
    
    const promises = entries.map(async ({ key, fetchFunction, ttl }) => {
      try {
        const data = await fetchFunction()
        this.set(key, data, ttl)
      } catch (error) {
        console.warn(`Failed to warm up cache for ${key}:`, error)
      }
    })
    
    await Promise.allSettled(promises)
    console.log('Cache warmup completed')
  }

  /**
   * Obtient les clés qui correspondent à un pattern
   */
  getKeysByPattern(pattern: RegExp): string[] {
    return this.cache.keys().filter(key => pattern.test(key))
  }

  /**
   * Invalide le cache pour un pattern de clés
   */
  invalidatePattern(pattern: RegExp): number {
    const keys = this.getKeysByPattern(pattern)
    return this.delMany(keys)
  }
}

// Instance globale du cache
export const financialCache = new FinancialDataCache({
  stdTTL: 300, // 5 minutes par défaut
  checkperiod: 60, // Vérification toutes les minutes
  useClones: false,
  maxKeys: 10000 // Limite de 10k entrées
})

// Types de cache prédéfinis
export const CACHE_TYPES = {
  QUOTE: 'quote',
  STATS: 'stats', 
  HISTORICAL: 'historical',
  COMPANY: 'company',
  SEARCH: 'search'
} as const

export type CacheType = typeof CACHE_TYPES[keyof typeof CACHE_TYPES]

// TTL par type de données
export const CACHE_TTL = {
  [CACHE_TYPES.QUOTE]: 60, // 1 minute
  [CACHE_TYPES.STATS]: 3600, // 1 heure
  [CACHE_TYPES.HISTORICAL]: 86400, // 24 heures
  [CACHE_TYPES.COMPANY]: 604800, // 7 jours
  [CACHE_TYPES.SEARCH]: 1800 // 30 minutes
} as const

/**
 * Génère une clé de cache standardisée
 */
export const generateCacheKey = (
  type: CacheType,
  identifier: string,
  params?: Record<string, any>
): string => {
  const baseKey = `${type}:${identifier.toUpperCase()}`
  
  if (params && Object.keys(params).length > 0) {
    const paramString = Object.keys(params)
      .sort()
      .map(key => `${key}=${params[key]}`)
      .join('&')
    return `${baseKey}:${paramString}`
  }
  
  return baseKey
}
