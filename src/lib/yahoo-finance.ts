import yahooFinance from 'yahoo-finance2'
import { YahooFinanceQuote, YahooFinancialStats, YahooFinanceHistoricalData } from '@/types'
import { financialCache, generateCacheKey, CACHE_TYPES, CACHE_TTL } from './cache-manager'

/**
 * Service Yahoo Finance avec cache intelligent
 * Récupère les données financières en temps réel avec mise en cache pour optimiser les performances
 */

/**
 * Récupère les données de cotation en temps réel
 */
export const getQuote = async (symbol: string): Promise<YahooFinanceQuote> => {
  const cacheKey = generateCacheKey(CACHE_TYPES.QUOTE, symbol)

  return financialCache.getOrSet(
    cacheKey,
    async () => {
      const result = await yahooFinance.quote(symbol)

      return {
        symbol: result.symbol || symbol,
        regularMarketPrice: result.regularMarketPrice || 0,
        regularMarketChange: result.regularMarketChange || 0,
        regularMarketChangePercent: result.regularMarketChangePercent || 0,
        regularMarketVolume: result.regularMarketVolume || 0,
        marketCap: result.marketCap || 0,
        trailingPE: (result as any).trailingPE,
        forwardPE: (result as any).forwardPE,
        dividendYield: (result as any).dividendYield,
        fiftyTwoWeekLow: result.fiftyTwoWeekLow || 0,
        fiftyTwoWeekHigh: result.fiftyTwoWeekHigh || 0,
        currency: result.currency || 'USD',
        exchangeName: result.fullExchangeName || '',
        longName: result.longName || '',
        shortName: result.shortName || '',
        sector: (result as any).sector,
        industry: (result as any).industry
      } as YahooFinanceQuote
    },
    CACHE_TTL[CACHE_TYPES.QUOTE],
    { staleWhileRevalidate: true, maxStaleTime: 300 }
  )
}

/**
 * Récupère plusieurs cotations en une seule fois
 */
export const getMultipleQuotes = async (symbols: string[]): Promise<YahooFinanceQuote[]> => {
  const promises = symbols.map(symbol => getQuote(symbol))
  const results = await Promise.allSettled(promises)
  
  return results
    .filter((result): result is PromiseFulfilledResult<YahooFinanceQuote> => 
      result.status === 'fulfilled'
    )
    .map(result => result.value)
}

/**
 * Récupère les statistiques financières détaillées
 */
export const getFinancialStats = async (symbol: string): Promise<YahooFinancialStats> => {
  const cacheKey = generateCacheKey(CACHE_TYPES.STATS, symbol)

  return financialCache.getOrSet(
    cacheKey,
    async () => {
      const [quoteSummary, financials] = await Promise.all([
        yahooFinance.quoteSummary(symbol, {
          modules: ['defaultKeyStatistics', 'financialData', 'balanceSheetHistory']
        }),
        yahooFinance.quoteSummary(symbol, {
          modules: ['incomeStatementHistory', 'cashflowStatementHistory']
        })
      ])

      const defaultKeyStatistics = quoteSummary.defaultKeyStatistics
      const financialData = quoteSummary.financialData
      const balanceSheet = quoteSummary.balanceSheetHistory?.balanceSheetStatements?.[0]
      const incomeStatement = financials.incomeStatementHistory?.incomeStatementHistory?.[0]
      const cashflow = financials.cashflowStatementHistory?.cashflowStatements?.[0]

      return {
        symbol,
        totalDebt: (balanceSheet as any)?.totalDebt || 0,
        totalCash: balanceSheet?.cash || 0,
        totalRevenue: (incomeStatement as any)?.totalRevenue || 0,
        grossProfits: (incomeStatement as any)?.grossProfit || 0,
        operatingCashflow: (cashflow as any)?.totalCashFromOperatingActivities || 0,
        freeCashflow: (cashflow as any)?.freeCashflow || 0,
        returnOnEquity: (defaultKeyStatistics as any)?.returnOnEquity || 0,
        returnOnAssets: (defaultKeyStatistics as any)?.returnOnAssets || 0,
        debtToEquity: (financialData as any)?.debtToEquity || 0,
        currentRatio: (financialData as any)?.currentRatio || 0,
        quickRatio: (financialData as any)?.quickRatio || 0,
        lastUpdated: new Date().toISOString()
      } as YahooFinancialStats
    },
    CACHE_TTL[CACHE_TYPES.STATS],
    { staleWhileRevalidate: true, maxStaleTime: 7200 }
  )
}

/**
 * Récupère l'historique des prix
 */
export const getHistoricalData = async (
  symbol: string,
  period1: Date,
  period2: Date = new Date(),
  interval: '1d' | '1wk' | '1mo' = '1d'
): Promise<YahooFinanceHistoricalData[]> => {
  const cacheKey = generateCacheKey(CACHE_TYPES.HISTORICAL, symbol, {
    period1: period1.toISOString().split('T')[0],
    period2: period2.toISOString().split('T')[0],
    interval
  })

  return financialCache.getOrSet(
    cacheKey,
    async () => {
      const result = await yahooFinance.historical(symbol, {
        period1,
        period2,
        interval
      })

      return result.map(item => ({
        date: item.date,
        open: item.open,
        high: item.high,
        low: item.low,
        close: item.close,
        adjClose: item.adjClose,
        volume: item.volume
      })) as YahooFinanceHistoricalData[]
    },
    CACHE_TTL[CACHE_TYPES.HISTORICAL],
    { staleWhileRevalidate: true, maxStaleTime: 172800 }
  )
}

/**
 * Recherche d'actions par nom ou symbole
 */
export const searchStocks = async (query: string): Promise<any[]> => {
  const cacheKey = generateCacheKey(CACHE_TYPES.SEARCH, query)

  return financialCache.getOrSet(
    cacheKey,
    async () => {
      const result = await yahooFinance.search(query)
      return result.quotes || []
    },
    CACHE_TTL[CACHE_TYPES.SEARCH]
  )
}

/**
 * Vide le cache pour un symbole spécifique ou tout le cache
 */
export const clearCache = (symbol?: string): void => {
  if (symbol) {
    const pattern = new RegExp(symbol.toUpperCase())
    const deletedCount = financialCache.invalidatePattern(pattern)
    console.log(`Cache cleared for ${symbol}: ${deletedCount} entries deleted`)
  } else {
    financialCache.flushAll()
    console.log('All cache cleared')
  }
}

/**
 * Obtient les statistiques du cache
 */
export const getCacheStats = () => {
  return financialCache.getStats()
}

/**
 * Précharge les données pour une liste de symboles
 */
export const preloadData = async (symbols: string[]): Promise<void> => {
  console.log(`Preloading data for ${symbols.length} symbols...`)

  const promises = symbols.map(async (symbol) => {
    try {
      await Promise.all([
        getQuote(symbol),
        getFinancialStats(symbol)
      ])
    } catch (error) {
      console.warn(`Failed to preload data for ${symbol}:`, error)
    }
  })

  await Promise.allSettled(promises)
  console.log('Preloading completed')
}

/**
 * Calcule les ratios Sharia à partir des données financières
 */
export const calculateShariaRatios = (stats: YahooFinancialStats) => {
  const debtRatio = stats.totalDebt && stats.totalRevenue
    ? (stats.totalDebt / stats.totalRevenue) * 100
    : 0

  const liquidityRatio = stats.totalCash && stats.totalRevenue
    ? (stats.totalCash / stats.totalRevenue) * 100
    : 0

  return {
    debtRatio: Math.round(debtRatio * 100) / 100,
    liquidityRatio: Math.round(liquidityRatio * 100) / 100,
    isCompliant: debtRatio <= 33 && liquidityRatio <= 33
  }
}
