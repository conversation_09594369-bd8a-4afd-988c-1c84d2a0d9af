import { supabase } from './supabase'

/**
 * Vérifie la configuration de l'application
 */
export async function checkConfiguration() {
  console.log('🔍 Vérification de la configuration...')
  
  const issues: string[] = []
  const success: string[] = []

  // 1. Vérifier les variables d'environnement
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
    issues.push('❌ NEXT_PUBLIC_SUPABASE_URL manquant')
  } else {
    success.push('✅ NEXT_PUBLIC_SUPABASE_URL configuré')
  }

  if (!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    issues.push('❌ NEXT_PUBLIC_SUPABASE_ANON_KEY manquant')
  } else {
    success.push('✅ NEXT_PUBLIC_SUPABASE_ANON_KEY configuré')
  }

  if (!process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID) {
    issues.push('❌ NEXT_PUBLIC_GOOGLE_CLIENT_ID manquant')
  } else {
    success.push('✅ NEXT_PUBLIC_GOOGLE_CLIENT_ID configuré')
  }

  // 2. Tester la connexion Supabase
  try {
    const { data, error } = await supabase.auth.getSession()
    if (error) {
      issues.push(`❌ Erreur connexion Supabase: ${error.message}`)
    } else {
      success.push('✅ Connexion Supabase OK')
    }
  } catch (error) {
    issues.push(`❌ Erreur connexion Supabase: ${error}`)
  }

  // 3. Vérifier si les tables existent
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('id')
      .limit(1)
    
    if (error && error.message.includes('does not exist')) {
      issues.push('❌ Table profiles n\'existe pas')
    } else if (error) {
      issues.push(`❌ Erreur table profiles: ${error.message}`)
    } else {
      success.push('✅ Table profiles existe')
    }
  } catch (error) {
    issues.push(`❌ Erreur vérification tables: ${error}`)
  }

  // 4. Afficher les résultats
  console.log('\n📊 Résultats de la vérification:')
  success.forEach(msg => console.log(msg))
  issues.forEach(msg => console.log(msg))

  if (issues.length === 0) {
    console.log('\n🎉 Configuration complète !')
  } else {
    console.log('\n⚠️  Configuration incomplète. Voir les erreurs ci-dessus.')
  }

  return {
    success: success.length,
    issues: issues.length,
    details: { success, issues }
  }
}

/**
 * Teste spécifiquement Google OAuth
 */
export async function testGoogleOAuth() {
  console.log('🔍 Test Google OAuth...')
  
  try {
    // Tenter d'initier une connexion Google (sans redirection)
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/auth/callback`,
        skipBrowserRedirect: true // Pour tester sans redirection
      }
    })

    if (error) {
      console.log('❌ Google OAuth non configuré:', error.message)
      return false
    } else {
      console.log('✅ Google OAuth configuré correctement')
      return true
    }
  } catch (error) {
    console.log('❌ Erreur test Google OAuth:', error)
    return false
  }
}

/**
 * Guide de configuration étape par étape
 */
export function showConfigurationGuide() {
  console.log(`
🔧 GUIDE DE CONFIGURATION GOOGLE OAUTH

1. Dans Supabase Dashboard (https://supabase.com/dashboard):
   - Aller dans Authentication > Providers
   - Activer "Google"
   - Client ID: VOTRE_GOOGLE_CLIENT_ID
   - Client Secret: VOTRE_GOOGLE_CLIENT_SECRET

2. Dans Google Cloud Console (https://console.cloud.google.com/):
   - APIs & Services > Credentials
   - Authorized redirect URIs:
     * https://VOTRE_PROJET_ID.supabase.co/auth/v1/callback
   - Authorized JavaScript origins:
     * http://localhost:3001

3. Variables d'environnement (.env.local):
   NEXT_PUBLIC_SUPABASE_URL=https://VOTRE_PROJET_ID.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=VOTRE_SUPABASE_ANON_KEY
   NEXT_PUBLIC_GOOGLE_CLIENT_ID=VOTRE_GOOGLE_CLIENT_ID
   GOOGLE_CLIENT_SECRET=VOTRE_GOOGLE_CLIENT_SECRET

4. Redémarrer l'application après configuration

📖 Voir les fichiers SETUP_DATABASE.md et GOOGLE_OAUTH_SETUP.md pour plus de détails
  `)
}
