# 🕌 Halal Invest Advisor

Une plateforme d'investissement conforme à la Sharia qui aide les musulmans à créer des portefeuilles d'investissement éthiques et halal.

## 🎯 Vision

Démocratiser l'investissement halal en fournissant des outils intelligents pour créer et gérer des portefeuilles conformes aux principes islamiques.

## ✨ Fonctionnalités Implémentées

### 🔍 Screening Sharia Automatique ✅
- ✅ Filtrage automatique selon les critères AAOIFI
- ✅ Exclusion des secteurs interdits (banques conventionnelles, alcool, etc.)
- ✅ Vérification des ratios financiers (dette <33%, revenus non-halal <5%)
- ✅ Contrôle de pureté avec slider 95%-100%

### 🎯 Générateur de Portefeuille Personnalisé ✅
- ✅ Questionnaire de profil de risque interactif
- ✅ Génération automatique de portefeuille ETF "maison"
- ✅ Répartition automatique en euros selon budget mensuel
- ✅ Exclusion d'entreprises selon boycotts personnels
- ✅ Algorithme adaptatif selon profil (conservateur/équilibré/agressif)

### 📊 Interface Portefeuille Complète ✅
- ✅ Page "Mon Portefeuille" avec répartition détaillée
- ✅ Graphiques interactifs (positions, secteurs, performance)
- ✅ Affichage montants en euros avec option masquage
- ✅ Export PDF professionnel (portefeuille + plan d'investissement)
- ✅ Métriques en temps réel (gains, Sharia Kings, conformité)

### 🔔 Système de Notifications ✅
- ✅ Bannières de notifications animées
- ✅ Alertes Sharia (changements de statut, non-conformité)
- ✅ Notifications portfolio (rééquilibrage, performance)
- ✅ Gestion des priorités et auto-dismiss
- ✅ Actions personnalisées (navigation, liens)

### 🏆 Sharia Kings ✅
- ✅ Identification des entreprises halal depuis 10+ ans
- ✅ Classification de stabilité éthique
- ✅ Priorité dans les recommandations d'allocation
- ✅ Badge visuel dans l'interface

### 🎨 UX/UI Optimisée ✅
- ✅ Système de thème complet (clair/sombre/système)
- ✅ Interface responsive et mobile-first
- ✅ Animations fluides avec Framer Motion
- ✅ Sliders intelligents pour budget et pureté
- ✅ Navigation intuitive entre les pages

## 🚀 État du Développement

### ✅ **TERMINÉ** - Phases 1-5 (MVP Complet)
- [x] **Phase 1**: Profil utilisateur et onboarding
- [x] **Phase 2**: Générateur de portefeuille ETF maison
- [x] **Phase 3**: Interface portefeuille complète
- [x] **Phase 4**: Système de notifications
- [x] **Phase 5**: Améliorations UX/UI

### 🔄 **EN COURS** - Phase 6: Fonctionnalités Sharia Avancées
- [x] Filtre Sharia automatique AAOIFI complet
- [x] Classification Sharia King (halal depuis 10+ ans)
- [x] Contrôle de pureté avec slider 95%-100%
- [ ] **À FAIRE**: Watchlist et suivi d'entreprises avec alertes

### 📋 **À FAIRE** - Prochaines Phases

#### Phase 7: Intégrations API Financières
- [ ] Intégration API Yahoo Finance pour données en temps réel
- [ ] Intégration API Alpha Vantage pour données financières détaillées
- [ ] Système de cache intelligent pour optimiser les performances
- [ ] Mise à jour automatique des statuts Sharia
- [ ] Gestion des erreurs et fallbacks API

#### Phase 8: Fonctionnalités Avancées
- [ ] Système de watchlist personnalisée
- [ ] Alertes email/push pour changements Sharia
- [ ] Backtesting de stratégies d'investissement
- [ ] Comparaison avec indices halal (FTSE Shariah, etc.)
- [ ] Recommandations IA basées sur le profil utilisateur

#### Phase 9: Optimisations et Production
- [ ] Tests automatisés complets (Jest, Cypress)
- [ ] Optimisations de performance (lazy loading, code splitting)
- [ ] SEO et métadonnées optimisées
- [ ] Monitoring et analytics (Sentry, Google Analytics)
- [ ] Documentation API complète

#### Phase 10: Fonctionnalités Premium
- [ ] Intégration avec brokers (simulation d'ordres)
- [ ] Rapports PDF avancés avec analyses
- [ ] Alertes SMS/WhatsApp
- [ ] Application mobile React Native
- [ ] Mode multi-devises (EUR, USD, GBP)

## �️ Stack Technique

### **Frontend**
- **Framework**: Next.js 15 avec Turbopack
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Animations**: Framer Motion
- **Components**: Radix UI
- **Charts**: Recharts (à intégrer)

### **Backend & Database**
- **Database**: Supabase (PostgreSQL)
- **Auth**: Supabase Auth
- **Storage**: Supabase Storage
- **API**: Next.js API Routes

### **Outils & Intégrations**
- **Package Manager**: **Bun** (recommandé) ou npm
- **PDF Export**: jsPDF + jsPDF-AutoTable
- **Theme**: next-themes
- **Deployment**: Vercel
- **Version Control**: Git + GitHub

## 🚀 Installation et Développement

### Prérequis
- **Bun** (recommandé) ou Node.js 18+
- Git

### Installation avec Bun (Recommandé)
```bash
# Cloner le repository
git clone https://github.com/amintt2/halal-invest.git
cd halal-invest

# Installer les dépendances avec Bun
bun install

# Configurer les variables d'environnement
cp .env.example .env.local
# Remplir les clés Supabase et API

# Lancer en développement avec Bun
bun run dev
```

### Installation avec npm (Alternative)
```bash
# Installer les dépendances avec npm
npm install

# Lancer en développement avec npm
npm run dev
```

### Scripts Disponibles
```bash
# Développement
bun run dev          # Serveur de développement
bun run build        # Build de production
bun run start        # Serveur de production
bun run lint         # Linting du code

# Tests (à implémenter)
bun run test         # Tests unitaires
bun run test:e2e     # Tests end-to-end
```

## 🔧 Configuration

### Variables d'environnement requises:
```env
# Supabase (Obligatoire pour l'auth et la DB)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# APIs Financières (À configurer pour les données réelles)
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key
YAHOO_FINANCE_API_KEY=your_yahoo_finance_key

# Optionnel
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## 📊 Critères Sharia Implémentés

### Ratios AAOIFI Stricts:
- **Dette totale / Capitalisation** < 33%
- **Revenus non-halal / Revenus totaux** < 5%
- **Liquidités / Capitalisation** < 33%

### Contrôle de Pureté:
- **100%**: Critères AAOIFI stricts
- **95-99%**: Critères légèrement assouplis pour plus de diversification

### Secteurs Exclus:
- Banques conventionnelles et assurance
- Alcool et substances illicites
- Porc et produits dérivés
- Divertissement adulte
- Jeux d'argent et casinos
- Tabac et cigarettes
- Armement et défense (optionnel)

### Classification Sharia King:
- Entreprises halal depuis **10+ ans** minimum
- Historique de conformité stable
- Priorité dans les allocations de portefeuille

## 🎯 Pages Fonctionnelles

1. **Page d'accueil** (`/`) - Landing page avec navigation
2. **Onboarding** (`/onboarding`) - Questionnaire de profil complet
3. **Générateur** (`/portfolio-generator`) - Création de portefeuille personnalisé
4. **Mon Portefeuille** (`/portfolio`) - Visualisation et gestion complète
5. **Dashboard** (`/dashboard`) - Vue d'ensemble (existant)

## 🔄 Prochaines Priorités (Par Ordre)

### 🚨 **Urgent** (Semaine 1-2)
1. **Intégration APIs financières** - Remplacer les données mock
2. **Système de watchlist** - Suivi personnalisé d'entreprises
3. **Tests automatisés** - Couverture de l'algorithme de génération

### 📈 **Important** (Semaine 3-4)
4. **Optimisations performance** - Cache et lazy loading
5. **Alertes email/push** - Notifications externes
6. **Rapports PDF avancés** - Analyses détaillées

### 🎯 **Moyen terme** (Mois 2-3)
7. **Application mobile** - React Native
8. **Backtesting** - Test de stratégies historiques
9. **Mode multi-devises** - Support EUR/USD/GBP

## 🤝 Contribution

Les contributions sont les bienvenues !

### Pour contribuer:
1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/AmazingFeature`)
3. Commit les changements (`git commit -m 'Add AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrir une Pull Request

### Guidelines:
- Utiliser **Bun** comme package manager
- Suivre les conventions TypeScript
- Ajouter des tests pour les nouvelles fonctionnalités
- Respecter les principes Sharia dans toute nouvelle feature

## 📄 License

MIT License - voir [LICENSE](LICENSE) pour plus de détails.

---

**Note**: Ce projet utilise **Bun** comme package manager recommandé pour de meilleures performances. Le système de thème a été spécialement optimisé pour la compatibilité Bun.

### Phase 2 : Avancé (Sharia Compliance)
- [ ] **Filtre Sharia automatique**
  - [ ] Critères AAOIFI implémentés :
    - [ ] Dette totale < 33% de la capitalisation
    - [ ] Revenus non-halal < 5% du total
    - [ ] Liquidités/dépôts < 33% de la capitalisation
  - [ ] Exclusion secteurs interdits :
    - [ ] Alcool et tabac
    - [ ] Banques conventionnelles (riba)
    - [ ] Gambling et casinos
    - [ ] Armement et défense
    - [ ] Divertissement immoral
  - [ ] Vérification quotidienne automatique

- [ ] **Classification "Sharia King"**
  - [ ] Entreprises halal depuis 10+ ans consécutifs
  - [ ] Badge spécial dans l'interface
  - [ ] Historique de conformité
  - [ ] Priorité dans les recommandations

- [ ] **Contrôle de pureté**
  - [ ] Slider 95% – 100% pureté
  - [ ] 95% = plus d'opportunités, plus de notifications
  - [ ] 100% = stabilité maximale, moins de choix
  - [ ] Impact sur la fréquence des mises à jour

- [ ] **Watchlist et suivi**
  - [ ] Liste d'entreprises suivies
  - [ ] Alertes de changement de statut halal
  - [ ] Suggestions d'alternatives

### Phase 3 : Fonctionnalités avancées
- [ ] **Optimisation portefeuille**
  - [ ] Backtesting historique
  - [ ] Simulation de performance
  - [ ] Rééquilibrage automatique suggéré
  - [ ] Analyse de corrélation

- [ ] **Intégrations externes**
  - [ ] API brokers (Trade Republic, IBKR)
  - [ ] Import de portefeuilles existants
  - [ ] Synchronisation automatique

- [ ] **Notifications avancées**
  - [ ] Push notifications mobiles
  - [ ] Email alerts
  - [ ] Webhook pour intégrations

- [ ] **Monétisation**
  - [ ] Abonnement premium
  - [ ] Fonctionnalités avancées payantes
  - [ ] API pour développeurs

---

## 🛠️ Stack Technique

- **Frontend** : Next.js 15 + TypeScript + Tailwind CSS
- **UI Components** : shadcn/ui
- **State Management** : Zustand
- **Backend** : Next.js API routes
- **Database & Auth** : Supabase
- **Validation** : Zod
- **HTTP Client** : Axios
- **Finance APIs** :
  - Yahoo Finance (gratuit)
  - Alpha Vantage (freemium)
  - Polygon.io (premium)

---

## 🌟 Concept "Sharia King"

> Inspiré des "Dividend Kings", mais version halal.

Un **"Sharia King"** est une entreprise qui :
- ✅ Est **Sharia compliant depuis 10+ ans consécutifs**
- ✅ Maintient ses ratios financiers dans les limites islamiques
- ✅ N'est jamais entrée dans des secteurs interdits
- ✅ (Bonus) Croissance stable et gouvernance éthique

**Exemples potentiels** : Apple, Microsoft, Nvidia, Johnson & Johnson
*(À vérifier annuellement avec l'algorithme)*

---

## 📊 Critères Sharia (AAOIFI)

### ✅ Critères d'inclusion
- Activité principale halal
- Dette/Capitalisation < 33%
- Revenus non-halal < 5%
- Liquidités/Capitalisation < 33%

### ❌ Secteurs exclus
- Alcool et tabac
- Banques conventionnelles
- Assurance conventionnelle
- Gambling et jeux d'argent
- Armement et défense
- Divertissement immoral
- Porc et produits non-halal

---

## 🗂️ Structure du projet

```
src/
├── app/                    # App Router Next.js
├── components/            # Composants UI réutilisables
├── lib/                   # Utilitaires et configurations
├── hooks/                 # Custom React hooks
├── stores/                # Zustand stores
├── types/                 # Types TypeScript
└── utils/                 # Fonctions utilitaires
```

---

## 🧠 Sliders Intelligents - Documentation Technique

### Principe
Les sliders utilisent un **mapping non-linéaire** pour donner plus de précision sur les valeurs couramment utilisées.

### Configuration par type
```typescript
// Revenus (0-50000€) - Plus de précision sur 0-2000€
incomeSliderConfig: {
  breakpoints: [
    { value: 0, percentage: 0 },
    { value: 1000, percentage: 20 },    // 20% pour 0-1000€
    { value: 2000, percentage: 35 },    // 15% pour 1000-2000€
    { value: 10000, percentage: 80 },   // 45% pour 2000-10000€
    { value: 50000, percentage: 100 }   // 20% pour 10000-50000€
  ]
}
```

### Utilisation
```tsx
<SmartSlider
  value={monthlyIncome}
  onChange={setMonthlyIncome}
  questionId="monthlyIncome"  // Détermine la configuration
/>
```

### Avantages
- **90% plus précis** sur les valeurs 0-1000€
- **UX améliorée** : Plus facile de sélectionner 150€ que 15000€
- **Mobile-friendly** : Sliders plus épais et tactiles

## 💾 Sauvegarde Automatique - Onboarding

### Principe
Toutes les réponses sont **automatiquement sauvegardées** dans les cookies pour éviter la perte de données.

### Fonctionnement
```typescript
// Sauvegarde automatique à chaque réponse
const updateAnswer = (questionId: string, value: any) => {
  // Debounce de 500ms pour éviter trop de sauvegardes
  setTimeout(() => {
    Cookies.set('halal_invest_onboarding_progress', {
      answers: newAnswers,
      currentStepIndex,
      currentQuestionIndex,
      lastUpdated: new Date().toISOString()
    }, { expires: 30 }) // 30 jours
  }, 500)
}
```

### Expérience utilisateur
- **Rafraîchissement de page** : Progression restaurée automatiquement
- **Notification intelligente** : Popup si plus de 2 réponses sauvegardées
- **Choix utilisateur** : Continuer ou recommencer depuis le début
- **Expiration** : Données supprimées après 30 jours

### Avantages
- **Zéro perte de données** même en cas de fermeture accidentelle
- **Reprise fluide** : L'utilisateur peut revenir des jours plus tard
- **UX premium** : Sensation d'application native

---

## 🔧 Configuration

1. Copier `.env.example` vers `.env.local`
2. Configurer les variables Supabase
3. Ajouter les clés API financières
4. Lancer `npm run dev`

---

## 📝 Contribution

Cocher les tâches terminées dans ce README au fur et à mesure du développement.
