#!/usr/bin/env node

/**
 * Script rapide pour vérifier l'état de la configuration
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

async function quickCheck() {
  console.log('🔍 Vérification rapide de la configuration...\n')

  // 1. Variables d'environnement
  const hasSupabaseUrl = !!process.env.NEXT_PUBLIC_SUPABASE_URL
  const hasSupabaseKey = !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  const hasGoogleClientId = !!process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID
  const hasGoogleSecret = !!process.env.GOOGLE_CLIENT_SECRET

  console.log('📋 Variables d\'environnement:')
  console.log(`  ${hasSupabaseUrl ? '✅' : '❌'} NEXT_PUBLIC_SUPABASE_URL`)
  console.log(`  ${hasSupabaseKey ? '✅' : '❌'} NEXT_PUBLIC_SUPABASE_ANON_KEY`)
  console.log(`  ${hasGoogleClientId ? '✅' : '❌'} NEXT_PUBLIC_GOOGLE_CLIENT_ID`)
  console.log(`  ${hasGoogleSecret ? '✅' : '❌'} GOOGLE_CLIENT_SECRET`)

  if (!hasSupabaseUrl || !hasSupabaseKey) {
    console.log('\n❌ Configuration Supabase incomplète')
    return
  }

  // 2. Test connexion Supabase
  console.log('\n🔗 Test connexion Supabase:')
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
    )

    const { error } = await supabase.auth.getSession()
    if (error) {
      console.log(`  ❌ Erreur: ${error.message}`)
    } else {
      console.log('  ✅ Connexion OK')
    }
  } catch (error) {
    console.log(`  ❌ Erreur: ${error.message}`)
    return
  }

  // 3. Test tables
  console.log('\n📊 Test des tables:')
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
    )

    const { error: profilesError } = await supabase
      .from('profiles')
      .select('id')
      .limit(1)

    if (profilesError && profilesError.message.includes('does not exist')) {
      console.log('  ❌ Tables non créées')
      console.log('     → Exécuter le script SQL dans Supabase Dashboard')
    } else if (profilesError) {
      console.log(`  ❌ Erreur: ${profilesError.message}`)
    } else {
      console.log('  ✅ Tables créées')
    }
  } catch (error) {
    console.log(`  ❌ Erreur: ${error.message}`)
  }

  // 4. Résumé
  console.log('\n📝 Résumé:')

  if (!hasSupabaseUrl || !hasSupabaseKey) {
    console.log('  🔧 Configurer les variables Supabase dans .env.local')
  }

  if (!hasGoogleClientId || !hasGoogleSecret) {
    console.log('  🔧 Configurer les variables Google OAuth dans .env.local')
  }

  console.log('  📖 Guide DB: SETUP_DATABASE.md')
  console.log('  📖 Guide OAuth: GOOGLE_OAUTH_SETUP.md')
  console.log('  📄 Script SQL: supabase-schema.sql')
}

quickCheck().catch(console.error)
