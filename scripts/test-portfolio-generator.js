const { generatePersonalizedPortfolio } = require('../src/lib/portfolio-generator.ts')

// Mock data pour les tests
const mockStocks = [
  {
    id: '1',
    symbol: 'AAPL',
    name: 'Apple Inc.',
    sector: 'Technology',
    isShariCompliant: true,
    shariaKingSince: '2015-01-01',
    debtRatio: 15,
    nonHalalRevenueRatio: 2,
    liquidityRatio: 10,
    currentPrice: 150,
    lastChecked: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '2',
    symbol: 'MSFT',
    name: 'Microsoft Corporation',
    sector: 'Technology',
    isShariCompliant: true,
    shariaKingSince: '2016-01-01',
    debtRatio: 20,
    nonHalalRevenueRatio: 3,
    liquidityRatio: 12,
    currentPrice: 300,
    lastChecked: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '3',
    symbol: 'GOOGL',
    name: 'Alphabet Inc.',
    sector: 'Technology',
    isShariCompliant: true,
    debtRatio: 25,
    nonHalalRevenueRatio: 4,
    liquidityRatio: 15,
    currentPrice: 2500,
    lastChecked: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '4',
    symbol: 'JNJ',
    name: 'Johnson & Johnson',
    sector: 'Healthcare',
    isShariCompliant: true,
    shariaKingSince: '2014-01-01',
    debtRatio: 18,
    nonHalalRevenueRatio: 1,
    liquidityRatio: 8,
    currentPrice: 160,
    lastChecked: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '5',
    symbol: 'PG',
    name: 'Procter & Gamble Co.',
    sector: 'Consumer Goods',
    isShariCompliant: true,
    shariaKingSince: '2013-01-01',
    debtRatio: 22,
    nonHalalRevenueRatio: 2,
    liquidityRatio: 11,
    currentPrice: 140,
    lastChecked: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
]

const mockProfile = {
  id: 'test-user',
  riskTolerance: 'moderate',
  investmentHorizon: 'long-term',
  monthlyBudget: 1000,
  shariaPurityLevel: 98,
  boycottPreferences: [],
  ethicalPreferences: [],
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
}

console.log('Testing portfolio generator...')

try {
  const portfolio = generatePersonalizedPortfolio(mockProfile, mockStocks)
  
  console.log('Generated portfolio:')
  portfolio.forEach((allocation, index) => {
    console.log(`${index + 1}. ${allocation.symbol} - ${allocation.name} - ${allocation.percentage}% (${allocation.amount}€)`)
  })
  
  // Vérifier les doublons
  const symbols = portfolio.map(allocation => allocation.symbol)
  const uniqueSymbols = [...new Set(symbols)]
  
  console.log('\nDuplicate check:')
  console.log('Total allocations:', symbols.length)
  console.log('Unique symbols:', uniqueSymbols.length)
  console.log('Has duplicates:', symbols.length !== uniqueSymbols.length)
  
  if (symbols.length !== uniqueSymbols.length) {
    console.log('DUPLICATES FOUND!')
    const duplicates = symbols.filter((symbol, index) => symbols.indexOf(symbol) !== index)
    console.log('Duplicate symbols:', [...new Set(duplicates)])
  } else {
    console.log('✅ No duplicates found!')
  }
  
  // Vérifier le total des pourcentages
  const totalPercentage = portfolio.reduce((sum, allocation) => sum + allocation.percentage, 0)
  console.log('\nTotal percentage:', totalPercentage.toFixed(2) + '%')
  
} catch (error) {
  console.error('Error testing portfolio generator:', error)
}
