-- Script de diagnostic pour les erreurs RLS
-- À exécuter dans Supabase Dashboard → SQL Editor

-- 1. Vérifier que la table profiles existe
SELECT table_name, table_schema 
FROM information_schema.tables 
WHERE table_name = 'profiles';

-- 2. Vérifier la structure de la table profiles
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'profiles' 
ORDER BY ordinal_position;

-- 3. Vérifier si RLS est activé
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename = 'profiles';

-- 4. Lister toutes les politiques RLS sur profiles
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies 
WHERE tablename = 'profiles';

-- 5. Vérifier les triggers
SELECT trigger_name, event_manipulation, action_statement
FROM information_schema.triggers 
WHERE event_object_table = 'users' 
AND trigger_schema = 'auth';

-- 6. Vérifier la fonction handle_new_user
SELECT routine_name, routine_type, routine_definition
FROM information_schema.routines 
WHERE routine_name = 'handle_new_user';

-- 7. Compter les profils existants
SELECT COUNT(*) as total_profiles FROM profiles;

-- 8. Vérifier les utilisateurs auth
SELECT id, email, created_at 
FROM auth.users 
ORDER BY created_at DESC 
LIMIT 5;

-- 9. Test de politique RLS (remplacez par votre ID utilisateur)
-- SELECT auth.uid() as current_user_id;
-- SELECT * FROM profiles WHERE id = auth.uid();

-- 10. Vérifier les permissions sur la table
SELECT grantee, privilege_type 
FROM information_schema.role_table_grants 
WHERE table_name = 'profiles';
