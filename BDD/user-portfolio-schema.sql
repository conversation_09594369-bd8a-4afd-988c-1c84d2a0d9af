-- Extension du schéma pour la gestion des portefeuilles utilisateur
-- À exécuter dans l'éditeur SQL de Supabase

-- 1. Table pour les watchlists utilisateur
CREATE TABLE IF NOT EXISTS user_watchlists (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  symbol TEXT NOT NULL,
  added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  notes TEXT,
  target_price DECIMAL(10,2),
  alert_enabled BOOLEAN DEFAULT false,
  
  -- Index unique pour éviter les doublons
  UNIQUE(user_id, symbol)
);

-- 2. Table pour les portefeuilles utilisateur
CREATE TABLE IF NOT EXISTS user_portfolios (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL DEFAULT 'Mon Portefeuille',
  description TEXT,
  total_invested DECIMAL(12,2) DEFAULT 0,
  target_amount DECIMAL(12,2),
  risk_tolerance TEXT CHECK (risk_tolerance IN ('conservative', 'moderate', 'aggressive')) DEFAULT 'moderate',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Table pour les positions dans les portefeuilles
CREATE TABLE IF NOT EXISTS user_portfolio_positions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  portfolio_id UUID REFERENCES user_portfolios(id) ON DELETE CASCADE NOT NULL,
  symbol TEXT NOT NULL,
  shares DECIMAL(10,4) NOT NULL DEFAULT 0,
  average_price DECIMAL(10,2) NOT NULL DEFAULT 0,
  total_invested DECIMAL(12,2) NOT NULL DEFAULT 0,
  target_allocation_percent DECIMAL(5,2), -- Allocation cible en pourcentage
  added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Index unique pour éviter les doublons
  UNIQUE(portfolio_id, symbol)
);

-- 4. Table pour les recommandations personnalisées
CREATE TABLE IF NOT EXISTS user_recommendations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  symbol TEXT NOT NULL,
  recommendation_type TEXT CHECK (recommendation_type IN ('buy', 'sell', 'hold', 'watch')) NOT NULL,
  reason TEXT NOT NULL,
  confidence_score DECIMAL(3,2) CHECK (confidence_score >= 0 AND confidence_score <= 1),
  target_price DECIMAL(10,2),
  target_allocation_percent DECIMAL(5,2),
  is_sharia_compliant BOOLEAN DEFAULT true,
  expires_at TIMESTAMP WITH TIME ZONE,
  is_read BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Index pour éviter les recommandations en double récentes
  UNIQUE(user_id, symbol, recommendation_type, created_at::date)
);

-- 5. Table pour l'historique des transactions (simulation)
CREATE TABLE IF NOT EXISTS user_transactions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  portfolio_id UUID REFERENCES user_portfolios(id) ON DELETE CASCADE,
  symbol TEXT NOT NULL,
  transaction_type TEXT CHECK (transaction_type IN ('buy', 'sell', 'dividend')) NOT NULL,
  shares DECIMAL(10,4) NOT NULL,
  price_per_share DECIMAL(10,2) NOT NULL,
  total_amount DECIMAL(12,2) NOT NULL,
  fees DECIMAL(8,2) DEFAULT 0,
  notes TEXT,
  transaction_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Table pour les alertes utilisateur
CREATE TABLE IF NOT EXISTS user_alerts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  symbol TEXT NOT NULL,
  alert_type TEXT CHECK (alert_type IN ('price_above', 'price_below', 'sharia_change', 'recommendation')) NOT NULL,
  target_value DECIMAL(10,2),
  is_active BOOLEAN DEFAULT true,
  is_triggered BOOLEAN DEFAULT false,
  triggered_at TIMESTAMP WITH TIME ZONE,
  message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. Index pour optimiser les performances
CREATE INDEX IF NOT EXISTS idx_user_watchlists_user_id ON user_watchlists(user_id);
CREATE INDEX IF NOT EXISTS idx_user_watchlists_symbol ON user_watchlists(symbol);
CREATE INDEX IF NOT EXISTS idx_user_watchlists_added_at ON user_watchlists(added_at);

CREATE INDEX IF NOT EXISTS idx_user_portfolios_user_id ON user_portfolios(user_id);
CREATE INDEX IF NOT EXISTS idx_user_portfolios_is_active ON user_portfolios(is_active);

CREATE INDEX IF NOT EXISTS idx_user_portfolio_positions_portfolio_id ON user_portfolio_positions(portfolio_id);
CREATE INDEX IF NOT EXISTS idx_user_portfolio_positions_symbol ON user_portfolio_positions(symbol);

CREATE INDEX IF NOT EXISTS idx_user_recommendations_user_id ON user_recommendations(user_id);
CREATE INDEX IF NOT EXISTS idx_user_recommendations_symbol ON user_recommendations(symbol);
CREATE INDEX IF NOT EXISTS idx_user_recommendations_created_at ON user_recommendations(created_at);
CREATE INDEX IF NOT EXISTS idx_user_recommendations_is_read ON user_recommendations(is_read);

CREATE INDEX IF NOT EXISTS idx_user_transactions_user_id ON user_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_transactions_portfolio_id ON user_transactions(portfolio_id);
CREATE INDEX IF NOT EXISTS idx_user_transactions_symbol ON user_transactions(symbol);
CREATE INDEX IF NOT EXISTS idx_user_transactions_date ON user_transactions(transaction_date);

CREATE INDEX IF NOT EXISTS idx_user_alerts_user_id ON user_alerts(user_id);
CREATE INDEX IF NOT EXISTS idx_user_alerts_symbol ON user_alerts(symbol);
CREATE INDEX IF NOT EXISTS idx_user_alerts_is_active ON user_alerts(is_active);

-- 8. Triggers pour mettre à jour updated_at automatiquement
CREATE TRIGGER update_user_portfolios_updated_at 
  BEFORE UPDATE ON user_portfolios
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_portfolio_positions_updated_at 
  BEFORE UPDATE ON user_portfolio_positions
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 9. Fonction pour calculer la valeur actuelle d'un portefeuille
CREATE OR REPLACE FUNCTION calculate_portfolio_value(p_portfolio_id UUID)
RETURNS TABLE(
  total_value DECIMAL,
  total_invested DECIMAL,
  total_gain_loss DECIMAL,
  gain_loss_percent DECIMAL
) AS $$
DECLARE
  position RECORD;
  current_price DECIMAL;
  position_value DECIMAL;
  v_total_value DECIMAL := 0;
  v_total_invested DECIMAL := 0;
BEGIN
  -- Parcourir toutes les positions du portefeuille
  FOR position IN 
    SELECT symbol, shares, average_price, total_invested
    FROM user_portfolio_positions 
    WHERE portfolio_id = p_portfolio_id
  LOOP
    -- Récupérer le prix actuel depuis la table stocks
    SELECT COALESCE(current_price, 0) INTO current_price
    FROM stocks 
    WHERE symbol = position.symbol;
    
    -- Calculer la valeur de la position
    position_value := position.shares * COALESCE(current_price, position.average_price);
    v_total_value := v_total_value + position_value;
    v_total_invested := v_total_invested + position.total_invested;
  END LOOP;
  
  RETURN QUERY SELECT 
    v_total_value as total_value,
    v_total_invested as total_invested,
    (v_total_value - v_total_invested) as total_gain_loss,
    CASE 
      WHEN v_total_invested > 0 THEN ((v_total_value - v_total_invested) / v_total_invested) * 100
      ELSE 0 
    END as gain_loss_percent;
END;
$$ LANGUAGE plpgsql;

-- 10. Fonction pour générer des recommandations basées sur le profil utilisateur
CREATE OR REPLACE FUNCTION generate_user_recommendations(p_user_id UUID)
RETURNS INTEGER AS $$
DECLARE
  user_profile RECORD;
  stock RECORD;
  recommendation_count INTEGER := 0;
  target_allocation DECIMAL;
  recommendation_reason TEXT;
BEGIN
  -- Récupérer le profil utilisateur
  SELECT * INTO user_profile FROM profiles WHERE id = p_user_id;
  
  IF NOT FOUND THEN
    RETURN 0;
  END IF;
  
  -- Supprimer les anciennes recommandations (plus de 7 jours)
  DELETE FROM user_recommendations 
  WHERE user_id = p_user_id 
    AND created_at < NOW() - INTERVAL '7 days';
  
  -- Générer des recommandations basées sur les actions Sharia compliant
  FOR stock IN 
    SELECT s.*, sfs.total_debt, sfs.total_revenue, sfs.return_on_equity
    FROM stocks_with_realtime_data s
    LEFT JOIN stock_financial_stats sfs ON s.symbol = sfs.symbol
    WHERE s.is_sharia_compliant = true
      AND s.current_price IS NOT NULL
      AND s.symbol NOT IN (
        -- Exclure les actions déjà en portefeuille
        SELECT DISTINCT upp.symbol 
        FROM user_portfolio_positions upp
        JOIN user_portfolios up ON upp.portfolio_id = up.id
        WHERE up.user_id = p_user_id AND up.is_active = true
      )
    ORDER BY 
      CASE WHEN s.sharia_king_since IS NOT NULL THEN 1 ELSE 2 END,
      s.market_cap DESC NULLS LAST
    LIMIT 20
  LOOP
    -- Déterminer l'allocation cible basée sur le profil de risque
    target_allocation := CASE user_profile.risk_tolerance
      WHEN 'conservative' THEN 
        CASE WHEN stock.sharia_king_since IS NOT NULL THEN 8.0 ELSE 3.0 END
      WHEN 'moderate' THEN 
        CASE WHEN stock.sharia_king_since IS NOT NULL THEN 6.0 ELSE 4.0 END
      WHEN 'aggressive' THEN 
        CASE WHEN stock.sharia_king_since IS NOT NULL THEN 5.0 ELSE 5.0 END
      ELSE 5.0
    END;
    
    -- Générer la raison de la recommandation
    recommendation_reason := CASE 
      WHEN stock.sharia_king_since IS NOT NULL THEN 
        'Sharia King avec historique de conformité depuis ' || stock.sharia_king_since
      WHEN stock.return_on_equity > 15 THEN 
        'Excellent retour sur capitaux propres (' || ROUND(stock.return_on_equity, 1) || '%)'
      WHEN stock.market_cap > 100000000000 THEN 
        'Grande capitalisation stable et conforme Sharia'
      ELSE 
        'Action conforme Sharia avec bon potentiel'
    END;
    
    -- Insérer la recommandation
    INSERT INTO user_recommendations (
      user_id, symbol, recommendation_type, reason, 
      confidence_score, target_allocation_percent, is_sharia_compliant
    ) VALUES (
      p_user_id, stock.symbol, 'buy', recommendation_reason,
      CASE 
        WHEN stock.sharia_king_since IS NOT NULL THEN 0.9
        WHEN stock.return_on_equity > 15 THEN 0.8
        WHEN stock.market_cap > 100000000000 THEN 0.7
        ELSE 0.6
      END,
      target_allocation, true
    ) ON CONFLICT (user_id, symbol, recommendation_type, created_at::date) DO NOTHING;
    
    recommendation_count := recommendation_count + 1;
  END LOOP;
  
  RETURN recommendation_count;
END;
$$ LANGUAGE plpgsql;

-- 11. Vue pour les statistiques de portefeuille utilisateur
CREATE OR REPLACE VIEW user_portfolio_summary AS
SELECT 
  up.id as portfolio_id,
  up.user_id,
  up.name as portfolio_name,
  up.total_invested as invested_amount,
  COUNT(upp.symbol) as positions_count,
  SUM(upp.shares * COALESCE(s.current_price, upp.average_price)) as current_value,
  SUM(upp.total_invested) as total_cost,
  SUM(upp.shares * COALESCE(s.current_price, upp.average_price)) - SUM(upp.total_invested) as unrealized_gain_loss,
  CASE 
    WHEN SUM(upp.total_invested) > 0 THEN 
      ((SUM(upp.shares * COALESCE(s.current_price, upp.average_price)) - SUM(upp.total_invested)) / SUM(upp.total_invested)) * 100
    ELSE 0 
  END as gain_loss_percent,
  COUNT(CASE WHEN s.is_sharia_compliant = true THEN 1 END) as sharia_compliant_count,
  COUNT(CASE WHEN s.sharia_king_since IS NOT NULL THEN 1 END) as sharia_kings_count,
  up.created_at,
  up.updated_at
FROM user_portfolios up
LEFT JOIN user_portfolio_positions upp ON up.id = upp.portfolio_id
LEFT JOIN stocks s ON upp.symbol = s.symbol
WHERE up.is_active = true
GROUP BY up.id, up.user_id, up.name, up.total_invested, up.created_at, up.updated_at;

-- 12. Politiques RLS pour la sécurité
ALTER TABLE user_watchlists ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_portfolios ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_portfolio_positions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_recommendations ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_alerts ENABLE ROW LEVEL SECURITY;

-- Politiques pour user_watchlists
CREATE POLICY "Users can manage their own watchlists" ON user_watchlists
  FOR ALL USING (auth.uid() = user_id);

-- Politiques pour user_portfolios
CREATE POLICY "Users can manage their own portfolios" ON user_portfolios
  FOR ALL USING (auth.uid() = user_id);

-- Politiques pour user_portfolio_positions
CREATE POLICY "Users can manage their own portfolio positions" ON user_portfolio_positions
  FOR ALL USING (
    portfolio_id IN (
      SELECT id FROM user_portfolios WHERE user_id = auth.uid()
    )
  );

-- Politiques pour user_recommendations
CREATE POLICY "Users can view their own recommendations" ON user_recommendations
  FOR ALL USING (auth.uid() = user_id);

-- Politiques pour user_transactions
CREATE POLICY "Users can manage their own transactions" ON user_transactions
  FOR ALL USING (auth.uid() = user_id);

-- Politiques pour user_alerts
CREATE POLICY "Users can manage their own alerts" ON user_alerts
  FOR ALL USING (auth.uid() = user_id);

-- 13. Commentaires pour la documentation
COMMENT ON TABLE user_watchlists IS 'Liste de surveillance des actions par utilisateur';
COMMENT ON TABLE user_portfolios IS 'Portefeuilles personnalisés des utilisateurs';
COMMENT ON TABLE user_portfolio_positions IS 'Positions dans les portefeuilles utilisateur';
COMMENT ON TABLE user_recommendations IS 'Recommandations personnalisées basées sur le profil utilisateur';
COMMENT ON TABLE user_transactions IS 'Historique des transactions simulées';
COMMENT ON TABLE user_alerts IS 'Alertes personnalisées pour les utilisateurs';
COMMENT ON VIEW user_portfolio_summary IS 'Vue résumée des statistiques de portefeuille';
COMMENT ON FUNCTION calculate_portfolio_value IS 'Calcule la valeur actuelle et les gains/pertes d\'un portefeuille';
COMMENT ON FUNCTION generate_user_recommendations IS 'Génère des recommandations personnalisées basées sur le profil utilisateur';
