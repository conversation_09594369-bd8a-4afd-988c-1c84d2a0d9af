-- Script de test simple pour vérifier l'accès aux tables
-- À exécuter dans Supabase Dashboard → SQL Editor

-- 1. Test d'accès basique à la table profiles
SELECT 'Test 1: Accès à la table profiles' as test;
SELECT COUNT(*) as nombre_profils FROM profiles;

-- 2. Test de lecture du profil spécifique
SELECT 'Test 2: Lecture du profil utilisateur' as test;
SELECT * FROM profiles WHERE id = '03ef09c2-dc7f-4c10-b247-97ce2c825a87';

-- 3. Vérifier les permissions sur la table
SELECT 'Test 3: Permissions sur la table' as test;
SELECT grantee, privilege_type 
FROM information_schema.role_table_grants 
WHERE table_name = 'profiles' AND table_schema = 'public';

-- 4. Vérifier l'état de RLS
SELECT 'Test 4: État de RLS' as test;
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename = 'profiles';

-- 5. Lister les politiques RLS actives
SELECT 'Test 5: Politiques RLS actives' as test;
SELECT policyname, cmd, permissive, roles, qual
FROM pg_policies 
WHERE tablename = 'profiles';

-- 6. Test d'insertion (sera annulé)
SELECT 'Test 6: Test d\'insertion' as test;
BEGIN;
INSERT INTO profiles (id, email, full_name) 
VALUES ('test-id-123', '<EMAIL>', 'Test User');
SELECT * FROM profiles WHERE id = 'test-id-123';
ROLLBACK; -- Annuler l'insertion

-- 7. Vérifier la fonction auth.uid()
SELECT 'Test 7: Fonction auth.uid()' as test;
SELECT auth.uid() as current_user_id;

-- 8. Vérifier la fonction auth.role()
SELECT 'Test 8: Fonction auth.role()' as test;
SELECT auth.role() as current_role;
