-- Mise à jour du schéma de base de données pour intégrer Yahoo Finance
-- À exécuter dans l'éditeur SQL de Supabase

-- 1. Étendre la table stocks avec les données en temps réel
ALTER TABLE stocks ADD COLUMN IF NOT EXISTS current_price DECIMAL(10,2);
ALTER TABLE stocks ADD COLUMN IF NOT EXISTS price_change DECIMAL(10,2);
ALTER TABLE stocks ADD COLUMN IF NOT EXISTS price_change_percent DECIMAL(5,2);
ALTER TABLE stocks ADD COLUMN IF NOT EXISTS volume BIGINT;
ALTER TABLE stocks ADD COLUMN IF NOT EXISTS market_cap BIGINT;
ALTER TABLE stocks ADD COLUMN IF NOT EXISTS trailing_pe DECIMAL(8,2);
ALTER TABLE stocks ADD COLUMN IF NOT EXISTS forward_pe DECIMAL(8,2);
ALTER TABLE stocks ADD COLUMN IF NOT EXISTS dividend_yield DECIMAL(5,2);
ALTER TABLE stocks ADD COLUMN IF NOT EXISTS fifty_two_week_low DECIMAL(10,2);
ALTER TABLE stocks ADD COLUMN IF NOT EXISTS fifty_two_week_high DECIMAL(10,2);
ALTER TABLE stocks ADD COLUMN IF NOT EXISTS currency TEXT DEFAULT 'USD';
ALTER TABLE stocks ADD COLUMN IF NOT EXISTS exchange_name TEXT;
ALTER TABLE stocks ADD COLUMN IF NOT EXISTS long_name TEXT;
ALTER TABLE stocks ADD COLUMN IF NOT EXISTS short_name TEXT;
ALTER TABLE stocks ADD COLUMN IF NOT EXISTS industry TEXT;
ALTER TABLE stocks ADD COLUMN IF NOT EXISTS price_last_updated TIMESTAMP WITH TIME ZONE;

-- 2. Créer une table pour l'historique des prix
CREATE TABLE IF NOT EXISTS stock_price_history (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  symbol TEXT NOT NULL,
  date DATE NOT NULL,
  open_price DECIMAL(10,2) NOT NULL,
  high_price DECIMAL(10,2) NOT NULL,
  low_price DECIMAL(10,2) NOT NULL,
  close_price DECIMAL(10,2) NOT NULL,
  adj_close_price DECIMAL(10,2) NOT NULL,
  volume BIGINT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Index unique pour éviter les doublons
  UNIQUE(symbol, date)
);

-- 3. Créer une table pour les statistiques financières détaillées
CREATE TABLE IF NOT EXISTS stock_financial_stats (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  symbol TEXT NOT NULL UNIQUE,
  total_debt BIGINT,
  total_cash BIGINT,
  total_revenue BIGINT,
  gross_profits BIGINT,
  operating_cashflow BIGINT,
  free_cashflow BIGINT,
  return_on_equity DECIMAL(5,2),
  return_on_assets DECIMAL(5,2),
  debt_to_equity DECIMAL(8,2),
  current_ratio DECIMAL(8,2),
  quick_ratio DECIMAL(8,2),
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Créer une table pour le cache des données API
CREATE TABLE IF NOT EXISTS api_cache (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  cache_key TEXT NOT NULL UNIQUE,
  data JSONB NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Index pour optimiser les performances
CREATE INDEX IF NOT EXISTS idx_stocks_symbol ON stocks(symbol);
CREATE INDEX IF NOT EXISTS idx_stocks_sector ON stocks(sector);
CREATE INDEX IF NOT EXISTS idx_stocks_is_sharia_compliant ON stocks(is_sharia_compliant);
CREATE INDEX IF NOT EXISTS idx_stocks_price_last_updated ON stocks(price_last_updated);

CREATE INDEX IF NOT EXISTS idx_stock_price_history_symbol ON stock_price_history(symbol);
CREATE INDEX IF NOT EXISTS idx_stock_price_history_date ON stock_price_history(date);
CREATE INDEX IF NOT EXISTS idx_stock_price_history_symbol_date ON stock_price_history(symbol, date);

CREATE INDEX IF NOT EXISTS idx_stock_financial_stats_symbol ON stock_financial_stats(symbol);
CREATE INDEX IF NOT EXISTS idx_stock_financial_stats_last_updated ON stock_financial_stats(last_updated);

CREATE INDEX IF NOT EXISTS idx_api_cache_key ON api_cache(cache_key);
CREATE INDEX IF NOT EXISTS idx_api_cache_expires_at ON api_cache(expires_at);

-- 6. Trigger pour mettre à jour updated_at automatiquement
CREATE TRIGGER update_stock_financial_stats_updated_at 
  BEFORE UPDATE ON stock_financial_stats
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_api_cache_updated_at 
  BEFORE UPDATE ON api_cache
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 7. Fonction pour nettoyer le cache expiré
CREATE OR REPLACE FUNCTION cleanup_expired_cache()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM api_cache WHERE expires_at < NOW();
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 8. Fonction pour calculer les ratios Sharia automatiquement
CREATE OR REPLACE FUNCTION calculate_sharia_ratios(
  p_symbol TEXT,
  p_total_debt BIGINT DEFAULT NULL,
  p_total_revenue BIGINT DEFAULT NULL,
  p_total_cash BIGINT DEFAULT NULL
)
RETURNS TABLE(
  debt_ratio DECIMAL,
  liquidity_ratio DECIMAL,
  is_compliant BOOLEAN
) AS $$
DECLARE
  v_debt_ratio DECIMAL := 0;
  v_liquidity_ratio DECIMAL := 0;
  v_is_compliant BOOLEAN := false;
BEGIN
  -- Récupérer les données si non fournies
  IF p_total_debt IS NULL OR p_total_revenue IS NULL OR p_total_cash IS NULL THEN
    SELECT 
      COALESCE(sfs.total_debt, 0),
      COALESCE(sfs.total_revenue, 0),
      COALESCE(sfs.total_cash, 0)
    INTO p_total_debt, p_total_revenue, p_total_cash
    FROM stock_financial_stats sfs
    WHERE sfs.symbol = p_symbol;
  END IF;

  -- Calculer les ratios
  IF p_total_revenue > 0 THEN
    v_debt_ratio := (p_total_debt::DECIMAL / p_total_revenue::DECIMAL) * 100;
    v_liquidity_ratio := (p_total_cash::DECIMAL / p_total_revenue::DECIMAL) * 100;
  END IF;

  -- Vérifier la conformité Sharia (dette < 33%, liquidité < 33%)
  v_is_compliant := v_debt_ratio <= 33 AND v_liquidity_ratio <= 33;

  RETURN QUERY SELECT 
    ROUND(v_debt_ratio, 2) as debt_ratio,
    ROUND(v_liquidity_ratio, 2) as liquidity_ratio,
    v_is_compliant as is_compliant;
END;
$$ LANGUAGE plpgsql;

-- 9. Vue pour les actions avec données en temps réel et ratios Sharia
CREATE OR REPLACE VIEW stocks_with_realtime_data AS
SELECT 
  s.*,
  sfs.total_debt,
  sfs.total_cash,
  sfs.total_revenue,
  sfs.return_on_equity,
  sfs.debt_to_equity,
  -- Calculer les ratios Sharia
  CASE 
    WHEN sfs.total_revenue > 0 AND sfs.total_debt IS NOT NULL 
    THEN ROUND((sfs.total_debt::DECIMAL / sfs.total_revenue::DECIMAL) * 100, 2)
    ELSE 0 
  END as calculated_debt_ratio,
  CASE 
    WHEN sfs.total_revenue > 0 AND sfs.total_cash IS NOT NULL 
    THEN ROUND((sfs.total_cash::DECIMAL / sfs.total_revenue::DECIMAL) * 100, 2)
    ELSE 0 
  END as calculated_liquidity_ratio,
  -- Vérifier la conformité Sharia
  CASE 
    WHEN sfs.total_revenue > 0 AND sfs.total_debt IS NOT NULL AND sfs.total_cash IS NOT NULL
    THEN (sfs.total_debt::DECIMAL / sfs.total_revenue::DECIMAL) * 100 <= 33 
         AND (sfs.total_cash::DECIMAL / sfs.total_revenue::DECIMAL) * 100 <= 33
    ELSE false 
  END as is_sharia_compliant_calculated
FROM stocks s
LEFT JOIN stock_financial_stats sfs ON s.symbol = sfs.symbol;

-- 10. Politique RLS pour les nouvelles tables
ALTER TABLE stock_price_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE stock_financial_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_cache ENABLE ROW LEVEL SECURITY;

-- Permettre la lecture publique pour les données financières
CREATE POLICY "Allow public read access to stock_price_history" ON stock_price_history
  FOR SELECT USING (true);

CREATE POLICY "Allow public read access to stock_financial_stats" ON stock_financial_stats
  FOR SELECT USING (true);

-- Restreindre l'écriture aux utilisateurs authentifiés
CREATE POLICY "Allow authenticated users to insert stock_price_history" ON stock_price_history
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users to update stock_price_history" ON stock_price_history
  FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users to insert stock_financial_stats" ON stock_financial_stats
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users to update stock_financial_stats" ON stock_financial_stats
  FOR UPDATE USING (auth.role() = 'authenticated');

-- Cache API accessible seulement aux services
CREATE POLICY "Allow service role access to api_cache" ON api_cache
  FOR ALL USING (auth.role() = 'service_role');

-- 11. Commentaires pour la documentation
COMMENT ON TABLE stock_price_history IS 'Historique des prix des actions récupérés via Yahoo Finance';
COMMENT ON TABLE stock_financial_stats IS 'Statistiques financières détaillées des entreprises';
COMMENT ON TABLE api_cache IS 'Cache pour les données des APIs externes';
COMMENT ON VIEW stocks_with_realtime_data IS 'Vue combinant les données des actions avec les informations financières en temps réel';
COMMENT ON FUNCTION calculate_sharia_ratios IS 'Calcule les ratios de conformité Sharia pour une action donnée';
