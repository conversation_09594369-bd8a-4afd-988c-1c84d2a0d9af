-- Script pour ajouter les colonnes d'onboarding à la table profiles existante
-- À exécuter dans Supabase Dashboard → SQL Editor

-- Ajouter les nouvelles colonnes d'onboarding
ALTER TABLE profiles
ADD COLUMN IF NOT EXISTS has_invested_before BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS is_currently_investing BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS current_investment_amount DECIMAL(12,2),
ADD COLUMN IF NOT EXISTS monthly_income DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS monthly_savings DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS investment_goals TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS age INTEGER,
ADD COLUMN IF NOT EXISTS profession TEXT,
ADD COLUMN IF NOT EXISTS profession_custom TEXT,
ADD COLUMN IF NOT EXISTS boycott_choice TEXT DEFAULT 'no_boycott',
ADD COLUMN IF NOT EXISTS onboarding_completed BOOLEAN DEFAULT false;

-- Vérifier que les colonnes ont été ajoutées
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'profiles' 
AND column_name IN (
  'has_invested_before',
  'is_currently_investing',
  'current_investment_amount',
  'monthly_income',
  'monthly_savings',
  'investment_goals',
  'age',
  'profession',
  'profession_custom',
  'boycott_choice',
  'onboarding_completed'
)
ORDER BY column_name;

-- Mettre à jour les profils existants pour marquer l'onboarding comme non complété
UPDATE profiles 
SET onboarding_completed = false 
WHERE onboarding_completed IS NULL;

SELECT 'Colonnes d''onboarding ajoutees avec succes!' as result;
