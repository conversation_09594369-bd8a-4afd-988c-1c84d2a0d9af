-- Script de réinitialisation complète de la table profiles
-- ⚠️ ATTENTION: Ce script supprime et recrée la table profiles
-- À utiliser seulement si les autres solutions ne fonctionnent pas

-- 1. Supprimer la table existante (ATTENTION: perte de données)
DROP TABLE IF EXISTS profiles CASCADE;

-- 2. Re<PERSON><PERSON>er la table profiles
CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT NOT NULL,
  full_name TEXT,
  risk_tolerance TEXT CHECK (risk_tolerance IN ('conservative', 'moderate', 'aggressive')) DEFAULT 'moderate',
  investment_horizon TEXT CHECK (investment_horizon IN ('short', 'medium', 'long')) DEFAULT 'medium',
  monthly_budget DECIMAL(10,2) DEFAULT 1000,
  sharia_purity_level INTEGER CHECK (sharia_purity_level >= 95 AND sharia_purity_level <= 100) DEFAULT 98,
  boycott_preferences TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Accorder toutes les permissions (sans RLS pour commencer)
GRANT ALL ON profiles TO authenticated;
GRANT ALL ON profiles TO anon;
GRANT ALL ON profiles TO postgres;

-- 4. Créer la fonction de mise à jour automatique
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- 5. Créer le trigger pour updated_at
CREATE TRIGGER update_profiles_updated_at 
  BEFORE UPDATE ON profiles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 6. Créer la fonction pour les nouveaux utilisateurs
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name)
  VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name')
  ON CONFLICT (id) DO NOTHING;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Créer le trigger pour les nouveaux utilisateurs
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 8. Créer le profil pour l'utilisateur actuel
INSERT INTO profiles (id, email, full_name, risk_tolerance, investment_horizon, monthly_budget, sharia_purity_level, boycott_preferences)
VALUES ('03ef09c2-dc7f-4c10-b247-97ce2c825a87', '<EMAIL>', 'Votre Nom', 'moderate', 'medium', 1000, 98, '{}')
ON CONFLICT (id) DO UPDATE SET
  email = EXCLUDED.email,
  full_name = EXCLUDED.full_name,
  updated_at = NOW();

-- 9. Vérifier que tout fonctionne
SELECT 'Profil créé:' as status, * FROM profiles WHERE id = '03ef09c2-dc7f-4c10-b247-97ce2c825a87';

-- 10. OPTIONNEL: Activer RLS plus tard si nécessaire
-- ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
-- CREATE POLICY "Allow all for authenticated users" ON profiles USING (true);

SELECT 'Table profiles réinitialisée avec succès!' as result;
